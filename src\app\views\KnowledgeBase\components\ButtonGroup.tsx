import { anyValueProps } from "@/types/common";
import { buttonGroupProps } from "./common";
import { withPermission } from "@/components/HocButton";
import PopoverDelete from "@/components/Popover";

interface Props {
  data: buttonGroupProps[];
  checkedList: anyValueProps[];
  setCheckedList: (value: any[]) => void;
  // setPopover?: (value: HTMLButtonElement | null) => void;
  downLoadSelect?: () => void;
  paperText?: string;
  deleteAction?: (value: any[]) => void;
}
const Root = styled("div")(() => ({
  display: "flex",
}));

const StyleButton = styled(Button, {
  shouldForwardProp: (props) => props !== "buttonType",
})<{ buttonType: string }>(({ buttonType }) => ({
  width: 124,
  marginRight: "15px",
  borderRadius: 22,
  background:
    buttonType !== "selectAll"
      ? "#fff"
      : "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
  border: "1px solid rgba(235, 235, 235, 1)",
  color: buttonType === "selectAll" ? "#fff" : "rgba(64, 64, 64, 1)",
  boxShadow: "none",
}));

const PermissionStyleButton = ({
  item,
  action,
  data,
  paperText,
}: {
  item: any;
  action: any;
  data: any;
  paperText: string;
}) => {
  const ButtonItem: React.FC<any> = ({ action, item, data, paperText }) => {
    const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
    const [deleteItem, setDeleteItem] = useState<any>(null);
    const cardDelete = (event: any) => {
      event.stopPropagation();
      const deleteIds = data.filter((item: any) => item.checked);
      // .map((item: any) => item.id);
      if (deleteIds.length === 0) {
        message.warning(`请选择要删除的${paperText}`);
        return;
      }
      setAnchorEl(event.currentTarget);
      setDeleteItem(deleteIds);
    };

    const handleConfirm = () => {
      action("deleteSelectAll", deleteItem);
      // setAnchorEl(null);
    };
    return (
      <>
        {item.keyword === "deleteSelectAll" ? (
          <>
            <StyleButton
              variant="contained"
              onClick={(e) => cardDelete(e)}
              buttonType={item.keyword}
            >
              {item.name}
            </StyleButton>
            {!!anchorEl && (
              <PopoverDelete
                title={`确定批量删除${paperText}吗?`}
                anchorEl={anchorEl}
                handleClose={() => setAnchorEl(null)}
                handleConfirm={handleConfirm}
                horizontal={"right"}
              />
            )}
          </>
        ) : (
          <StyleButton
            variant="contained"
            onClick={() => action(item.keyword)}
            buttonType={item.keyword}
          >
            {item.name}
          </StyleButton>
        )}
      </>
    );
  };
  const PermissionButton = withPermission(ButtonItem, item.role);
  return (
    <PermissionButton
      item={item}
      action={action}
      data={data}
      paperText={paperText}
    />
  );
};

const Index: React.FC<Props> = ({
  data,
  checkedList,
  setCheckedList,
  // setPopover,
  downLoadSelect,
  paperText,
  deleteAction,
}) => {
  // 全选
  const handleSelectAll = () => {
    const updatedCheckboxes = checkedList.map((checkbox) => ({
      ...checkbox,
      checked: true,
    }));
    setCheckedList(updatedCheckboxes);
  };
  // 反向选择
  const handleReverseSelectAll = () => {
    const updatedCheckboxes = checkedList.map((checkbox) => ({
      ...checkbox,
      checked: !checkbox.checked,
    }));
    setCheckedList(updatedCheckboxes);
  };
  // 清除全选
  const clearSelectAll = () => {
    const updatedCheckboxes = checkedList.map((checkbox) => ({
      ...checkbox,
      checked: false,
    }));
    setCheckedList(updatedCheckboxes);
  };
  // 删除所选
  // const deleteSelectAll = (
  //   event: React.MouseEvent<HTMLButtonElement, MouseEvent>,
  // ) => {
  //   const allUnchecked = checkedList.every((item) => item.checked === false);
  //   window.console.log(checkedList);
  //   if (allUnchecked) {
  //     message.warning(`请选择要删除的${paperText}`);
  //     return;
  //   }
  //   setPopover && setPopover(event.currentTarget);
  // };
  const handleClick = (
    // e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    item: string,
    data?: any,
  ) => {
    switch (item) {
      case "selectAll":
        handleSelectAll();
        break;
      case "reverseSelectAll":
        handleReverseSelectAll();
        break;
      case "clearSelectAll":
        clearSelectAll();
        break;
      case "deleteSelectAll":
        deleteAction && deleteAction(data);
        break;
      case "downloadSelectAll": {
        downLoadSelect && downLoadSelect();
        break;
      }
      default:
        break;
    }
  };
  return (
    <Root>
      {data &&
        data.map((item, index) => (
          <PermissionStyleButton
            key={index}
            action={handleClick}
            item={item}
            data={checkedList}
            paperText={paperText || ""}
          />
        ))}
    </Root>
  );
};
export default Index;
