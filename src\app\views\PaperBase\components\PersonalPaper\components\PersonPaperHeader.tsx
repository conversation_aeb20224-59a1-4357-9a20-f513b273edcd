import React from "react";
import { styled } from "@mui/material";
import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { searchColumns, taskSearchColumns } from "../setting";
import { SearchParamProp } from "..";
import UploadDialog from "./PersonalPaperDialog/UploadDialog";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import { useNavigate } from "react-router-dom";
import { setActives, setSelectedBank } from "@/store/counterSlice";
import { anyValueProps } from "@/types/common";
import { withPermission } from "@/components/HocButton";
import { PERMISSION_MENU } from "@/utils/permission";
import { getNewChatId } from "@/api/chat";
import { batchRetry } from "@/api/personalpaper";

const HEIGHT = 40;

const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
  alignItems: "center",
}));

const FormBox = styled("div")(() => ({
  height: "100%",
  display: "flex",
  alignItems: "center",
  borderRadius: 16,
  background: "#fff",
  flex: 1,
}));

const ButtonGroup = styled("div")(() => ({
  width: "100%",
  height: HEIGHT,
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
}));

const ButtonBox = styled("div")(() => ({
  height: "100%",
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
  borderRadius: 16,
  background: "#fff",
  boxSizing: "border-box",
  marginLeft: 20,
  padding: "0 13px 0 19px",
}));

interface ButtonProps {
  mycolor: string;
  width?: number;
}

const StyleButton = styled(Button, {
  shouldForwardProp: (props) => props !== "ButtonProps",
})<ButtonProps>(({ mycolor, width }) => ({
  width: width || 60,
  height: 32,
  background:
    mycolor === "ai"
      ? "linear-gradient(90deg, rgba(110, 84, 227, 1) 0%, rgba(27, 130, 227, 1) 100%)"
      : mycolor === "primary"
        ? "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)"
        : "rgba(242, 242, 242, 1)",
  color: mycolor == "gary" ? "#000" : "#fff",
  borderRadius: 28,
  cursor: "pointer",
  ":disabled": {
    cursor: " not-allowed",
    pointerEvents: "auto",
    background: "#ccc",
  },
}));

interface PersonHeaderProps {
  loadData?: () => void;
  taskId?: string;
  searchParam: SearchParamProp;
  setSearchParam: (param: SearchParamProp) => void;
  paperBaseId?: string;
  paperBaseName?: string;
  tableData: anyValueProps[];
  isShare: boolean;
  groupCode?: string;
  groupName?: string;
}

const UploadButton = ({ action }: { action: any }) => {
  const StyleUploadButton: React.FC<any> = ({ action }) => (
    <StyleButton mycolor="primary" onClick={action}>
      上传
    </StyleButton>
  );

  const PermissionButton = withPermission(
    StyleUploadButton,
    PERMISSION_MENU["edit"],
  );
  return <PermissionButton action={action} />;
};

const RetryButton = ({
  action,
  data,
}: {
  action: any;
  data: anyValueProps[];
}) => {
  const StyleUploadButton: React.FC<any> = ({ action, data }) => (
    <StyleButton
      width={80}
      sx={{ mr: "10px" }}
      mycolor="primary"
      onClick={action}
      disabled={!data.some((item: any) => item.checked)}
    >
      重新解析
    </StyleButton>
  );

  const PermissionButton = withPermission(
    StyleUploadButton,
    PERMISSION_MENU["edit"],
  );
  return <PermissionButton action={action} data={data} />;
};

const AiChatButton = ({
  action,
  data,
}: {
  action: any;
  data: anyValueProps[];
}) => {
  const StyleAiChatButton: React.FC<any> = ({ action, data }) => (
    <StyleButton
      mycolor="ai"
      width={80}
      sx={{ mr: 1.25 }}
      disabled={!data.some((item: any) => item.checked)}
      onClick={action}
    >
      +AI对话
    </StyleButton>
  );
  const PermissionButton = withPermission(
    StyleAiChatButton,
    PERMISSION_MENU["chat"],
  );
  return <PermissionButton action={action} data={data} />;
};

const PersonPaperHeader: React.FC<PersonHeaderProps> = ({
  loadData,
  taskId,
  searchParam,
  setSearchParam,
  paperBaseId,
  paperBaseName,
  tableData,
  isShare,
  groupCode,
  groupName,
}) => {
  const formRef = useRef<RefProps>(null);
  const dispatch = useAppDispatch();
  const navigator = useNavigate();
  const [uploadDialogOpen, setUploadDialogOpen] = useState<boolean>(false);
  const { listTotal } = useAppSelector((state: RootState) => state.counter);
  const { buttonPermMenus } = useAppSelector((state) => state.route);

  // const taskTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
  // const taskName = `任务${taskTime}`;

  // const onChange = (key: string, value: any) => {
  //   const newSearchParam = { ...searchParam, [key]: value };
  //   setSearchParam(newSearchParam);
  // };

  // const onAddChat = () => {};
  const handelReset = () => {
    setSearchParam({
      ...(paperBaseId ? { source: "0" } : {}),
      status: "1",
    });
  };

  const handleSubmit = () => {
    formRef.current?.submit().then((res) => {
      const newSearchParam = { ...searchParam, ...(res || {}) };
      setSearchParam(newSearchParam);
    });
  };

  const getChatId = async () => {
    const {
      data: { data },
    } = await getNewChatId();
    dispatch(setActives(data));
  };
  const chatPaper = () => {
    const newData = tableData
      .filter((item) => item.checked)
      .map((item) => item.id);
    if (newData.length > 5) {
      message.warning("最多选择5篇资料");
    } else if (listTotal >= 1000) {
      message.warning("聊天窗口已到达1000次,请删除之前创建的聊天窗口");
    } else {
      dispatch(setActives(null));
      dispatch(setSelectedBank({ externalIds: newData, bankType: 2 }));
      navigator("/ai-chat");
      getChatId();
    }
  };

  const handleRetry = async () => {
    const ids = tableData.filter((item) => item.checked).map((item) => item.id);
    try {
      const { data } = await batchRetry(ids);
      if (data.code === 200) {
        loadData && loadData();
      } else {
        message.error("重新解析失败");
      }
    } catch {
      message.error("重新解析失败");
    }
  };

  return (
    <Root>
      <FormBox>
        <Box sx={{ width: "60%", height: HEIGHT, mr: 4, flex: 1 }}>
          <DynamicForm
            ref={formRef}
            columns={!taskId ? searchColumns : taskSearchColumns}
            formData={searchParam}
            // onChange={onChange}
            size="small"
          />
        </Box>
        <Box sx={{ height: HEIGHT, mr: "20px", display: "flex" }}>
          <ButtonGroup>
            <StyleButton mycolor="gary" sx={{ mr: 1.25 }} onClick={handelReset}>
              重置
            </StyleButton>
            <StyleButton mycolor="primary" onClick={handleSubmit}>
              查询
            </StyleButton>
          </ButtonGroup>
        </Box>
      </FormBox>
      {!taskId && (!isShare || buttonPermMenus.includes("CHAT")) && (
        <ButtonBox>
          <AiChatButton action={chatPaper} data={tableData} />
          {!isShare && <RetryButton action={handleRetry} data={tableData} />}
          {!isShare && (
            <UploadButton action={() => setUploadDialogOpen(true)} />
          )}
        </ButtonBox>
      )}
      {
        <UploadDialog
          open={uploadDialogOpen}
          setOpen={setUploadDialogOpen}
          reload={loadData}
          documentId={paperBaseId}
          documentName={paperBaseName}
          uploadGroupCode={groupCode}
          uploadGroupName={groupName}
        />
      }
    </Root>
  );
};

export default PersonPaperHeader;
