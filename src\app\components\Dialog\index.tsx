import React from "react";
import {
  styled,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import LoadingButton from "@mui/lab/LoadingButton";
import {
  DialogProps,
  DialogHeaderProps,
  DialogFooterProps,
  ButtonProps,
} from "./common";
const DialogRoot = styled(Dialog, {
  shouldForwardProp: (props) => props !== "bgColor",
})<{ bgColor: string | undefined }>(({ bgColor }) => ({
  "& .MuiDialogContent-root": {
    padding: 0,
  },
  "& .MuiDialogActions-root": {
    padding: "0 20px",
    borderTop: "dashed 1px rgba(0,0,0,0.1)",
  },
  "& .MuiPaper-root": {
    borderRadius: "20px",
    background: bgColor ? bgColor : "rgba(255, 255, 255, 1)",
    // padding: "0 20px",
    width: bgColor ? "83%" : "",
    height: bgColor ? "88%" : "",
  },
}));

const DialogTitleRoot = styled(DialogTitle, {
  shouldForwardProp: (props) => props !== "hasMyHeader",
})<{ hasMyHeader?: boolean }>(({ theme }) => ({
  margin: 0,
  // padding: hasMyHeader ? 0 : `${theme.spacing(2)} 0`,
  padding: `${theme.spacing(2)} 20px`,
  fontSize: "18px",
  borderBottom: "solid 1px rgba(0,0,0,0.1)",
}));

const DialogTitleMain = styled("div")(() => ({
  width: "100%",
  display: "flex",
  justifyContent: "flex-end",
  alignItems: "center",
  position: "relative",
}));

const TitleContent = styled("div")<{
  center: boolean;
  position: boolean | undefined;
}>(({ center, position }) => ({
  width: "100%",
  textAlign: center ? "center" : "left",
  boxSizing: "border-box",
  position: position ? "static" : "absolute",
  top: 0,
  left: 0,
  color: "rgba(0, 0, 0, 1)",
  fontWeight: 700,
}));

const DialogClose = styled(Button)(() => ({
  minWidth: 0,
  padding: 0,
  color: "#ccc",
  borderRadius: "50%",
  // background: "rgba(217, 217, 217, 1)",
}));

const ActionsButtonGroup = styled("div", {
  shouldForwardProp: (props) => props !== "center" && props !== "buttonHeight",
})<{ center: boolean; buttonHeight: string | undefined }>(
  ({ center, buttonHeight }) => ({
    width: "100%",
    height: buttonHeight ? "53px" : "73px",
    display: "flex",
    justifyContent: center ? "center" : "flex-end",
    gap: 10,
    paddingRight: buttonHeight ? 0 : 16,
  }),
);

const DialogLoadingButton = styled(LoadingButton)(() => ({
  alignSelf: "center",
  height: "32px",
  borderRadius: "24px",
  lineHeight: "32px",
}));

const DialogBody = styled(DialogContent, {
  shouldForwardProp: (props) => props !== "width",
})<{ width: number | undefined | string }>(({ width }) => ({
  width: width ? width : "410px",
  // minHeight: 150,
  paddingLeft: "50px",
  display: "flex",
  alignItems: width ? "stretch" : "center",
}));

const DialogHeader = ({
  title,
  hideCloseButton,
  header,
  onClose,
  center = false,
}: DialogHeaderProps) => (
  <DialogTitleRoot>
    {header || (
      <DialogTitleMain>
        <TitleContent center={center} position={hideCloseButton}>
          {title}
        </TitleContent>
        {!hideCloseButton && (
          <DialogClose aria-label="close" onClick={onClose}>
            <CloseIcon style={{ fontSize: "25px" }} />
          </DialogClose>
        )}
      </DialogTitleMain>
    )}
  </DialogTitleRoot>
);

const DialogFooter = ({
  footer,
  cancel,
  confirm,
  hideCloseButton,
  btnIsCenter = false,
  okButtonProps,
  cancelButtonProps,
  buttonHeight,
}: DialogFooterProps) => {
  const handleAction = (type: "ok" | "cancel") => {
    const buttonProps = type === "ok" ? okButtonProps : cancelButtonProps;
    const confirmAction = type === "ok" ? confirm : cancel;
    const action = type === "ok" ? buttonProps?.onOk : buttonProps?.onCancel;
    action?.();
    if (typeof buttonProps?.loading !== "boolean" && !buttonProps?.onOk) {
      confirmAction();
    }
  };

  const renderButton = (
    type: "ok" | "cancel",
    variant: "outlined" | "contained",
    props?: ButtonProps,
  ) => (
    <DialogLoadingButton
      variant={variant}
      size={props?.size || "small"}
      onClick={() => handleAction(type)}
      loading={props?.loading}
      disabled={props?.disabled}
      sx={{
        background: type === "ok" ? "" : "rgba(255, 255, 255, 1)",
        color: type === "ok" ? "" : "rgba(31, 31, 31, 1)",
        border: type === "ok" ? "none" : "1px solid rgba(235, 235, 235, 1)",
      }}
    >
      {props?.text || (type === "ok" ? "确定" : "取消")}
    </DialogLoadingButton>
  );

  return (
    <DialogActions>
      {footer || (
        <ActionsButtonGroup center={btnIsCenter} buttonHeight={buttonHeight}>
          {!hideCloseButton &&
            renderButton("cancel", "contained", cancelButtonProps)}
          {renderButton("ok", "contained", okButtonProps)}
        </ActionsButtonGroup>
      )}
    </DialogActions>
  );
};

const CustomDialog: React.FC<DialogProps> = ({
  open,
  setDialogOpen,
  title = "",
  center = false,
  hideCloseButton = false,
  hideTitleCloseButton = false,
  children,
  okButtonProps,
  cancelButtonProps,
  width,
  btnIsCenter = false,
  maskClosable = true,
  bgColor,
  buttonHeight,
}) => {
  const header = React.Children.toArray(children).find(
    (child: any) => child.props && child.props.slot === "header",
  );
  const content = React.Children.toArray(children).find(
    (child: any) => child.props && child.props.slot === "content",
  );
  const footer = React.Children.toArray(children).find(
    (child: any) => child.props && child.props.slot === "footer",
  );

  const handleOk = async () => {
    setDialogOpen(false);
  };

  const handleClose = () => {
    setDialogOpen(false);
    cancelButtonProps?.onCancel?.();
  };

  return (
    <DialogRoot
      open={open}
      onClose={maskClosable ? handleClose : undefined}
      maxWidth={false}
      bgColor={bgColor}
    >
      <DialogHeader
        title={title}
        onClose={handleClose}
        header={header}
        hideCloseButton={hideTitleCloseButton}
        center={center}
      />
      <DialogBody width={width}>{content}</DialogBody>
      <DialogFooter
        footer={footer}
        cancel={() => setDialogOpen(false)}
        confirm={handleOk}
        hideCloseButton={hideCloseButton}
        center={center}
        okButtonProps={okButtonProps}
        cancelButtonProps={cancelButtonProps}
        btnIsCenter={btnIsCenter}
        buttonHeight={buttonHeight}
      />
    </DialogRoot>
  );
};
export default CustomDialog;
