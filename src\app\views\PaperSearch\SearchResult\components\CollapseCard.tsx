import React, { useState } from "react";
import {
  styled,
  Collapse,
  IconButton,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Paper,
} from "@mui/material";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ExpandMore from "@mui/icons-material/ExpandMore";
import { CollapseCardProps } from "..";

const PaperWrapper = styled(Paper)(() => ({
  padding: "16px 20px",
  marginBottom: 18,
  borderRadius: 20,
  background:
    "radial-gradient(36.76% 69.17% at 17.058823529411764% -39.849624060150376%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
}));
const TitleWrapper = styled("div")(() => ({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  height: 18,
  marginBottom: 18,
}));

const StyledFormControlLabel = styled(FormControlLabel)(() => ({
  marginLeft: 0,
  marginBottom: 16,
  "&  .MuiFormControlLabel-label": {
    fontSize: 14,
  },
  ".MuiCheckbox-root": {
    marginRight: 10,
  },
}));

const CollapseCard: React.FC<CollapseCardProps> = ({
  title,
  options,
  grid,
}) => {
  const [open, setOpen] = useState(true);

  const handleChange = () => {};
  return (
    <PaperWrapper elevation={3}>
      <TitleWrapper>
        <Typography variant="subtitle1">{title}</Typography>
        <IconButton onClick={() => setOpen(!open)}>
          {open ? <ExpandLess /> : <ExpandMore />}
        </IconButton>
      </TitleWrapper>
      <Collapse sx={{ p: "4px 0" }} in={open} timeout="auto" unmountOnExit>
        <FormGroup
          sx={{ display: "grid", gridTemplateColumns: `repeat(${grid}, 1fr)` }}
        >
          {options.map((item: any, index: number) => (
            <StyledFormControlLabel
              key={index}
              control={
                <Checkbox
                  // checked
                  name={item.prop}
                  onChange={handleChange}
                  sx={{
                    width: 18,
                    height: 18,
                    "& .MuiSvgIcon-root": { fontSize: 18 },
                  }}
                />
              }
              label={item.label || item}
            />
          ))}
        </FormGroup>
      </Collapse>
    </PaperWrapper>
  );
};

export default CollapseCard;
