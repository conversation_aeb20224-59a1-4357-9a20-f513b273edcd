import LoginBox from "./components/LoginBox";
import { SelectType } from "./setting";
import OnTrialBox from "./components/OnTrialBox";
import ForgetPasswordBox from "./components/ForgetPasswordBox";
import CopyRight from "@/components/Layout/components/CopyRight";
import Background from "@/assets/background.png";
import { getDefaultLogo } from "@/utils/logoUtils";
import LoginImg from "@/assets/login-background.png";

const Root = styled("div")(() => ({
  height: "100%",
  width: "100%",
  // position: "relative",
  boxSizing: "border-box",
  backgroundImage: `url(${Background})`,
  backgroundSize: "cover",
}));

const HeaderBar = styled("div")(() => ({
  width: "100%",
  height: 54,
  background: "rgba(255, 255, 255, 0.3)",
  display: "flex",
  alignItems: "center",
  padding: "0 50px 0 258.5px",
  boxSizing: "border-box",
}));

const ImgBox = styled("div")(() => ({
  width: 800,
  height: 500,
  backgroundImage: `url(${LoginImg})`,
  backgroundSize: "cover",
  backgroundPosition: "center",
  marginLeft: -120,
  marginRight: 100,
}));

const HeaderTitle = styled("div")(() => ({
  fontSize: 16,
  fontWeight: 400,
  color: "rgba(48, 48, 48, 1);",
  display: "flex",
  alignItems: "center",
}));

const LogoImg = styled("div")(() => ({
  width: 32,
  height: 32,
  backgroundImage: `url(${getDefaultLogo()})`,
  backgroundSize: "100% 100%",
  backgroundPosition: "center",
  marginRight: 10,
}));

const Content = styled("div", {
  shouldForwardProp: (props) => props !== "cardType",
})<{ cardType: string }>(({ cardType }) => ({
  width: "100%",
  height: "calc(100% - 84px)",
  boxSizing: "border-box",
  overflow: "auto",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  paddingBottom: cardType === "login" ? 50 : 0,
}));

const Login: React.FC = () => {
  const [cardType, setCardType] = useState<SelectType>("login");

  return (
    <Root>
      <HeaderBar>
        <HeaderTitle>
          <LogoImg />
          智能助手
        </HeaderTitle>
      </HeaderBar>
      <Content cardType={cardType}>
        {cardType === "login" && <ImgBox></ImgBox>}
        {cardType === "login" && <LoginBox setAction={setCardType}></LoginBox>}
        {cardType === "trialuse" && (
          <OnTrialBox setAction={setCardType}></OnTrialBox>
        )}
        {cardType === "forgotpw" && (
          <ForgetPasswordBox setAction={setCardType}></ForgetPasswordBox>
        )}
      </Content>
      <CopyRight />
    </Root>
  );
};
export default Login;
