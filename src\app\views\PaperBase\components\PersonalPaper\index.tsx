import React, { useEffect, useState } from "react";
import { styled, Box } from "@mui/material";
import PdfDrawer from "./components/PdfDrawer";
import PopConfirm from "../../../../components/Popover";
import { downloadFile, getFile } from "@/api/paperSearch";
import EditDialog from "./components/PersonalPaperDialog/EditDialog";
import {
  batchPdfs,
  deletePdf,
  getPersonalPaperList,
  reparsePdf,
} from "@/api/personalpaper";
import { anyValueProps } from "@/types/common";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import Breadcrumb from "@/components/Breadcrumb";
import {
  columns,
  defalutBreadcrumb,
  documentOperations,
  filterButtonGroup,
  shareOperations,
  showCheckButton,
  taskColumns,
  taskOperations,
} from "./setting";
import PaginatedTable, { ActionButtonProps } from "@/components/PaginatedTable";
import PersonPaperHeader from "./components/PersonPaperHeader";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import { useQuery } from "@tanstack/react-query";
import { buttonGroup } from "./setting";
import ButtonGroup from "@/views/KnowledgeBase/components/ButtonGroup";
import PopoverDelete from "@/components/Popover";
import { ColumnProps } from "@/components/CustomTable";
import { useDispatch } from "react-redux";
import { setBreadcrumb } from "@/store/breadcrumbSlice";
import { useCheckPermission } from "@/utils/tableCheck";
import { splitByTriplePipe } from "@/views/PaperSearch/PaperDetail/common";

const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  boxSizing: "border-box",
}));

const RootContent = styled("div")(() => ({
  width: "100%",
  height: "calc(100% - 54px)",
  padding: "18px 41px 10px 41px",
  boxSizing: "border-box",
}));

const OperationBar = styled(Box)(() => ({
  width: "100%",
  height: 64,
  boxSizing: "border-box",
  display: "flex",
  alignItems: "center",
  minWidth: "1600px",
}));

const BackBox = styled("div")(() => ({
  width: 86,
  height: "100%",
  borderRadius: 14,
  background:
    "radial-gradient(70.93% 70.31% at 17.441860465116278% -40.625%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(242, 243, 247, 1)",
  border: "2px solid rgba(255, 255, 255, 1)",
  marginRight: 17,
  boxSizing: "border-box",
  fontSize: 14,
  color: "#000",
  fontWeight: 400,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  cursor: "pointer",
}));

const BackIcon = styled("div")(() => ({
  width: 16,
  height: 16,
  borderRadius: "50%",
  background: "rgba(255, 255, 255, 0.88)",
  marginRight: 8,
  boxSizing: "border-box",
}));

const TableBar = styled(Box)(() => ({
  display: "flex",
  width: "100%",
  height: "calc(100% - 80px)",
  background:
    "radial-gradient(46.19% 59.91% at 17.11229946524064% -40.09433962264151%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  borderRadius: 20,
  marginTop: "20px",
  padding: "10px",
  boxSizing: "border-box",
  boxShadow:
    "0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12)",
}));

const ButtonBox = styled("div", {
  shouldForwardProp: (p) => p !== "buttonLength",
})<{ buttonLength: number }>(({ buttonLength }) => ({
  width: "100%",
  height: "100%",
  display: "flex",
  alignItems: "center",
  justifyContent: buttonLength === 1 ? "center" : "space-evenly",
}));

const StyleButton = styled("div", {
  shouldForwardProp: (p) => p !== "statusCode" && p !== "type",
})<{ statusCode: boolean; type: string }>(({ statusCode, type }) => ({
  color:
    type !== "查看" || (type === "查看" && statusCode)
      ? "rgba(24, 112, 199, 1)"
      : "gray",
  cursor:
    type !== "查看" || (type === "查看" && statusCode)
      ? "pointer"
      : "not-allowed",
}));

interface PaginationProps {
  page: number;
  size: number;
}
export type SearchParamProp = anyValueProps | null;
interface SortInfoProps {
  sort: string;
  asc: boolean;
}
interface Props {
  setCheckedData: (data: any) => void;
  id: number;
}
const PersonalPaper: React.FC<Props> = ({ id }) => {
  const navigator = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const fromPage = params.get("page");
  const fromPageSize = params.get("size");
  const share = params.get("share");
  const groupCode = params.get("resourceCode");
  const { taskId, type, documentId } = useParams();
  const [pagination, setPagination] = useState<PaginationProps>({
    page: 1,
    size: 50,
  });
  const [total, setTotal] = useState<number>(0);
  const [tableData, setTableData] = useState<anyValueProps[]>([]);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [drawerTitle, setDrawerTitle] = useState<string>("");
  const [pdfUrl, setPdfUrl] = useState<string>("");
  const [deletePdfId, setDeletePdfId] = useState<{
    type: string;
    id: number | string;
  }>({
    type: "",
    id: "",
  });
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false);
  const [rowOption, setRowOption] = useState<anyValueProps>();
  const [searchParam, setSearchParam] = useState<SearchParamProp>({
    fuzzyQuery: "",
    source: "0",
    status: "1",
  });
  const [queryParams, setQueryParams] = useState<anyValueProps>({});
  const [sortInfo, setSortInfo] = useState<SortInfoProps>({
    sort: taskId ? "createTime" : "updateTime", // 排序字段
    asc: false, // 正序 | 反序
  });
  // const paperBaseName = localStorage.getItem("paperBaseName");
  const paperBaseName = decodeURIComponent(params.get("paperBaseName") || "");
  const groupName = decodeURIComponent(params.get("groupName") || "");
  const [popover, setPopover] = useState<HTMLButtonElement | null>(null);
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const breadcrumbInfo = useMemo(
    () => ({
      current: taskId
        ? "资料任务详情"
        : paperBaseName
          ? paperBaseName
          : "资料库详细",
      parent: taskId
        ? [
            ...defalutBreadcrumb[type ? type : "MyDocDB"],
            { name: "上传记录", path: `/paper-base/${type}/upload-records` },
          ]
        : defalutBreadcrumb[type ? type : "MyDocDB"],
    }),
    [taskId],
  );

  const delayReload = (tasks: any) => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    const refetchFlag = tasks.some(
      (task: any) =>
        task.parseStatusCode === 0 ||
        task.parseStatusCode === 1000 ||
        task.parseStatusCode === 2000,
    );
    if (refetchFlag) {
      timerRef.current = setTimeout(() => {
        refetch();
        delayReload(data?.data || []);
      }, 5000);
    } else {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    }
  };

  const queryRequest = async () => {
    const httpRequest = getPersonalPaperList;
    window.console.log("queryRequest", queryParams);
    const response = await httpRequest({
      ...pagination,
      ...sortInfo,
      ...queryParams,
      ...(taskId && { externalId: taskId, source: "task" }),
      ...(documentId && !taskId
        ? { externalId: documentId }
        : { documentId: id }),
    });
    return response.data;
  };

  const { data, status, refetch, error } = useQuery({
    queryKey: ["get-person", pagination, sortInfo, queryParams], // 使用唯一键，将参数作为查询键的一部分
    queryFn: queryRequest,
  });

  useEffect(() => {
    switch (status) {
      case "success":
        setTableData(
          data?.data?.map((item: any) => ({
            ...item,
            checked: false,
          })) || [],
        );
        delayReload(data?.data || []);
        setTotal(data?.total || 0);
        break;
      case "error":
        message.error("获取资料列表失败, " + error.message);
        break;
    }
  }, [status, data]);

  const onPageChange = (paginationParams: anyValueProps) => {
    const { page, pageSize: size } = paginationParams;
    setPagination({ page, size });
  };

  const formatRowOptionTitle = (value: string) => {
    if (!value) return { title: value };
    const titleSplit = splitByTriplePipe(value);
    if (titleSplit.length !== 1) {
      return {
        title: titleSplit[1],
        zhTitle: titleSplit[0],
      };
    } else {
      return { title: titleSplit[0] };
    }
  };

  const processAuthors = (
    authorsSplit: string[] | string[][],
    language: string,
  ) => {
    if (authorsSplit.length === 1) {
      const firstItem = authorsSplit[0];
      const isStringArray = Array.isArray(firstItem);
      return {
        authors:
          language === "zh" ? [] : isStringArray ? firstItem : [firstItem],
        zhAuthors: <AUTHORS>
      };
    }
    const secondItem = authorsSplit[1];
    return {
      authors: Array.isArray(secondItem) ? secondItem : [secondItem],
      zhAuthors: <AUTHORS>
        ? authorsSplit[0]
        : [authorsSplit[0]],
    };
  };

  const formatRowOptionAuthors = (value: string, language: string) => {
    if (!value) return { authors: value };
    const authorsSplit = splitByTriplePipe(JSON.parse(value));
    const { authors: processedAuthors, zhAuthors } = processAuthors(
      authorsSplit,
      language,
    );
    console.log(processedAuthors, zhAuthors);

    return {
      authors: processedAuthors.join(","),
      zhAuthors: <AUTHORS>
    };
  };

  const handleAction = (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    key: string,
    row: anyValueProps,
  ) => {
    switch (key) {
      case "查看": {
        if (
          row.parseStatusCode === 3000 ||
          row.parseStatusCode === 4000 ||
          row.parseStatusCode === 5000
        ) {
          const breadcrumb = [
            ...breadcrumbInfo.parent,
            {
              name: breadcrumbInfo.current,
              path:
                location.pathname +
                `?page=${pagination.page}&size=${pagination.size}&share=${share}&resourceCode=${groupCode}`,
            },
          ];
          dispatch(setBreadcrumb(breadcrumb));
          navigator(
            `/paper-base/paper-details/paperbase?id=${row.id}&share=${share}`,
          );
        }
        break;
      }
      case "预览":
        previewPdf(row);
        break;
      case "编辑":
        {
          setEditDialogOpen(true);
          // window.console.log(
          //   "row",
          //   formatRowOptionAuthors(row.authors, row.language),
          // );
          const formatTitle = formatRowOptionTitle(row.title);
          const formatAuthors = formatRowOptionAuthors(
            row.authors,
            row.language,
          );
          const formatRow = {
            ...row,
            ...formatTitle,
            ...formatAuthors,
          };
          window.console.log("formatRow", formatRow);
          setRowOption(formatData(formatRow));
        }
        break;
      case "删除": {
        setAnchorEl(e.currentTarget);
        if (taskId) {
          setDeletePdfId({ type: row.source, id: row.relationId });
        } else {
          setDeletePdfId({ type: row.source, id: row.id });
        }
        break;
      }
      case "下载":
        downLoadPdf(row);
        break;
      case "重试":
        reParsePdfs(row.id);
        break;
    }
  };

  const formatData = (data: anyValueProps) => {
    const formattedData: anyValueProps = {};
    for (const key in data) {
      if (data[key] === null) {
        formattedData[key] = "";
      } else {
        formattedData[key] = data[key];
      }
      // else if (key === "keywords" || key === "authors") {
      //   try {
      //     console.log(data[key]);

      //     formattedData[key] = data[key].join(",");
      //   } catch (e) {
      //     formattedData[key] = data[key];
      //     window.console.log(e);
      //   }
      // }
    }
    return formattedData;
  };

  const downLoadPdf = async (item: anyValueProps) => {
    try {
      const { pdfName, pdfUrl, id } = item;
      const { data } = await downloadFile(pdfUrl + `&pdfId=${id}`);
      const url = window.URL.createObjectURL(new Blob([data]));
      const a = document.createElement("a");
      a.href = url;
      a.download = `${pdfName}`; // 设置文件名
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (e) {
      message.error("下载失败" + e);
    }
  };

  const reParsePdfs = async (id: number) => {
    try {
      const { data } = await reparsePdf({
        id,
      });
      if (data.code === 200) {
        message.success("重试成功");
        refetch?.();
      } else {
        message.error("重试失败");
      }
    } catch (e: any) {
      message.error("重试失败" + e.message);
    }
  };

  // 预览pdf
  const previewPdf = async (rowData: anyValueProps) => {
    try {
      // 先清空当前PDF URL并设置标题
      setPdfUrl("");
      setDrawerTitle(rowData.title);
      setDrawerOpen(true);

      // 加载新的PDF
      const { pdfUrl, id } = rowData;
      const { data } = await getFile(pdfUrl + `&pdfId=${id}`);
      const pdfData = new Blob([data], { type: "application/pdf" });
      const pdfDownloadUrl = window.URL.createObjectURL(pdfData);
      setPdfUrl(pdfDownloadUrl);
    } catch (e) {
      message.error("获取PDF失败" + e);
    }
  };

  const handleConfirm = async () => {
    setAnchorEl(null);
    try {
      const payload = {
        externalId: Number(taskId || documentId),
        type: taskId ? "task" : deletePdfId.type,
        ...(taskId
          ? { relationIds: [deletePdfId.id] }
          : { pdfIds: [deletePdfId.id] }),
      };

      const { data } = await deletePdf(payload);

      if (data.code === 200) {
        message.success("删除成功");
        if (pagination.page > Math.ceil((total - 1) / pagination.size)) {
          setPagination({
            ...pagination,
            page: pagination.page - 1,
          });
        } else {
          refetch?.();
        }
        // refetch?.();
      } else {
        message.error("删除失败");
      }
    } catch (e: any) {
      message.error("删除失败: " + e.message);
    }
  };

  const handlePopoverConfirm = async () => {
    const deleteIds = tableData
      .filter((item) => item.checked)
      .map((item) => item.id);
    const params = {
      pdfIds: deleteIds,
      externalId: Number(documentId),
    };
    try {
      const { data } = await deletePdf(params);
      if (data.code === 200) {
        message.success("删除成功");
        if (
          pagination.page >
          Math.ceil((total - deleteIds.length) / pagination.size)
        ) {
          setPagination({
            ...pagination,
            page: pagination.page - 1,
          });
        } else {
          refetch?.();
        }
        // refetch?.();
      } else {
        message.error("删除失败");
      }
    } catch (e: any) {
      message.error("删除失败: " + e.message);
    }
    setPopover(null);
  };

  const onSortChange = (
    sort: SortInfoProps["sort"],
    asc: SortInfoProps["asc"],
  ) => {
    setSortInfo({ sort, asc });
  };

  const handleBack = () => {
    const index = breadcrumbInfo.parent.length - 1;
    navigator(breadcrumbInfo.parent[index].path);
  };

  const batchDownLoad = async () => {
    const downLoadIds = tableData
      .filter((item) => item.checked)
      .map((item) => ({
        pdfId: item.id,
        bucket: item.bucket,
        path: item.pdfPath,
      }));
    if (downLoadIds.length === 0) {
      message.warning("请选择要下载的资料");
      return;
    }
    const { data } = await batchPdfs(downLoadIds);
    const url = window.URL.createObjectURL(new Blob([data]));
    const a = document.createElement("a");
    a.href = url;
    a.download = `pdfs.zip`; // 设置文件名
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleSearchParams = (value: any) => {
    const { fuzzyQuery, date, status, source } = value;
    if (date && date.length > 0) {
      setQueryParams({
        ...(fuzzyQuery ? { fuzzyQuery } : {}),
        ...(source && source !== "0" ? { source } : {}),
        ...(status.toString() !== "1" ? { parseStatusCode: status } : {}),
        updateStartTime: date[0],
        updateEndTime: date[1],
      });
    } else {
      setQueryParams({
        ...(fuzzyQuery ? { fuzzyQuery } : {}),
        ...(source && source !== "0" ? { source } : {}),
        ...(status.toString() !== "1" ? { parseStatusCode: status } : {}),
      });
    }
    setPagination((prev) => ({ page: 1, size: prev.size }));
  };

  const documentActions = useCheckPermission(documentOperations);
  const shareActions = useCheckPermission(shareOperations);
  const taskActions = useCheckPermission(taskOperations);
  const simpleOperationColumns: ColumnProps = {
    dataKey: "actions",
    label: "操作",
    width: 180,
    align: "center",
    render: (row: anyValueProps) => {
      const columns = taskId
        ? taskActions
        : share === "true"
          ? shareActions
          : documentActions;
      return (
        <ButtonBox buttonLength={columns.length}>
          {columns.map((action: ActionButtonProps) =>
            action.type === "重试" &&
            (row.parseStatusCode === 4000 || row.parseStatusCode === 5000) ? (
              <StyleButton
                key={action.type}
                onClick={(e: any) => handleAction(e, action.type, row)}
                statusCode={showCheckButton.includes(row.parseStatusCode)}
                type={action.type}
              >
                {action.type}
              </StyleButton>
            ) : (
              action.type !== "重试" && (
                <StyleButton
                  key={action.type}
                  onClick={(e: any) => handleAction(e, action.type, row)}
                  statusCode={showCheckButton.includes(row.parseStatusCode)}
                  type={action.type}
                >
                  {action.type}
                </StyleButton>
              )
            ),
          )}
        </ButtonBox>
      );
    },
  };

  useEffect(() => {
    handleSearchParams(searchParam);
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [searchParam]);

  useEffect(() => {
    if (fromPage) {
      setPagination({
        page: Number(fromPage),
        size: Number(fromPageSize),
      });
    }
  }, [fromPage]);

  return (
    <Root>
      <Breadcrumb
        parent={breadcrumbInfo.parent}
        current={breadcrumbInfo.current}
      />
      <RootContent>
        <OperationBar>
          <BackBox onClick={() => handleBack()}>
            <BackIcon>
              <ArrowBackIosNewIcon sx={{ fontSize: 14 }} />
            </BackIcon>
            返回
          </BackBox>
          <PersonPaperHeader
            searchParam={searchParam}
            setSearchParam={setSearchParam}
            taskId={taskId}
            loadData={refetch}
            paperBaseId={documentId || ""}
            paperBaseName={paperBaseName || ""}
            tableData={tableData}
            isShare={share === "true"}
            groupCode={groupCode || ""}
            groupName={groupName || ""}
          />
        </OperationBar>
        <TableBar>
          <PaginatedTable
            total={total}
            page={pagination.page}
            pageSize={pagination.size}
            onChangePage={onPageChange}
            onSortChange={onSortChange}
            rows={tableData}
            headCells={
              taskId
                ? taskColumns.concat(simpleOperationColumns)
                : columns.concat(simpleOperationColumns)
            }
            selection={true}
            buttonGroup={
              !taskId && (
                <ButtonGroup
                  paperText={"资料"}
                  data={filterButtonGroup(buttonGroup, share)}
                  checkedList={tableData}
                  setCheckedList={setTableData}
                  downLoadSelect={batchDownLoad}
                  deleteAction={handlePopoverConfirm}
                />
              )
            }
            setCheckedList={setTableData}
            pageSizeOptions={[50, 100, 150]}
          />
        </TableBar>
        {drawerOpen && (
          <PdfDrawer
            setOpen={setDrawerOpen}
            width={800}
            title={drawerTitle}
            pdfUrl={pdfUrl}
          />
        )}
        {!!anchorEl && (
          <PopConfirm
            title="确定删除该篇资料吗?"
            anchorEl={anchorEl}
            handleClose={() => setAnchorEl(null)}
            handleConfirm={handleConfirm}
          />
        )}
        {!!popover && (
          <PopoverDelete
            title="确定批量删除资料吗?"
            anchorEl={popover}
            handleClose={() => setPopover(null)}
            handleConfirm={handlePopoverConfirm}
            horizontal={"right"}
          />
        )}
        {!!editDialogOpen && (
          <EditDialog
            open={editDialogOpen}
            setOpen={setEditDialogOpen}
            reload={refetch}
            rowOption={rowOption}
          />
        )}
      </RootContent>
    </Root>
  );
};

export default PersonalPaper;
