import CardTable from "@/components/CardTable";
import { buttonGroup, replaceNullWithEmptyString } from "./setting";
import { anyValueProps } from "@/types/common";
import { deletePaperBase, getPaperBase } from "@/api/personalpaper";
import { useQuery } from "@tanstack/react-query";
import { useNavigate, useParams } from "react-router-dom";
import Breadcrumb from "@/components/Breadcrumb";
import ButtonGroup from "../KnowledgeBase/components/ButtonGroup";
import AddPaperBaseDialog from "./components/PersonalPaper/components/PersonalPaperDialog/AddPaperBaseDialog";
import PaperBaseHeader from "./components/PaperBaseHeader";
import { SearchParamProp } from "./components/PersonalPaper";
import Base64 from "base-64";
import { DownloadBaseUrl } from "@/api/config";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import { setActives, setSelectedBank } from "@/store/counterSlice";
import { getDocumentByUid, getNewChatId } from "@/api/chat";
import { checkPermission } from "@/utils/auth";

const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
}));

const Content = styled("div", {
  shouldForwardProp: (p) => p !== "isDialog",
})<{ isDialog: boolean }>(({ isDialog }) => ({
  flex: 1,
  display: "flex",
  padding: isDialog ? 0 : "18px 41px 10px 41px",
}));

const RightContent = styled(Box)(() => ({
  flex: 1,
  boxSizing: "border-box",
  borderRadius: "4px",
  display: "flex",
  flexDirection: "column",
}));

const OperationBar = styled("div")(() => ({
  width: "100%",
  height: 64,
}));

const TableSide = styled("div", {
  shouldForwardProp: (p) => p !== "isDialog",
})<{ isDialog: boolean }>(({ isDialog }) => ({
  flex: "1 0 auto",
  boxSizing: "border-box",
  padding: isDialog ? "10px 0" : "20px 0px 0 0px",
  height: 0,
}));

interface PaginationProps {
  page: number;
  pageSize: number;
}
interface Props {
  isDialog: boolean;
  setCheckedData: (data: any) => void;
}
const PaperBase: React.FC<Props> = ({ isDialog, setCheckedData }) => {
  const { type } = useParams();
  const navigator = useNavigate();
  const dispatch = useAppDispatch();
  const { permMenus } = useAppSelector((state) => state.route);
  const { roleOption } = useAppSelector((state) => state.user);
  const [active, setActive] = useState("MyDocDB");
  const [open, setOpen] = useState<boolean>(false);
  const [tableList, setTableList] = useState<any[]>([]);
  const [pagination, setPagination] = useState<PaginationProps>({
    page: 1,
    pageSize: 12,
  });
  const { listTotal } = useAppSelector((state: RootState) => state.counter);
  const [searchParam, setSearchParam] = useState<SearchParamProp>({
    ...(checkPermission(roleOption) ? { resourceCode: "1" } : {}),
  });
  const [queryParams, setQueryParams] = useState<anyValueProps>({});
  const [total, setTotal] = useState<number>(0);
  // const [popover, setPopover] = useState<HTMLButtonElement | null>(null);
  const [editObject, setEditObject] = useState<anyValueProps>({});
  const [dialogType, setDialogType] = useState("add");

  const initActiveGroup = () => {
    const docdbOptions = permMenus.find((item) => item.menuCode === "DocDB");
    if (docdbOptions.children.length === 0) return;
    const newArray = [...docdbOptions.children].sort(
      (a: any, b: any) => a.id - b.id,
    );
    if (type) {
      setActive(type);
    } else {
      setActive(newArray[0].menuCode);
    }
  };

  const checkIncludeActiveGroup = () => {
    if (!type) return;
    const docdbOptions = permMenus.find((item) => item.menuCode === "DocDB");
    if (docdbOptions.children.length === 0) return;
    const flag = docdbOptions.children.find(
      (item: any) => item.menuCode === type,
    );
    if (!flag) {
      navigator("/401");
    }
  };

  useEffect(() => {
    initActiveGroup();
  }, [permMenus]);

  useEffect(() => {
    checkIncludeActiveGroup();
  }, [type]);

  const handleEdit = (item: anyValueProps) => {
    setDialogType("edit");
    const newItem = replaceNullWithEmptyString(item);
    setEditObject(newItem);
    setOpen(true);
  };

  const handleDelete = async (item: anyValueProps) => {
    const {
      data: { code },
    } = await deletePaperBase([item.id]);
    if (code === 200) {
      refetch();
      return true;
    } else {
      return false;
    }
  };

  const getChatId = async () => {
    const {
      data: { data },
    } = await getNewChatId();
    dispatch(setActives(data));
  };
  const handleChat = (item: anyValueProps) => {
    if (listTotal >= 1000) {
      message.warning("聊天窗口已到达1000次,请删除之前创建的聊天窗口");
    } else {
      dispatch(setActives(null));
      dispatch(
        setSelectedBank({
          data: item,
          bankType: 1,
          name: item.name,
          ids: [item.id],
        }),
      );
      navigator(`/ai-chat`);
      getChatId();
    }
  };

  const handleTransfer = (item: anyValueProps) => {
    window.console.log(item);
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>, id: number) => {
    const { checked } = e.target;
    const updatedCheckboxes = tableList.map((item) => ({
      ...item,
      checked: item.id === id ? checked : item.checked,
    }));
    const newData = updatedCheckboxes.filter((item) => item.checked) || null;
    if (setCheckedData) setCheckedData(newData);
    setTableList(updatedCheckboxes);
  };

  const onPageChange = (paginationParams: anyValueProps) => {
    const { page, pageSize } = paginationParams;
    setPagination({ page, pageSize });
  };

  const handleClickCard = (data: anyValueProps) => {
    if (isDialog) {
      const updatedCheckboxes = tableList.map((item) => ({
        ...item,
        checked: item.id === data.id ? !data.checked : false,
      }));
      const newData = updatedCheckboxes.find((item) => item.checked) || null;
      setCheckedData(newData);
      setTableList(updatedCheckboxes);
      return;
    }
    // 构建URL参数对象
    const urlParams = {
      share: data.documentSourceType === "share",
      resourceCode: data.resourceCode,
      paperBaseName: encodeURIComponent(data.name),
      groupName: encodeURIComponent(data.fromResourceName),
    };

    // 将参数对象转换为查询字符串
    const queryString = new URLSearchParams(
      Object.entries(urlParams).reduce(
        (acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        },
        {} as Record<string, string>,
      ),
    ).toString();

    navigator(`/paper-base/${active}/personal-paper/${data.id}?${queryString}`);
    // localStorage.setItem("paperBaseName", data.name);
  };

  const handlePopoverConfirm = async (item: anyValueProps[]) => {
    const deleteIds = item.map((ele) => ele.id);
    const {
      data: { code },
    } = await deletePaperBase(deleteIds);
    if (code === 200) {
      refetch();
      message.success("删除成功");
    } else {
      message.error("删除失败");
    }
  };

  const queryRequest = async () => {
    const params = {
      page: pagination.page,
      size: pagination.pageSize,
      ...queryParams,
    };
    const api = isDialog ? getDocumentByUid(params) : getPaperBase(params);
    const response = await api;
    return response.data;
  };

  const { data, status, refetch, error } = useQuery({
    queryKey: ["getPaperBase", pagination, queryParams, type],
    queryFn: queryRequest,
  });

  const batchDownLoad = async () => {
    const downLoadList = tableList.filter((item) => item.checked);
    const shareList = downLoadList.filter(
      (item) => item.documentSourceType === "share",
    );
    window.console.log(downLoadList, "downLoadList");
    if (downLoadList.length === 1 && downLoadList[0].pdfNumber === 0) {
      message.warning("资料库中没有资料, 无法下载");
      return;
    }
    if (shareList.length > 0) {
      message.warning("选中的资料库中存在分享的资料库不支持下载");
      return;
    }
    const downLoadIds = downLoadList.map((item) => item.id);
    if (downLoadIds.length === 0) {
      message.warning("请选择要下载的资料库");
      return;
    }
    const idsBase64 = Base64.encode(JSON.stringify(downLoadIds));
    const url = `${DownloadBaseUrl}/api/file/download/document?document=${idsBase64}`;
    const link = document.createElement("a");
    link.href = url;
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(url);
  };

  const handleSearchParams = (value: any) => {
    const { fuzzyQuery, date, sort, asc, resourceCode } = value;
    const queryParams: any = {
      ...(fuzzyQuery && { fuzzyQuery }),
      ...(sort && { sort }),
      ...(asc === "asc" && { asc: asc === "asc" }),
      ...(resourceCode === "1" ? {} : { resourceCode }),
    };
    if (date && date.length > 0) {
      queryParams.updateStartTime = date[0];
      queryParams.updateEndTime = date[1];
    }

    setQueryParams(queryParams);
    setPagination((prev) => ({ page: 1, pageSize: prev.pageSize }));
  };

  useEffect(() => {
    switch (status) {
      case "success": {
        setTotal(data?.total);
        const modifiedData = data?.data.map((item: any) => ({
          ...item,
          checked: false,
        }));
        setTableList(modifiedData);
        break;
      }
      case "error":
        message.error("获取资料库列表失败" + error.message);
        break;
      default:
        break;
    }
  }, [data, status]);

  useEffect(() => {
    handleSearchParams(searchParam);
  }, [searchParam]);

  return (
    <Root>
      {!isDialog && <Breadcrumb parent={[]} current={"资料库"} />}
      <Content isDialog={isDialog}>
        <RightContent>
          {!isDialog && (
            <OperationBar>
              <PaperBaseHeader
                searchParam={searchParam}
                setSearchParam={setSearchParam}
                setAddOpen={setOpen}
                setAddType={setDialogType}
                refetch={refetch}
                type={active}
              />
            </OperationBar>
          )}
          <TableSide isDialog={isDialog}>
            <CardTable
              type="paper"
              total={total}
              pagination={pagination}
              onChangePage={onPageChange}
              data={tableList}
              onSelectChange={onChange}
              onClickCard={handleClickCard}
              columnsTypeName={{
                title: "name",
                version: "version",
                count: "pdfNumber",
                updateTime: "updateTime",
                groupName: "fromResourceName",
                documentSourceType: "documentSourceType",
              }}
              buttonGroup={
                !isDialog && (
                  <ButtonGroup
                    paperText={"资料库"}
                    data={buttonGroup}
                    checkedList={tableList}
                    setCheckedList={setTableList}
                    // setPopover={setPopover}
                    deleteAction={handlePopoverConfirm}
                    downLoadSelect={batchDownLoad}
                  />
                )
              }
              operations={
                !isDialog
                  ? {
                      edit: handleEdit,
                      delete: handleDelete,
                      chat: handleChat,
                      transfer: handleTransfer,
                    }
                  : undefined
              }
            />
          </TableSide>
        </RightContent>
      </Content>
      {open && (
        <AddPaperBaseDialog
          type={dialogType}
          open={open}
          setOpen={setOpen}
          reload={refetch}
          editOption={editObject}
        />
      )}
    </Root>
  );
};
export default PaperBase;
