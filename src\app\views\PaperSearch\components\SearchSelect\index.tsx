import { MenuItem, Select, SxProps, Theme } from "@mui/material";
import React from "react";
import { styled } from "@mui/material";

const StyledSelect = styled(Select)(() => ({
  background: "#fff",
  borderRadius: 28,
  fontSize: 16,
  "&::before": {
    display: "none",
  },
  "& .MuiSelect-icon": {
    right: 20,
  },
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none",
  },
  "& .MuiSelect-outlined": {
    padding: 0,
  },
}));

type ValueType = keyof SearchSelectProps["optionMap"];
type OptionType = {
  label: string;
  value: number | string;
};

interface SearchSelectProps {
  width?: number;
  height?: number | string;
  value: string | number;
  optionMap?: Record<number, string>;
  options?: Array<number | string | OptionType>;
  onChange: (value: ValueType) => void;
  sx?: SxProps<Theme>;
}
const SearchSelect: React.FC<SearchSelectProps> = ({
  value,
  optionMap,
  options,
  onChange,
  sx,
  width = "100%",
  height = "100%",
}) => {
  const selectOptions = useMemo(() => {
    let optionList: any = [];
    if (options) {
      const isOptionType =
        Array.isArray(options) &&
        options.every((item) => typeof item === "object" && item !== null);
      if (!isOptionType) {
        optionList = options.map((item) => ({
          label: item + "",
          value: item,
        }));
      }
    } else if (optionMap) {
      optionList = Object.entries(optionMap).map(([value, label]) => ({
        label,
        value,
      }));
    }
    return optionList;
  }, [options, optionMap]);
  return (
    <StyledSelect
      sx={{
        width,
        height,
        ...sx,
      }}
      value={value}
      onChange={(e) => onChange(e.target.value as ValueType)}
    >
      {selectOptions.map((item: any, index: number) => (
        <MenuItem key={index} value={item.value}>
          {item.label}
        </MenuItem>
      ))}
    </StyledSelect>
  );
};

export default SearchSelect;
