import { Pagination, PaginationProps, styled } from "@mui/material";
import React from "react";
import MySelect from "../Select";

const PaginationBar = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
}));

const PageSizeBox = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  marginInlineEnd: "8px",
  fontSize: 14,
  fontWeight: 400,
  color: "rgba(64, 64, 64, 1)",
}));

const PageSizeSelect = styled(MySelect)(() => ({
  width: "115px !important",
  height: 32,
}));

const StyledPagination = styled(Pagination)(({ theme }) => ({
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "flex-end",
  padding: theme.spacing(2, 0),
  "& .MuiPaginationItem-root": {
    width: 40,
    height: 32,
    backgroundColor: "#fff",
    color: "rgba(64, 64, 64, 1)",
    border: "1px solid rgba(235, 235, 235, 1)",
  },
  "& .Mui-selected": {
    width: 40,
    height: 32,
    background:
      "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
    color: "#fff",
  },
}));

export interface PageProps {
  page: number;
  pageSize: number;
}

interface MyPaginationProps extends PaginationProps {
  total: number;
  page: number;
  onChangePage: (pageParams: PageProps) => void;
  size?: "small" | "medium" | "large";
  pageSize?: number;
  pageSizeOptions?: number[];
}
const MyPaperPagination: React.FC<MyPaginationProps> = (props) => {
  const {
    total,
    page,
    onChangePage,
    size = "large",
    pageSize = 10,
    showFirstButton = false,
    showLastButton = false,
    siblingCount = 2,
    boundaryCount = 1,
    pageSizeOptions = [],
    ...others
  } = props;
  const handleChange = (type: string, value: number) => {
    if (type === "page") {
      onChangePage({ page: value, pageSize });
    } else if (type === "pageSize") {
      const calcPage: number =
        Math.ceil(total / value) < page ? Math.ceil(total / value) : page;
      onChangePage({ page: calcPage, pageSize: value });
    }
  };
  return (
    <PaginationBar>
      <PageSizeBox>
        <span style={{ marginRight: 9 }}>每页显示</span>
        {pageSizeOptions.length > 0 && (
          <PageSizeSelect
            // width={115}
            value={pageSize.toString()}
            anchorOrigin={"top"}
            transformOrigin={"bottom"}
            options={pageSizeOptions.map((item) => ({
              label: `${item} 条/页`,
              value: String(item),
            }))}
            onChange={(e) => handleChange("pageSize", Number(e.target.value))}
          />
        )}
      </PageSizeBox>
      <StyledPagination
        color="primary"
        showFirstButton={showFirstButton}
        showLastButton={showLastButton}
        siblingCount={siblingCount}
        boundaryCount={boundaryCount}
        size={size}
        count={Math.ceil(total / pageSize)}
        page={page}
        {...others}
        onChange={(_, page: number) => handleChange("page", page)}
      />
    </PaginationBar>
  );
};

export default MyPaperPagination;
