import { PayloadAction, createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { anyValueProps } from "../types/common";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { getToken } from "@/utils/auth";
import { getChatDetailById } from "@/api/chat";

export interface CounterState {
  appSetting: anyValueProps;
  active: string | null;
  currentChat: anyValueProps[];
  isFirst: boolean;
  selectedBank: anyValueProps;
  previewItem: anyValueProps;
  analysisResult: anyValueProps;
  listTotal: number;
}

const initialState: CounterState = {
  // 设置模块
  appSetting: {
    knowledge: 0,
    normal: 50,
    switchState: true, // 是否携带聊天记录
    theme: "light",
    temperature: 0.2,
  },
  isFirst: false,
  active: null,
  currentChat: [],
  selectedBank: {},
  previewItem: {},
  analysisResult: [],
  listTotal: 0,
};

interface fetchStreamDataThunkProps {
  userRole: any;
  body: any;
  onclose: () => void;
  onerror: () => void;
  scrollToBottom: () => void;
  isScrollingRef: any;
  chatDetailId: number | undefined;
  text: any;
  controller: AbortController;
}

export const fetchStreamDataThunk = createAsyncThunk(
  "chat/fetchStreamData",
  async (
    {
      userRole,
      body,
      onclose,
      onerror,
      scrollToBottom,
      controller,
      isScrollingRef,
      text,
      chatDetailId,
    }: fetchStreamDataThunkProps,
    { dispatch },
  ) => {
    try {
      let buffer = text.buffer ? text.buffer : "";
      let thinkText = text.thinkText ? text.thinkText : "";
      let progressText = text.progressText ? text.progressText : "";
      let insideThink = text.insideThink ? text.insideThink : false;
      let insideProgress = text.insideProgress ? text.insideProgress : false;
      let isFirstChunk = true;
      let additionalInfo = "";
      await fetchEventSource("/chat/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "text/event-stream",
          Authorization: `Bearer ${getToken()}`,
          ...(userRole.resourceCode
            ? { "X-Group": userRole.resourceCode }
            : {}),
        },
        body: JSON.stringify(body),
        signal: controller.signal, // 支持中止请求
        openWhenHidden: true, // 避免连接断开
        onmessage: async (event) => {
          const text = JSON.parse(event.data).data;
          // 首次消息块处理 - 非阻塞获取额外信息
          if (isFirstChunk && body.type === 1) {
            isFirstChunk = false;

            // 不等待请求完成，直接处理消息
            if (chatDetailId) {
              getChatDetailById({ id: chatDetailId })
                .then(({ data: { data } }) => {
                  additionalInfo = data.additionalInfo;
                  // 更新聊天状态，只更新 additionalInfo
                  dispatch(
                    updateCurrentChat({
                      additionalInfo,
                    }),
                  );
                })
                .catch((error) => {
                  message.error((error as Error).message);
                });
            }
          }
          const thinkStart = /<think\s*[^>]*>/;
          const thinkEnd = /<\s*\/think\s*>/;
          const progressStart = /<chatProcess\s*[^>]*>/;
          const progressEnd = /<\s*\/chatProcess\s*>/;

          const thinkStarts = text.match(thinkStart);
          const thinkEnds = text.match(thinkEnd);
          const progressStarts = text.match(progressStart);
          const progressEnds = text.match(progressEnd);

          if (thinkStarts) {
            insideThink = true;
            thinkText += text;
          } else if (thinkEnds) {
            thinkText += text;
            insideThink = false;
          } else if (insideThink) {
            thinkText += text;
          } else if (progressStarts) {
            insideProgress = true;
            progressText += text;
          } else if (progressEnds) {
            progressText += text;
            insideProgress = false;
          } else if (insideProgress) {
            progressText += text;
          } else {
            if (text.match(thinkStart)) {
              thinkText += text;
            } else {
              buffer += text;
            }
          }
          dispatch(
            updateCurrentChat({
              answer: buffer,
              thinkText,
              progressText,
              active: body.chatId,
              loading: true,
              additionalInfo,
              chatDetailId,
            }),
          );
          if (isScrollingRef.current) {
            scrollToBottom();
          }
        },
        onopen: async () => {
          scrollToBottom();
        },
        onclose: () => {
          onclose();
        },
        onerror: (error) => {
          if (error instanceof Error) {
            onerror();
          }
          throw error;
        },
      });
    } catch (error) {
      console.error("Unexpected error:", error);
      throw error;
    }
  },
);
export const counterSlice = createSlice({
  name: "counter",
  initialState,
  reducers: {
    setAnalysis: (state, action: PayloadAction<anyValueProps>) => {
      state.analysisResult = action.payload;
    },
    setPreviewItem: (state, action: PayloadAction<anyValueProps>) => {
      state.previewItem = action.payload;
    },
    setSelectedBank: (state, action: PayloadAction<anyValueProps>) => {
      state.selectedBank = action.payload;
    },
    setIsFirst: (state, action: PayloadAction<boolean>) => {
      state.isFirst = action.payload;
    },
    addCurrentChat: (state, action: PayloadAction<anyValueProps>) => {
      state.currentChat.push(action.payload);
    },
    // 更新对话
    updateCurrentChat(state, action) {
      const { active } = action.payload;
      const len = state.currentChat.findIndex((item) => item.active === active);
      if (len !== -1) {
        state.currentChat[len] = {
          ...state.currentChat[len],
          ...action.payload,
        };
      }
    },
    deleteCurrentChat(state, action) {
      const data = state.currentChat.filter(
        (item) => item.active !== action.payload,
      );
      state.currentChat = data;
    },
    deleteCurrentLocalStorageChat(state, action) {
      const data = state.currentChat.filter(
        (item) => item.id !== action.payload,
      );
      state.currentChat = data;
    },
    clearCurrentChat(state) {
      state.currentChat = [];
    },
    setActives: (state, action) => {
      state.active = action.payload;
    },
    setListTotal: (state, action) => {
      state.listTotal = action.payload;
    },
    // 改变设置
    changeValue: (state, action: PayloadAction<any>) => {
      const { field, value } = action.payload;
      state.appSetting[field] = value;
    },
    reset: () => initialState,
  },
});

export const {
  setSelectedBank,
  setListTotal,
  setActives,
  changeValue,
  addCurrentChat,
  clearCurrentChat,
  updateCurrentChat,
  deleteCurrentChat,
  deleteCurrentLocalStorageChat,
  setIsFirst,
  setPreviewItem,
  setAnalysis,
  reset,
} = counterSlice.actions;
export default counterSlice.reducer;
