import React, { useMemo, useState } from "react";
import { Box, List, styled } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import Footer from "./components/Footer";
// import { useDispatch } from "react-redux";
import CopyRight from "./components/CopyRight";
import background from "@/assets/background.png";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { reset } from "@/store/counterSlice";
import { getLogoByState } from "@/utils/logoUtils";

const NAVWIDTH = {
  min: 64,
  max: 180,
};

const Root = styled("div")(() => ({
  height: "100%",
  width: "100%",
  overflow: "auto",
}));

const NavigationBar = styled(Box, {
  shouldForwardProp: (prop) => prop !== "isShrink",
})<{ isShrink: boolean }>(({ theme, isShrink }) => ({
  width: NAVWIDTH[isShrink ? "min" : "max"],
  height: "100%",
  position: "fixed",
  zIndex: 1000,
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  boxSizing: "border-box",
  padding: theme.spacing(0, 0, 3, 0),
  background: "rgba(255, 255, 255, 1)",
  boxShadow: "10px 0px 30px  rgba(0, 0, 0, 0.05)",
  transition:
    "opacity .3s cubic-bezier(.645,.045,.355,1), width .3s cubic-bezier(.645,.045,.355,1)",
}));

const Content = styled("div")(() => ({
  height: "100%",
  paddingLeft: NAVWIDTH.min,
  backgroundImage: `url(${background})`,
  backgroundSize: "cover",
  backgroundPosition: "center",
  backgroundRepeat: "no-repeat",
  // transition: "margin 0.7s ease",
  boxSizing: "border-box",
  minWidth: "1750px",
  overflowY: "hidden",
}));

const ContentMain = styled("div")(() => ({
  width: "100%",
  height: "calc(100% - 30px)",
}));

const LogoBox = styled("div")(({}) => ({
  width: "100%",
  height: 64,
  borderBottom: "1px solid rgba(237, 237, 237, 1)",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const LogoImg = styled("div", {
  shouldForwardProp: (prop) => prop !== "isShrink",
})<{ isShrink: boolean }>(({ isShrink }) => ({
  width: isShrink ? 38 : 150,
  height: 38,
  cursor: "pointer",
  backgroundImage: `url(${getLogoByState(isShrink)})`,
  backgroundSize: "100% 100%",
}));

const NavBox = styled(Box)(() => ({
  width: "100%",
  height: "calc(100% - 64px)",
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
}));

const StyledIconDiv = styled("div", {
  shouldForwardProp: (prop) => prop !== "isShrink",
})<{ isShrink: boolean }>(({ isShrink, theme }) => ({
  width: "100%",
  height: 48,
  marginBottom: "3px",
  display: "flex",
  alignItems: "center",
  justifyContent: isShrink ? "center" : "flex-start",
  paddingLeft: isShrink ? 0 : theme.spacing(2),
  borderRadius: "16px",
  boxSizing: "border-box",
  ":hover": {
    background:
      "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
    cursor: "pointer",
    color: "#fff",
  },
  ":hover img": {
    filter: "brightness(0) invert(1)",
  },
  ":hover span": {
    color: "#fff",
  },
}));

const StyledImageIcon = styled("img")(() => ({
  width: 22,
  height: 22,
}));

const StyleItemBar = styled(Box)(() => ({
  display: "flex",
  alignItems: "center",
  flexDirection: "column",
  marginTop: "20px",
  fontSize: "12px",
  borderRadius: "5px",
  boxSizing: "border-box",
  color: "#000",
  ":hover": {
    cursor: "pointer",
  },
  "& > div": {
    textAline: "center",
  },
}));

const StyledName = styled("span", {
  shouldForwardProp: (prop) => prop !== "active" && prop !== "isShrink",
})<{ active: boolean; isShrink: boolean }>(({ active, isShrink }) => ({
  height: 20,
  display: "inline-block",
  maxWidth: isShrink ? 0 : "100%",
  overflow: "hidden",
  opacity: isShrink ? 0 : 1,
  fontSize: 18,
  fontWeight: 400,
  lineHeight: "18px",
  color: active ? "#fff" : "#000",
  marginLeft: isShrink ? 0 : 14,
  fontFamily: "思源宋体 Medium",
  transition:
    "opacity .3s cubic-bezier(.645,.045,.355,1), width .3s cubic-bezier(.645,.045,.355,1)",
}));

/**
 * 判断当前路径与规则是否匹配
 * @param path 当前路径
 * @param rule 路由列表内配置的路径规则
 */
const match = (path: string, rule: string) => {
  const pathChip = path.split("/");
  const ruleChip = rule.split("/");
  if (pathChip.length !== ruleChip.length) {
    return false;
  }

  return ruleChip.reduce((res, cur, curIndex) => {
    let mate = false;
    if (cur.startsWith(":")) {
      mate = true;
    } else if (cur === pathChip[curIndex]) {
      mate = true;
    }

    return mate && res;
  }, true);
};

const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  // const dispatch = useDispatch();
  const [active, setActive] = useState<string>("");
  const [isShrink, setIsShrink] = useState<boolean>(true);
  const systemRoutes = useAppSelector((state) => state.route.systemRoute);

  const handleRouter = (path: string) => {
    const newPath = "/" + location.pathname;
    if (match(path, newPath)) {
      return;
    }
    dispatch(reset());
    navigate(path);
  };
  const goHome = () => {
    navigate("/");
  };

  const handleMove = (e: any) => {
    setIsShrink(e.type !== "mousemove");
  };

  useMemo(() => {
    const newPath = location.pathname.split("/")[1];
    if (newPath) {
      setActive(newPath);
    }
  }, [location]);

  return (
    <Root>
      <NavigationBar isShrink={isShrink}>
        <LogoBox>
          <LogoImg isShrink={isShrink} onClick={goHome} />
        </LogoBox>
        <NavBox>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "space-between",
              pr: "7px",
              pl: "7px",
            }}
            onMouseMove={(e) => handleMove(e)}
            onMouseLeave={(e) => handleMove(e)}
          >
            <List sx={{ width: "100%" }}>
              {systemRoutes.map(
                (item, index) =>
                  !item.hidden && (
                    <StyleItemBar
                      key={index}
                      onClick={() => handleRouter(item.path)}
                    >
                      {item.icon && (
                        <StyledIconDiv
                          isShrink={isShrink}
                          sx={{
                            background:
                              active === item.name
                                ? "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)"
                                : "#fff",
                          }}
                        >
                          <StyledImageIcon
                            src={item.icon}
                            alt="route-icon"
                            sx={{
                              filter:
                                active === item.name
                                  ? "brightness(0) invert(1)"
                                  : "none",
                            }}
                          />
                          <StyledName
                            active={active === item.name}
                            isShrink={isShrink}
                          >
                            {item.description}
                          </StyledName>
                        </StyledIconDiv>
                      )}
                    </StyleItemBar>
                  ),
              )}
            </List>
          </Box>
          <Footer isMinSize={isShrink} />
        </NavBox>
      </NavigationBar>
      <Content>
        <ContentMain>{children}</ContentMain>
        <CopyRight></CopyRight>
      </Content>
    </Root>
  );
};

export default Layout;
