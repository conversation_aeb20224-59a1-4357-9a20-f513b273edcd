declare namespace Chat {
  interface Chat {
    dateTime: string;
    text: string;
    inversion?: boolean;
    error?: boolean;
    loading?: boolean;
    pg_id?: number[];
    titleText?: titleTextProps[];
    knowledge?: number;
    requestOptions: requestOptionsProps;
  }

  interface titleTextProps {
    doi: number;
    title: string;
    texts?: string;
    id: number;
  }
  interface requestOptionsProps {
    prompt: string;
    options?: Chat.ConversationRequest | null;
  }
  interface History {
    title: string;
    isEdit: boolean;
    uuid: number;
  }

  interface ChatState {
    active: number | null;
    history: History[];
    chat: { uuid: number; data: Chat[] }[];
  }

  interface ConversationRequest {
    conversationId?: string | number;
    parentMessageId?: string;
  }
}
