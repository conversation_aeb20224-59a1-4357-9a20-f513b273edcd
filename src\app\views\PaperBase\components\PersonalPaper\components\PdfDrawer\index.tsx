import Drawer from "@/components/Drawer";
// import PdfView from "../../../../../../components/PdfView";
import PDFVirtualList from "@/components/PdfVirtualList";
import { CircularProgress, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";
import { useEffect, useState } from "react";

interface Props {
  setOpen: (value: boolean) => void;
  width: number;
  title: string;
  pdfUrl?: string;
}

const LoadingBox = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
}));

const PdfDrawer: React.FC<Props> = ({ setOpen, width, title, pdfUrl }) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const getHight = (value: number) => {
    // 这个函数会被传递给Drawer组件，但不在这里使用返回值
  };
  const [drawerTitle, setDrawerTitle] = useState<React.ReactNode>(title);

  const initTitle = () => {
    if (!title) {
      setDrawerTitle("");
      return;
    }

    // 按照|||分割标题
    const titleParts = title.split("|||");

    if (titleParts.length === 1) {
      // 如果没有分割符，直接显示原标题
      setDrawerTitle(titleParts[0].trim());
    } else if (titleParts.length === 2) {
      // 如果有两个部分，创建二维数组结构的显示
      const titleElement = (
        <div style={{ display: "flex", flexDirection: "column", gap: "4px" }}>
          <div style={{ fontWeight: "bold", fontSize: "16px" }}>
            {titleParts[0].trim()}
          </div>
          <div
            style={{ fontSize: "14px", color: "#666", fontWeight: "normal" }}
          >
            {titleParts[1].trim()}
          </div>
        </div>
      );
      setDrawerTitle(titleElement);
    } else {
      // 如果有多个部分，显示第一个作为主标题，其余作为副标题
      const titleElement = (
        <div style={{ display: "flex", flexDirection: "column", gap: "4px" }}>
          <div style={{ fontWeight: "bold", fontSize: "16px" }}>
            {titleParts[0].trim()}
          </div>
          <div
            style={{ fontSize: "14px", color: "#666", fontWeight: "normal" }}
          >
            {titleParts
              .slice(1)
              .map((part) => part.trim())
              .join(" • ")}
          </div>
        </div>
      );
      setDrawerTitle(titleElement);
    }
  };

  const close = () => {
    setOpen(false);
  };

  // 组件卸载时释放URL资源
  useEffect(
    () => () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    },
    [pdfUrl],
  );

  useEffect(() => {
    initTitle();
  }, [title]);

  return (
    <Drawer
      title={drawerTitle}
      setOpen={setOpen}
      open={true}
      width={width}
      getHeight={getHight}
      drawerClose={close}
    >
      {/* {pdfUrl && <PdfView width={width} file={pdfUrl} height={height} />} */}
      {pdfUrl ? (
        <PDFVirtualList key={pdfUrl} url={pdfUrl} />
      ) : (
        <LoadingBox>
          <CircularProgress color="inherit" />
          <Typography variant="h4" sx={{ ml: 2 }}>
            PDF加载中...
          </Typography>
        </LoadingBox>
      )}
    </Drawer>
  );
};

export default PdfDrawer;
