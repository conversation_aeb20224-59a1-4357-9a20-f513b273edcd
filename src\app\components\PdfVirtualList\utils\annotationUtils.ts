import {
  RawAnnotation,
  FormattedAnnotation,
  PageDimensions,
  CacheEntry,
} from "../types/types";

/**
 * 优化的注释缓存管理器
 */
class AnnotationCache {
  private cache = new Map<string, CacheEntry<FormattedAnnotation[][]>>();
  private maxCacheSize = 10;
  private dimensionsHashCache = new Map<string, string>();

  /**
   * 生成页面尺寸的哈希值
   */
  private hashDimensions(dimensions: Record<number, PageDimensions>): string {
    const sortedKeys = Object.keys(dimensions).sort(
      (a, b) => Number(a) - Number(b),
    );
    const hashString = sortedKeys
      .map((key) => {
        const dim = dimensions[Number(key)];
        return `${key}:${dim.width.toFixed(2)}x${dim.height.toFixed(2)}`;
      })
      .join("|");

    return this.simpleHash(hashString);
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }

  /**
   * 生成注释数据的哈希值
   */
  private hashAnnotations(annotations: RawAnnotation[]): string {
    if (!annotations?.length) return "empty";

    const hashString = annotations
      .map((ann) => `${ann.paragraphId}:${ann.frameCoords?.length || 0}`)
      .join("|");

    return this.simpleHash(hashString);
  }

  /**
   * 获取缓存键
   */
  private getCacheKey(
    annotations: RawAnnotation[],
    dimensions: Record<number, PageDimensions>,
  ): string {
    const annotationsHash = this.hashAnnotations(annotations);
    const dimensionsHash = this.hashDimensions(dimensions);
    return `${annotationsHash}_${dimensionsHash}`;
  }

  /**
   * LRU缓存清理
   */
  private evictLRU(): void {
    if (this.cache.size <= this.maxCacheSize) return;

    let oldestKey = "";
    let oldestTime = Date.now();

    // 使用forEach替代for...of来避免迭代器兼容性问题
    this.cache.forEach((entry, key) => {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    });

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * 获取缓存的注释数据
   */
  get(
    annotations: RawAnnotation[],
    dimensions: Record<number, PageDimensions>,
  ): FormattedAnnotation[][] | null {
    const key = this.getCacheKey(annotations, dimensions);
    const entry = this.cache.get(key);

    if (entry) {
      // 更新访问计数和时间戳
      entry.accessCount++;
      entry.timestamp = Date.now();
      return entry.data;
    }

    return null;
  }

  /**
   * 设置缓存数据
   */
  set(
    annotations: RawAnnotation[],
    dimensions: Record<number, PageDimensions>,
    data: FormattedAnnotation[][],
  ): void {
    const key = this.getCacheKey(annotations, dimensions);

    this.evictLRU();

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      accessCount: 1,
    });
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.dimensionsHashCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      hitRate: this.calculateHitRate(),
    };
  }

  private calculateHitRate(): number {
    let totalAccess = 0;
    let totalHits = 0;

    // 使用forEach替代for...of来避免迭代器兼容性问题
    this.cache.forEach((entry) => {
      totalAccess += entry.accessCount;
      totalHits += entry.accessCount - 1; // 第一次访问不算命中
    });

    return totalAccess > 0 ? totalHits / totalAccess : 0;
  }
}

// 全局缓存实例
const annotationCache = new AnnotationCache();

function findMinObject(data: Record<number, PageDimensions>) {
  let minEntry = null;

  for (const [key, value] of Object.entries(data)) {
    if (
      !minEntry ||
      (value.width < minEntry.width && value.height < minEntry.height)
    ) {
      minEntry = { key, ...value };
    }
  }

  return minEntry;
}

/**
 * 优化的注释格式化函数，支持缓存
 * @param annotations - 原始标注数据数组
 * @param pageDimensions - 页面尺寸记录，key为页码，value为宽高对象
 * @returns 按页面分组的格式化标注数组
 */
export function formatAnnotations(
  annotations: RawAnnotation[],
  pageDimensions: Record<number, PageDimensions>,
): FormattedAnnotation[][] {
  // 参数验证
  if (
    !annotations?.length ||
    !pageDimensions ||
    !Object.keys(pageDimensions).length
  ) {
    return [];
  }

  // 尝试从缓存获取
  const cachedResult = annotationCache.get(annotations, pageDimensions);
  if (cachedResult) {
    return cachedResult;
  }

  // 计算新的注释数据
  const result = computeAnnotations(annotations, pageDimensions);

  // 缓存结果
  annotationCache.set(annotations, pageDimensions, result);

  return result;
}

/**
 * 实际的注释计算逻辑
 */
function computeAnnotations(
  annotations: RawAnnotation[],
  pageDimensions: Record<number, PageDimensions>,
): FormattedAnnotation[][] {
  // 获取基准页面尺寸
  const basePage = findMinObject(pageDimensions);
  if (!basePage?.width || !basePage?.height) {
    console.warn("无效的页面尺寸数据");
    return [];
  }

  // 使用 Map 来存储每页的标注，提高性能
  const pageAnnotationsMap = new Map<number, FormattedAnnotation[]>();

  // 批量处理注释数据
  for (const item of annotations) {
    if (!item.frameCoords?.length) continue;

    for (const coord of item.frameCoords) {
      try {
        // 计算坐标
        const xMin = basePage.width * coord.upLeftScale[0];
        const xMax = basePage.width * coord.lowRightScale[0];
        const yMin = basePage.height * coord.upLeftScale[1];
        const yMax = basePage.height * coord.lowRightScale[1];

        // 验证坐标有效性
        if (isNaN(xMin) || isNaN(xMax) || isNaN(yMin) || isNaN(yMax)) {
          console.warn(`无效的坐标数据: ${item.paragraphId}`);
          continue;
        }

        // 创建标注对象
        const annotation: FormattedAnnotation = {
          id: `annotation_${item.paragraphId}`,
          text: item.paragraph,
          x: [xMin, xMax],
          y: [yMin, yMax],
          page: coord.page,
          originalWidth: basePage.width,
          originalHeight: basePage.height,
        };

        // 获取或创建页面数组
        const pageIndex = coord.page - 1;
        if (!pageAnnotationsMap.has(pageIndex)) {
          pageAnnotationsMap.set(pageIndex, []);
        }
        pageAnnotationsMap.get(pageIndex)!.push(annotation);
      } catch (error) {
        console.error(`处理注释时出错: ${item.paragraphId}`, error);
      }
    }
  }

  // 将 Map 转换为数组，确保按页码顺序排列
  const maxPage = Math.max(...Array.from(pageAnnotationsMap.keys()));
  const result: FormattedAnnotation[][] = [];

  for (let i = 0; i <= maxPage; i++) {
    result[i] = pageAnnotationsMap.get(i) || [];
  }

  return result;
}

/**
 * 清空注释缓存
 */
export function clearAnnotationCache(): void {
  annotationCache.clear();
}

/**
 * 获取注释缓存统计信息
 */
export function getAnnotationCacheStats() {
  return annotationCache.getStats();
}
