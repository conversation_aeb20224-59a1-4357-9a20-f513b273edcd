import { FormColumnProps } from "@/components/DynamicForm";
import { Checkbox, CheckboxProps } from "@mui/material";
import * as yup from "yup";

export const LoginColumns: FormColumnProps[] = [
  {
    name: "email",
    label: "邮箱号",
    componentType: "input",
    required: false,
    placeholder: "请输入邮箱号",
    grid: 12,
    validation: yup.string().required("请输入用户名/邮箱"),
    styleProps: {
      height: 45,
    },
    labelStyleProps: {
      marginLeft: "13px",
    },
  },
  {
    name: "password",
    label: "登录密码",
    componentType: "password",
    required: false,
    placeholder: "请输入密码",
    grid: 12,
    validation: yup.string().required("请输入密码"),
    styleProps: {
      height: 45,
    },
    labelStyleProps: {
      marginLeft: "13px",
    },
  },
];

export const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const OnTrialColumns: FormColumnProps[] = [
  {
    name: "company",
    label: "机构名称",
    componentType: "input",
    // required: true,
    placeholder: "请输入机构名称",
    grid: 12,
    // validation: yup.string().required("请输入机构名称"),
    styleProps: {
      height: 45,
    },
    labelStyleProps: {
      marginTop: 0,
    },
  },
  {
    name: "phone",
    label: "手机号",
    componentType: "input",
    // required: true,
    placeholder: "请输入手机号码",
    grid: 12,
    // validation: yup
    //   .string()
    //   .matches(/^1[3-9]\d{9}$/, "请输入有效的手机号码")
    //   .required("请输入手机号码"),
    styleProps: {
      height: 45,
    },
    labelStyleProps: {
      marginTop: 0,
    },
  },
  {
    name: "email",
    label: "邮箱",
    componentType: "input",
    required: true,
    placeholder: "请输入邮箱",
    grid: 12,
    validation: yup
      .string()
      .matches(emailRegex, "请输入有效的邮箱")
      .required("请输入邮箱"),
    styleProps: {
      height: 45,
    },
    labelStyleProps: {
      marginTop: 0,
    },
  },
  {
    name: "password",
    label: "密码",
    componentType: "password",
    required: true,
    placeholder: "请输入密码",
    grid: 12,
    validation: yup.string().required("请输入密码"),
    styleProps: {
      height: 45,
    },
    labelStyleProps: {
      marginTop: 0,
    },
  },
];

const BpIcon = styled("span")(({ theme }) => ({
  borderRadius: "50%",
  width: 14,
  height: 14,
  boxShadow:
    "inset 0 0 0 1px rgba(16,22,26,.2), inset 0 -1px 0 rgba(16,22,26,.1)",
  backgroundColor: "#f5f8fa",
  backgroundImage:
    "linear-gradient(180deg,hsla(0,0%,100%,.8),hsla(0,0%,100%,0))",
  ".Mui-focusVisible &": {
    outline: "2px auto rgba(19,124,189,.6)",
    outlineOffset: 2,
  },
  "input:hover ~ &": {
    backgroundColor: "#ebf1f5",
    ...theme.applyStyles("dark", {
      backgroundColor: "#30404d",
    }),
  },
  "input:disabled ~ &": {
    boxShadow: "none",
    background: "rgba(206,217,224,.5)",
    ...theme.applyStyles("dark", {
      background: "rgba(57,75,89,.5)",
    }),
  },
  ...theme.applyStyles("dark", {
    boxShadow: "0 0 0 1px rgb(16 22 26 / 40%)",
    backgroundColor: "#394b59",
    backgroundImage:
      "linear-gradient(180deg,hsla(0,0%,100%,.05),hsla(0,0%,100%,0))",
  }),
}));

const BpCheckedIcon = styled(BpIcon)({
  backgroundColor: "rgba(24, 112, 199, 1)",
  backgroundImage:
    "linear-gradient(180deg,hsla(0,0%,100%,.1),hsla(0,0%,100%,0))",
  "&::before": {
    display: "block",
    width: 14,
    height: 14,
    backgroundImage:
      "url(\"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath" +
      " fill-rule='evenodd' clip-rule='evenodd' d='M12 5c-.28 0-.53.11-.71.29L7 9.59l-2.29-2.3a1.003 " +
      "1.003 0 00-1.42 1.42l3 3c.***********.71.29s.53-.11.71-.29l5-5A1.003 1.003 0 0012 5z' fill='%23fff'/%3E%3C/svg%3E\")",
    content: '""',
  },
  "input:hover ~ &": {
    backgroundColor: "rgba(24, 112, 199, 1)",
  },
});

export function BpCheckbox(props: CheckboxProps) {
  return (
    <Checkbox
      sx={{
        "&:hover": { bgcolor: "transparent" },
        padding: 0,
        marginRight: "5px",
      }}
      disableRipple
      color="default"
      checkedIcon={<BpCheckedIcon />}
      icon={<BpIcon />}
      inputProps={{ "aria-label": "Checkbox demo" }}
      {...props}
    />
  );
}

export type SelectType = "login" | "trialuse" | "forgotpw";

export const steps = ["验证邮箱号", "设置密码", "密码重置成功"];

export const forgotStep1Columns: FormColumnProps[] = [
  {
    name: "email",
    label: "邮箱号",
    componentType: "input",
    required: true,
    placeholder: "请输入邮箱",
    grid: 12,
    validation: yup
      .string()
      .required("请输入邮箱")
      .matches(emailRegex, "请输入有效的邮箱"),
    styleProps: {
      height: 56,
    },
  },
];

export const forgotStep2Columns: FormColumnProps[] = [
  {
    name: "password",
    label: "密码",
    componentType: "password",
    required: true,
    placeholder: "请输入密码",
    grid: 12,
    validation: yup
      .string()
      .min(6, "密码长度不可小于6位")
      .max(20, "密码长度不可大于20位")
      .required("请输入密码"),
  },
  {
    name: "confirmPassword",
    label: "确认密码",
    componentType: "password",
    required: true,
    placeholder: "请再次输入密码",
    grid: 12,
    validation: yup
      .string()
      .oneOf([yup.ref("password")], "两次输入的密码不一致")
      .required("请输入密码"),
  },
];
