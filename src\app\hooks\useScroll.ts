export function useScroll() {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isScrolling, setIsScrolling] = useState<boolean>(true);
  const isScrollingRef = useRef(isScrolling);
  const scrollThreshold = 30;

  const scrollToBottom = () => {
    requestAnimationFrame(() => {
      if (scrollRef.current) {
        scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
      }
    });
  };

  useEffect(() => {
    const container = scrollRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollHeight, scrollTop, clientHeight } = container;
      const isNearBottom =
        scrollHeight - (scrollTop + clientHeight) <= scrollThreshold;
      setIsScrolling(isNearBottom);
      isScrollingRef.current = isNearBottom;
    };

    container.addEventListener("scroll", handleScroll);
    // // 初始检查
    // handleScroll();

    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return {
    scrollRef,
    scrollToBottom,
    isScrolling,
    isScrollingRef,
    setIsScrolling,
  };
}

export default useScroll;
