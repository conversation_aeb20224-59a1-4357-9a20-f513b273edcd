import ArticleItem from "@/views/PaperSearch/components/ArticleItem";
import CloseIcon from "@mui/icons-material/Close";
import { BpCheckbox } from "./setting";
import SortPart from "@/views/KnowledgeBase/components/SortPart";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import { useNavigate } from "react-router-dom";
import { setActives, setSelectedBank } from "@/store/counterSlice";
import { getNewChatId } from "@/api/chat";
import { anyValueProps } from "@/types/common";
import { batchPdfs } from "@/api/personalpaper";
// import { batchPdfs } from "@/api/personalpaper";
const Root = styled("div")(() => ({
  width: "560px",
  border: "1px solid #e5e6eb",
  borderRadius: "12px",
  background: "#fff",
  display: "flex",
  flexDirection: "column",
}));

const PaperInfoHead = styled("div")(() => ({
  height: "48px",
  padding: "12px 16px",
  borderBottom: "1px solid #e5e6eb",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  boxSizing: "border-box",
}));

const PaperInfoTitle = styled("div")(() => ({}));

const IconDiv = styled("div")(() => ({
  cursor: "pointer",
}));

const PaperInfoMain = styled("div")(() => ({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  minHeight: 0,
}));

const PaperInfoMainHead = styled("div")(() => ({
  height: "26px",
  padding: "0 16px",
  margin: "10px 0 8px",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  fontSize: "14px",
}));

const PaperInfoMainContent = styled("div")(() => ({
  flex: 1,
  overflow: "auto",
  padding: "0 16px",
  marginRight: "3px",
  boxSizing: "border-box",
}));

const PaperInfoFooter = styled("div")(() => ({
  height: "56px",
  borderTop: "1px solid #e5e6eb",
  padding: "12px 16px",
  boxSizing: "border-box",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  fontSize: "14px",
}));

const ButtonStyle = styled(Button, {
  shouldForwardProp: (prop) =>
    prop !== "currentBgColor" && prop !== "fontColor",
})<{ currentBgColor: string; fontColor?: string }>(
  ({ currentBgColor, fontColor }) => ({
    borderRadius: "19px",
    background: currentBgColor,
    marginLeft: "5px",
    color: fontColor ? fontColor : "#fff",
    lineHeight: "normal",
    padding: "5px 15px",
    ":disabled": {
      cursor: " not-allowed",
      pointerEvents: "auto",
    },
  }),
);

const PaperInfoFooterLeft = styled("div")(() => ({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  columnGap: "5px",
}));

const StyledArticleItem = styled("div")(() => ({
  display: "flex",
  borderRadius: 20,
  background: "#fff",
  border: "1px solid  rgba(235, 235, 235, 1)",
  marginBottom: 20,
  padding: "15px 20px",
  boxSizing: "border-box",
}));

const Main = styled("div")(() => ({
  marginLeft: 10,
  boxSizing: "border-box",
  flex: 1,
  minWidth: 0,
}));

interface Props {
  setExpandedIndex: (value: number | null) => void;
  paperData: anyValueProps[];
  currentPaperItemId: number;
}
const Index: React.FC<Props> = ({
  setExpandedIndex,
  paperData,
  currentPaperItemId,
}) => {
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [newPaperData, setNewPaperData] = useState<anyValueProps[]>([]);
  const [allChecked, setAllChecked] = useState<boolean>(false);
  const [sort, setSort] = useState<string>("");
  const { listTotal } = useAppSelector((state: RootState) => state.counter);
  const itemRefs = useRef<any>({});
  const containerRef = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();
  const navigator = useNavigate();
  const [isMounted, setIsMounted] = useState(false);

  const pageIds = useMemo(
    () => paperData.map((item) => item.pdfId),
    [paperData],
  );

  const memoizedArray = useMemo(() => paperData, [JSON.stringify(paperData)]);

  useEffect(() => {
    setNewPaperData(memoizedArray);
    setSort("");
  }, [memoizedArray]);

  useEffect(() => {
    setIsMounted(true); // 组件挂载后设置为true
  }, []);

  const onChangeId = (id: number, selected: boolean) => {
    const newSelectedIds = selected
      ? Array.from(new Set([...selectedIds, id]))
      : selectedIds.filter((item) => item !== id);
    setSelectedIds(newSelectedIds);
  };

  const handleChange = (e: any) => {
    const checked = e.target.checked;
    if (checked) {
      setSelectedIds(Array.from(new Set([...selectedIds, ...pageIds])));
      setAllChecked(true);
    } else {
      setSelectedIds([]);
      setAllChecked(false);
    }
  };

  // 批量取消
  const handleCancel = () => {
    setAllChecked(false);
    setSelectedIds([]);
  };

  useEffect(() => {
    if (sort === "asc") {
      const ascData = [...memoizedArray].sort((a, b) => b.index - a.index);
      setNewPaperData(ascData);
    } else if (sort === "desc") {
      const descData = [...memoizedArray].sort((a, b) => a.index - b.index);
      setNewPaperData(descData);
    }
  }, [sort]);

  const handleSortChange = (value: { order: string; name: string }) => {
    setSort(value.order);
    setAllChecked(false);
    setSelectedIds([]);
  };

  //
  const getChatId = async () => {
    const {
      data: { data },
    } = await getNewChatId();
    dispatch(setActives(data));
  };

  const chatPdf = () => {
    if (listTotal >= 1000) {
      message.warning("聊天窗口已到达1000次,请删除之前创建的聊天窗口");
    } else if (selectedIds.length > 5) {
      message.warning("最多选择5篇资料");
    } else {
      dispatch(setActives(null));
      dispatch(setSelectedBank({ externalIds: selectedIds, bankType: 2 }));
      navigator("/ai-chat");
      getChatId();
    }
  };

  const batchDownLoad = async () => {
    const filteredPapers = paperData
      .filter((paper: any) => selectedIds.includes(paper.pdfId))
      .map((item) => ({
        pdfId: item.pdfId,
        bucket: item.bucket,
        path: item.pdfPath,
      }));

    const { data } = await batchPdfs(filteredPapers);
    const url = window.URL.createObjectURL(new Blob([data]));
    const a = document.createElement("a");
    a.href = url;
    a.download = `pdfs.zip`; // 设置文件名
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // 修复滚动逻辑
  useEffect(() => {
    // 确保组件已挂载且有 currentPaperItemId
    if (!isMounted || !currentPaperItemId) return;

    // 使用 setTimeout 确保 DOM 更新完成后再滚动
    const timer = setTimeout(() => {
      const targetElement = itemRefs.current[currentPaperItemId];
      const container = containerRef.current;

      if (targetElement && container) {
        const containerRect = container.getBoundingClientRect();
        const elementRect = targetElement.getBoundingClientRect();
        const scrollTop =
          elementRect.top - containerRect.top + container.scrollTop;

        container.scrollTo({
          top: scrollTop,
          behavior: "smooth",
        });
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [currentPaperItemId, isMounted, paperData]);

  return (
    <Root>
      <PaperInfoHead>
        <PaperInfoTitle>参考资料</PaperInfoTitle>
        <IconDiv onClick={() => setExpandedIndex(null)}>
          <CloseIcon style={{ fontSize: "18px" }} />
        </IconDiv>
      </PaperInfoHead>
      <PaperInfoMain>
        <PaperInfoMainHead>
          <span>共 {paperData.length} 个结果</span>
          <div style={{ marginRight: "15px" }}>
            <SortPart
              label="相关性优先"
              name="updateTime"
              setSortInfo={handleSortChange}
              isBorder={true}
            ></SortPart>
          </div>
        </PaperInfoMainHead>
        <PaperInfoMainContent ref={containerRef}>
          {newPaperData.map((item) => (
            <StyledArticleItem
              key={item.pdfId}
              ref={(el) => (itemRefs.current[item.pdfId] = el)}
            >
              <div>
                <BpCheckbox
                  checked={selectedIds.some((el) => el === item.pdfId)}
                  onChange={(event) =>
                    onChangeId(item.pdfId, event.target.checked)
                  }
                />
              </div>
              <Main>
                <ArticleItem
                  articleInfo={{
                    title: item.pdfTitle,
                    keywords: item.keywords,
                    authors: item.authors,
                    pdfId: item.pdfId,
                    index: item.index,
                  }}
                  type="aiDetail"
                />
              </Main>
            </StyledArticleItem>
          ))}
        </PaperInfoMainContent>
      </PaperInfoMain>
      <PaperInfoFooter>
        <PaperInfoFooterLeft>
          <BpCheckbox
            onChange={handleChange}
            checked={allChecked}
            indeterminate={
              selectedIds &&
              selectedIds.length > 0 &&
              selectedIds.length < paperData.length
            }
          />
          <span>全选</span>
          <span>已选 {selectedIds.length} 个</span>
        </PaperInfoFooterLeft>
        <div>
          <ButtonStyle
            currentBgColor="rgba(240, 241, 244, 0.549019607843137)"
            fontColor="#333333"
            onClick={handleCancel}
            disabled={selectedIds.length === 0}
          >
            取消
          </ButtonStyle>
          <ButtonStyle
            currentBgColor="rgba(2, 167, 240, 0.549019607843137)"
            disabled={selectedIds.length === 0}
            onClick={batchDownLoad}
          >
            下载
          </ButtonStyle>
          <ButtonStyle
            currentBgColor="rgba(75,71,203)"
            onClick={chatPdf}
            disabled={selectedIds.length === 0}
          >
            AI对话
          </ButtonStyle>
        </div>
      </PaperInfoFooter>
    </Root>
  );
};
export default Index;
