import { RouteProps } from "..";
import searchIcon from "@/assets/search.svg";
import PaperSearch from "@/views/PaperSearch";
import SearchResult from "@/views/PaperSearch/SearchResult";
// import PdfChat from "@/views/PaperSearch/PdfChat";
import AnalysisResults from "@/views/PaperSearch/AnalysisResults";
import PaperDetail from "@/views/PaperSearch/PaperDetail";

export const paperRoute: Array<RouteProps> = [
  {
    path: "/paper-search",
    name: "paper-search",
    description: "检索",
    lazyComponent: () => import("../../views/PaperSearch"),
    components: PaperSearch,
    icon: searchIcon,
    menuCode: "Search",
    children: [
      {
        path: "/paper-search/search-result/:searchId",
        name: "search-result",
        description: "检索结果",
        lazyComponent: () => import("../../views/PaperSearch/SearchResult"),
        components: SearchResult,
        hidden: true,
      },
      // {
      //   path: "/paper-search/search-result/pdf-chat",
      //   name: "pdf-chat",
      //   description: "PDF对话",
      //   lazyComponent: () => import("../../views/PaperSearch/PdfChat"),
      //   components: PdfChat,
      //   hidden: true,
      // },
      {
        path: "/paper-search/analysis-results/:way",
        name: "analysis-results",
        description: "",
        hidden: false,
        lazyComponent: () => import("../../views/PaperSearch/AnalysisResults"),
        components: AnalysisResults,
      },
      {
        path: "/paper-search/paper-details/:way",
        name: "paper-details",
        description: "",
        hidden: false,
        lazyComponent: () => import("../../views/PaperSearch/PaperDetail"),
        components: PaperDetail,
      },
    ],
  },
];
