import React from "react";
import { styled } from "@mui/material";
import Breadcrumb from "@/components/Breadcrumb";
import { useParams } from "react-router-dom";
import EchartsItem from "./components/EchartsItem";
import { RootState, useAppSelector } from "@/hooks";
import { pdfDrawKeywords } from "@/api/personalpaper";
const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
}));
const HeadInfo = styled("div")(() => ({
  width: "100%",
  height: "52px",
  borderRadius: "20px",
  background: "#fff",
  display: "flex",
  margin: "24px 0 15px 0",
  // opacity: 1,
}));
const HeadInfoItem = styled("div")(() => ({
  width: "33.33%",
  fontSize: "14px",
  background:
    "radial-gradient(8.62% 151.39% at 4.077540106951872% -91.66666666666666%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  borderRadius: "20px",
  lineHeight: "52px",
  padding: "0 35px",
}));

const PaperNumber = styled(HeadInfoItem)(() => ({
  background:
    "radial-gradient(22.67% 101.39% at 17% -40.27777777777778%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
}));
const NumberStyle = styled("span")(() => ({
  fontSize: "20px",
  fontWeight: 600,
  padding: "0 30px",
}));
const Main = styled("div")(() => ({
  flex: 1,
  height: 0,
  margin: "0 10%",
  borderRadius: "10px",
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
}));

const MainContent = styled("div")(() => ({
  flex: 1,
  boxSizing: "border-box",
  overflow: "scroll",
  "&::-webkit-scrollbar": {
    display: "none",
  },
}));

const AnalysisResults: React.FC = () => {
  const { way } = useParams<{
    way: string;
  }>();
  const [keys, setKeys] = useState("");
  const [selected, setSelected] = useState<any>({});
  const [trendAllData, setTrendAllData] = useState<any>({});

  const { analysisResult } = useAppSelector(
    (state: RootState) => state.counter,
  );
  const wordCloudData = useMemo(
    () =>
      (analysisResult.keywordsCount = analysisResult.keywordsCount
        .sort((a: any, b: any) => b.value - a.value)
        .slice(0, 100)),
    [analysisResult.keywordsCount],
  );

  const trendData = wordCloudData
    .slice(0, 10)
    .map((ite: { name: string }) => ite.name);

  function extractTimeAndValue(
    data: { [x: string]: { [x: string]: any } },
    key: string | number,
  ) {
    const timeArray: string[] = []; // 存储时间（年份）
    const valueArray: number[] = []; // 存储对应的值
    // // 遍历 data[key] 对象
    for (const year in data[key]) {
      timeArray.push(year); // 将年份添加到时间数组
      valueArray.push(data[key][year]); // 将值添加到值数组
    }

    return { timeArray, valueArray };
  }

  const { timeArray, valueArray } = extractTimeAndValue(trendAllData, keys);
  const init = async () => {
    try {
      const param = {
        ...analysisResult.searchParam,
        keywords: trendData,
      };
      const {
        data: { code, data },
      } = await pdfDrawKeywords(param);
      if (code === 200) {
        setTrendAllData(data);
      }
    } catch (error) {
      message.error("获取趋势信息失败" + (error as Error).message);
    }
  };

  useEffect(() => {
    init();
    setKeys(trendData[0]);
  }, []);
  // 提取时间数组和值数组的函数

  useEffect(() => {
    // 生成 legend.selected 配置
    // 创建一个新的 selected 对象
    const selected: any = {};
    trendData.forEach((key: string | number) => {
      if (key === keys) {
        selected[key] = true;
      } else {
        selected[key] = false;
      }
      // 只有第一个 key 为 true
    });

    setSelected(selected);
  }, [keys]);
  const option = {
    title: {
      left: "center",
      axisLabel: {
        color: "#333",
        fontSize: 18,
      },
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "none", // 鼠标指针类型
      },
    },
    legend: {
      icon: "rect",
      itemWidth: 12, // 设置宽度，单位：px
      itemHeight: 12,
      data: trendData,
      selected,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: timeArray,
      axisLine: {
        show: false, // 隐藏 x 轴线
      },
      axisTick: {
        show: false, // 隐藏刻度线
      },
    },
    yAxis: {
      name: valueArray.length ? "次数/次" : "",
      type: "value",
      minInterval: 1,
      nameGap: 30,
      nameTextStyle: {
        padding: [0, 0, 0, 10], // 向右移动名称
      },
    },
    grid: {
      left: "1%",
      right: "1%",
      bottom: "5%",
      top: "20%",
      containLabel: true,
    },
    series: trendData.map((key: string | number) => ({
      name: key,
      type: "line",
      data: valueArray, // 值数组
      // smooth: true,
      // areaStyle: {
      //   opacity: 0.5,
      // },
    })),
  };

  const options = {
    tooltip: {
      show: true,
    },
    grid: {
      left: "1%",
      top: "10%",
      right: "5%",
      bottom: "5%",
      containLabel: true, // 确保图表不会超出grid
    },
    legend: {
      show: true,
      icon: "circle",
      orient: "horizontal",
      top: "90.5%",
      right: "center",
      itemWidth: 16.5,
      itemHeight: 6,
    },
    xAxis: [
      {
        data: analysisResult.relevantAuthor.map(
          (item: { name: string }) => item.name,
        ),
        name: "作者",
        axisLabel: {
          show: false,
          margin: 30,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
          width: 0.08,
          lineStyle: {
            type: "solid",
            color: "#03202E",
          },
        },
      },
    ],
    yAxis: [
      {
        name: "资料数量/篇",
        nameGap: 30,
        nameTextStyle: {
          padding: [0, 0, 0, 40], // 向右移动名称
        },
        minInterval: 1,
        splitLine: {
          show: false,
          lineStyle: {
            color: "#eee",
            type: "solid",
          },
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          align: "right",
        },
      },
    ],
    series: [
      {
        name: "",
        type: "pictorialBar",
        symbolSize: [40, 10],
        symbolOffset: [0, 6],
        color: "#14b1eb",
        z: 12,
        data: analysisResult.relevantAuthor.map(
          (item: { value: number }) => item.value,
        ),
        label: {
          show: true,
          position: "top",
          formatter: "{c}",
        },
      },
      {
        name: "",
        type: "bar",
        barWidth: 40,
        itemStyle: {
          color: "#14b1eb",
          opacity: 0.8,
        },
        barCategoryGap: "50%",
        data: analysisResult.relevantAuthor.map(
          (item: { value: number }) => item.value,
        ),
      },
      {
        name: "",
        type: "pictorialBar",
        symbolSize: [40, 10],
        symbolOffset: [0, -6],
        z: 12,
        symbolPosition: "end",
        color: "#14b1eb",
        data: analysisResult.relevantAuthor.map(
          (item: { value: number }) => item.value,
        ),
      },
    ],
  };

  const processWord = (word: string, maxLength = 40) =>
    word.length > maxLength ? word.substring(0, maxLength) + "..." : word;

  const processedData = wordCloudData.map(
    (item: { name: string; value: any }, index: any) => ({
      // 显示用名称（超长会被截断加省略号）
      name: processWord(item.name),
      // 保留原始完整名称
      originalName: item.name,
      value: item.value,
      // 标记是否被截断
      isTruncated: item.name.length > 40,
      isTop: index,
    }),
  );

  // 3. 处理数据
  const optionKey = useMemo(
    () => ({
      tooltip: {
        trigger: "item",
        confine: true, // 确保 tooltip 不超出容器
        extraCssText:
          "max-width: 300px; white-space: normal; word-break: break-all;",
        formatter(params: {
          data: { originalName: string; isTruncated: boolean; isTop: number };
        }) {
          return `
            <div class="tooltip-title">${params.data.originalName}</div>`;
        },
      },
      series: [
        {
          type: "wordCloud",
          shape: "square",
          width: "100%",
          height: "100%",
          sizeRange: [20, 80],
          rotationRange: [0, 0],
          rotationStep: 10,
          gridSize: 10,
          drawOutOfBound: false,
          textStyle: {
            fontFamily: "Arial",
            fontWeight: "bold",
            color() {
              return (
                "rgb(" +
                [
                  Math.round(Math.random() * 160 + 64),
                  Math.round(Math.random() * 160 + 64),
                  Math.round(Math.random() * 160 + 64),
                ].join(",") +
                ")"
              );
            },
          },
          emphasis: {
            focus: "self",
            textStyle: {
              shadowBlur: 10,
              shadowColor: "#333",
            },
          },
          data: processedData,
        },
      ],
    }),
    [wordCloudData],
  );
  const journalOption = {
    grid: {
      left: "1%",
      right: "1%",
      bottom: "1%",
      top: "1%",
      containLabel: true,
    },
    xAxis: {
      show: false,
      type: "value",
    },
    textColor: "#000",
    yAxis: [
      {
        type: "category",
        inverse: true,
        axisLabel: {
          show: true,
          color: "rgba(25, 52, 65, 1)",
          // formatter(value: string) {
          //   // 缩短名称或使用缩写
          //   return value.length > 20 ? value.substring(0, 20) + "..." : value;
          // },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        triggerEvent: true,
        data: analysisResult.journalCount.map((item: any) => item.name),
      },
      {
        type: "category",
        inverse: true,
        axisTick: "none",
        axisLine: "none",
        show: true,
        axisLabel: {
          color: "rgba(25, 52, 65, 1)",
          fontSize: "12",
          formatter(value: number) {
            const number: number[] = analysisResult.journalCount.map(
              (item: any) => item.value,
            );
            const sum = number.reduce((acc, num) => acc + num, 0);
            const account = Math.floor((value / sum) * 100);
            return account + "%";
          },
        },
        data: analysisResult.journalCount.map((item: any) => item.value),
      },
    ],
    series: [
      {
        type: "bar",
        zlevel: 1,
        itemStyle: {
          borderRadius: 18,
          color(params: { dataIndex: number }) {
            // 颜色数组
            const colorList = [
              "rgba(106,212,165)",
              "rgba(80,213,248)",
              "rgba(224,118,119)",
              "rgba(232,98,71)",
              "rgba(173,105,243)",
              "rgba(220,127,198)",
              "rgba(55,164,187)",
              "rgba(95,110,144)",
              "rgba(50,116,210)",
              "rgba(245,195,57)",
              "rgba(91,250,207)",
            ];
            return colorList[params.dataIndex % colorList.length]; // 使用模运算确保颜色循环使用
          },
        },
        barWidth: 20,
        data: Object.values(analysisResult.journalCount).splice(0, 10),
      },
    ],
  };

  const journalCountData = () => {
    const number: number[] = analysisResult.journalCount.map(
      (item: { value: number }) => item.value,
    );
    const sum = number.reduce((acc, num) => acc + num, 0);
    const percentageData: any = [];
    for (const key in analysisResult.journalCount) {
      if (percentageData.length < 10) {
        percentageData.push({
          name: analysisResult.journalCount[key].name,
          value:
            Math.floor((analysisResult.journalCount[key].value / sum) * 100) +
            "%",
        });
      }
    }

    return percentageData;
  };

  const isEmpty = (value: any) => {
    // 检查 null 或 undefined
    if (value == null) return true;
    // 检查空字符串
    if (typeof value === "string") return value.trim() === "";
    // 检查空数组
    if (Array.isArray(value)) return value.length === 0;
    // 检查空对象
    if (typeof value === "object") return Object.keys(value).length === 0;
    // 其他类型（如数字、布尔值）视为非空
    return false;
  };

  const checkAllEmpty = (
    obj: { [s: string]: unknown } | ArrayLike<unknown> | null,
  ) => {
    // 如果传入非对象（如 null 或数组），直接返回 false
    if (typeof obj !== "object" || obj === null || Array.isArray(obj)) {
      return false;
    }

    // 获取对象的所有属性值
    const values = Object.values(obj);
    // 若对象无属性，直接判定为全空
    if (values.length === 0) return false;

    // 检查是否所有值均为空
    return !values.every(isEmpty);
  };
  return (
    <Root>
      <Breadcrumb
        parent={[
          {
            name: way === "home" ? "检索" : "检索页",
            path: way === "home" ? "/paper-search" : `/${way}`,
          },
        ]}
        current={`分析结果`}
      />
      <Main>
        <HeadInfo>
          <PaperNumber>
            资料数量(篇)：<NumberStyle>{analysisResult.count} </NumberStyle>
          </PaperNumber>
          <HeadInfoItem>
            涉及学者(位)：
            <NumberStyle>{analysisResult.authorCount}</NumberStyle>
          </HeadInfoItem>
          {/* <HeadInfoItem>
            包含机构(家)：<NumberStyle>30</NumberStyle>
          </HeadInfoItem> */}
        </HeadInfo>
        <MainContent>
          <EchartsItem
            option={checkAllEmpty(trendAllData) ? option : {}}
            title={"研究趋势TOP10"}
            data={trendAllData}
            setKey={setKeys}
            keys={keys}
          ></EchartsItem>
          <EchartsItem
            option={analysisResult.journalCount.length ? journalOption : {}}
            title={"期刊分析TOP10"}
            data={journalCountData()}
          ></EchartsItem>
          <EchartsItem
            option={analysisResult.keywordsCount.length ? optionKey : {}}
            title={"热门词云"}
            data={wordCloudData}
          ></EchartsItem>
          <EchartsItem
            option={analysisResult.relevantAuthor.length ? options : {}}
            title={"相关作者TOP20"}
            data={analysisResult.relevantAuthor}
          ></EchartsItem>
        </MainContent>
      </Main>
    </Root>
  );
};

export default AnalysisResults;
