import {
  // IconButton,
  // ListItemIcon,
  // ListItemText,
  MenuItem,
  Select,
} from "@mui/material";
// import EditIcon from "@mui/icons-material/Edit";
import { RootState, useAppSelector } from "@/hooks";
import SelectFrom from "../SelectFrom";
// import Add from "@mui/icons-material/Add";
// import Remove from "@mui/icons-material/Remove";
interface Props {
  selectValue: string | number;
  selectChange: (e: any) => void;
  protocolLoading: boolean;
  loading: boolean;
}

const HeadDropDown = styled("div")(() => ({
  height: "52px",
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
  padding: "0 0 15px 0",
  margin: "0 15px 15px",
  display: "flex",
  justifyContent: "flex-end",
  alignItems: "center",
  boxSizing: "border-box",
}));

const SelectDiv = styled("div")(() => ({
  position: "relative",
}));

// const ListItemTextStyle = styled(ListItemText)(() => ({
//   width: "85px",
//   whiteSpace: "nowrap", // 禁止换行
//   overflow: "hidden", // 隐藏溢出内容
//   textOverflow: "ellipsis", // 显示省略号
//   "& span":{
//     display: "inline",
//   }
// }));

const SelectStyle = styled(Select, {
  shouldForwardProp: (prop) => prop !== "bgColor",
})<{ bgColor: boolean }>(({ bgColor }) => ({
  height: "32px",
  borderRadius: "16px",
  background: bgColor
    ? "#ccc"
    : "linear-gradient(90deg, rgba(110, 84, 227, 1) 0%, rgba(27, 130, 227, 1) 100%)",
  "& .MuiOutlinedInput-notchedOutline": {
    border: "none",
    color: "#fff",
  },
  "& .Mui-disabled": {
    cursor: "not-allowed",
    pointerEvents: "auto",
  },
  "& svg": {
    color: "#fff",
  },
}));

const Index: React.FC<Props> = ({
  selectValue,
  selectChange,
  protocolLoading,
  loading,
}) => {
  const { selectedBank } = useAppSelector((state: RootState) => state.counter);
  const [editOpen, setEditOpen] = useState<boolean>(false);
  const [selectOpen, setSelectOpen] = useState(false);
  return (
    <HeadDropDown>
      <SelectDiv>
        <SelectStyle
          labelId="demo-select-small-label"
          id="demo-select-small"
          open={selectOpen}
          onOpen={() => setSelectOpen(true)}
          onClose={() => setSelectOpen(false)}
          value={selectValue}
          onChange={selectChange}
          displayEmpty
          disabled={loading}
          bgColor={loading}
          renderValue={(selected) => {
            if (!selected) {
              return <span style={{ color: "#fff" }}>请选择</span>;
            }
            return selected as React.ReactNode;
          }}
          MenuProps={{
            anchorOrigin: {
              vertical: "bottom",
              horizontal: "right",
            },
            transformOrigin: {
              vertical: "top",
              horizontal: "right",
            },
          }}
        >
          <MenuItem value={"资料总结"}>资料总结</MenuItem>
          <MenuItem
            value={"资料对比"}
            disabled={selectedBank.externalIds.length !== 2}
          >
            资料对比
          </MenuItem>

          {/* <MenuItem key={1} onClick={() => console.log("选择了:")}>
            <ListItemTextStyle title="删除">
              {***************}
            </ListItemTextStyle>
            <ListItemIcon>
              <IconButton
                edge="end"
                aria-label="编辑"
                onClick={(e) => {
                  e.stopPropagation();
                  setEditOpen(!editOpen);
                  setSelectOpen(false);
                }}
                size="small"
              >
                <EditIcon fontSize="small" />
              </IconButton>
              <IconButton
                edge="end"
                aria-label="新增"
                onClick={(e) => {
                  e.stopPropagation();
                  setEditOpen(!editOpen);
                }}
                size="small"
              >
                <Add fontSize="small" />
              </IconButton>
              <IconButton
                edge="end"
                aria-label="删除"
                onClick={(e) => {
                  e.stopPropagation();
                  setEditOpen(!editOpen);
                }}
                size="small"
              >
                <Remove fontSize="small" />
              </IconButton>
            </ListItemIcon>
          </MenuItem> */}
          {selectedBank.externalIds.length === 1 && (
            <MenuItem value={"生成实验方案"} disabled={protocolLoading}>
              生成实验方案
            </MenuItem>
          )}
        </SelectStyle>
        {editOpen && <SelectFrom setEditOpen={setEditOpen}></SelectFrom>}
      </SelectDiv>
    </HeadDropDown>
  );
};
export default Index;
