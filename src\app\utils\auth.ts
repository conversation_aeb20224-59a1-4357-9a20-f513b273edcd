import { anyValueProps } from "@/types/common";
import Cookies from "js-cookie";

const TokenKey = `token-aihub-chat${location.port}`;
// const TokenKey = `token${location.port}`;
const RoleKey = `roleInfo`;

export const getToken = () => {
  const tokenFromCookie = Cookies.get(TokenKey);
  if (tokenFromCookie) {
    return tokenFromCookie;
  }

  const tokenFromSessionStorage = sessionStorage.getItem(TokenKey);
  if (tokenFromSessionStorage) {
    return tokenFromSessionStorage;
  }

  return null;
};

export const setToken = (token: string, keepLoggedIn: boolean) => {
  if (keepLoggedIn) {
    Cookies.set(TokenKey, token, {
      expires: 7, // 7天过期
      path: "/",
      sameSite: "strict",
    });
  } else {
    sessionStorage.setItem(TokenKey, token);
  }
};

export const removeToken = () => {
  // 移除cookie
  Cookies.remove(To<PERSON><PERSON>ey);
  // 移除sessionStorage
  sessionStorage.removeItem(TokenKey);
};

export const getRoleInfo = () => {
  const roleInfoCookie = Cookies.get(RoleKey);
  if (!roleInfoCookie) {
    return null;
  } else {
    return JSON.parse(roleInfoCookie);
  }
};

export const setRoleInfo = (roleInfo: string) => {
  Cookies.set(RoleKey, roleInfo, {
    path: "/",
    sameSite: "strict",
  });
};

export const removeRoleInfo = () => {
  Cookies.remove(RoleKey, {
    path: "/",
    sameSite: "strict",
  });
};

const checkHttpCode = (data: string | string[]) => {
  if (!data) return "";
  const index = data.indexOf("://");
  if (index === -1) {
    return `${window.location.origin}${data}`;
  }
  return data;
};

export const navigateToLogin = async () => {
  const url = localStorage.getItem("loginUrl");
  const checkUrl = url ? (url.endsWith("/") ? url : url + "/") : "";
  const loginUrl = checkHttpCode(checkUrl);
  window.location.href = loginUrl + "login";
};

export const setLoginUrl = (url: string) => {
  Cookies.set("loginUrl", url, {
    path: "/",
    sameSite: "strict",
  });
};

export const checkPermission = (role: anyValueProps) =>
  !("resourceCode" in role);
