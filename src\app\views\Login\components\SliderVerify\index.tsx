import ReactSliderVerify from "react-slider-verify";
import "./index.css";
const Note = styled("span")(() => ({
  fontSize: 12,
  fontWeight: 400,
  color: "rgba(179, 179, 179, 1)",
}));

const SilderBar = styled("div")(() => ({
  width: "100%",
  height: "100%",
  background: "#fff",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  padding: "0 26px",
  boxSizing: "border-box",
  borderRadius: 10,
}));

const Line = styled("div")(() => ({
  width: 4,
  height: 26,
  background: "rgba(217, 217, 217, 1)",
  borderRadius: 10,
}));

interface Props {
  onSuccess: () => void;
}
const SliderVerify: React.FC<Props> = ({ onSuccess }) => (
  <ReactSliderVerify
    width={325}
    height={45}
    onSuccess={onSuccess}
    tips={<Note>按住滑块滑动，拖到最右边</Note>}
    bgColor="rgba(242, 242, 242, 1)"
    barWidth={81}
    bar={
      <SilderBar>
        <Line />
        <Line />
        <Line />
      </SilderBar>
    }
  ></ReactSliderVerify>
);
export default SliderVerify;
