import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import copy from "copy-to-clipboard";
import { Tooltip } from "@mui/material";
interface Props {
  text: string;
  handleDelete?: () => void;
}

const Root = styled("div")(() => ({
  display: "flex",
  gap: "5px",
  borderTop: "1px solid rgba(201,201,201)",
  padding: "10px 0 5px 0",
  marginTop: "10px",
}));

const IconDiv = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  gap: "2px",
  ":hover": {
    cursor: "pointer",
    color: "rgba(29,88,254)",
  },
}));
const Index: React.FC<Props> = ({ text, handleDelete }) => {
  const handleSelect = (action: string) => {
    switch (action) {
      case "copyText":
        copy(text || "", {
          debug: true,
          message: "Press #{key} to copy",
        });
        message.success("复制成功");
        break;
      case "delete":
        handleDelete && handleDelete();
        break;
      default:
        break;
    }
  };

  return (
    <Root>
      <Tooltip title="复制" arrow placement="top">
        <IconDiv onClick={() => handleSelect("copyText")}>
          <ContentCopyIcon sx={{ fontSize: "14px" }} />
        </IconDiv>
      </Tooltip>
      <Tooltip title="删除" arrow placement="top">
        <IconDiv onClick={() => handleSelect("delete")}>
          <DeleteOutlineIcon sx={{ fontSize: "17px" }} />
        </IconDiv>
      </Tooltip>
    </Root>
  );
};
export default Index;
