import { ColumnProps } from "@/components/CustomTable";
import { FormColumnProps } from "@/components/DynamicForm";
import { anyValueProps } from "@/types/common";
import { Tooltip } from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import React from "react";
import { buttonGroupProps } from "@/views/KnowledgeBase/components/common";
import { PERMISSION_MENU } from "@/utils/permission";
import { ActionButtonProps } from "@/components/PaginatedTable";
import { queryGroupList } from "@/api/knowledgeBase";

const StatusDot = styled("span", {
  shouldForwardProp: (prop) => prop !== "color",
})<{ color?: string }>(({ color }) => ({
  display: "inline-block",
  width: 10,
  height: 10,
  marginRight: 5,
  borderRadius: "50%",
  marginLeft: 8,
  background: color || "none",
}));

export const showCheckButton = [3000, 4000, 5000];

export const parseStatusList = [
  {
    value: 0,
    label: "未解析",
    color: "grey",
  },
  {
    value: 1000,
    label: "排队中",
    color: "orange",
  },
  {
    value: 2000,
    label: "解析中",
    color: "rgba(255, 195, 0, 1)",
  },
  {
    value: 3000,
    label: "全部成功",
    color: "rgba(67, 207, 124, 1)",
  },
  {
    value: 4000,
    label: "部分成功",
    color: "skyblue",
  },
  {
    value: 5000,
    label: "全部失败",
    color: "rgba(255, 87, 51, 1)",
  },
];

const StatusBox: React.FC<{ status: number }> = ({ status }) => {
  const statusInfo = parseStatusList.find((el) => el.value === status);
  return (
    <Box
      sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}
    >
      <StatusDot color={statusInfo?.color} />
      <Typography>{statusInfo?.label || status}</Typography>
    </Box>
  );
};

const StatusLabel = styled("div")(() => ({
  display: "flex",
}));

const ErrorIcon = styled(ErrorOutlineIcon, {
  shouldForwardProp: (prop) => prop !== "statusColor",
})<{ statusColor: string | undefined }>(({ statusColor }) => ({
  fontSize: "20px",
  color: statusColor || "red",
  marginLeft: "5px",
}));
const TooltipTitleMain = styled("div")(() => ({
  maxWidth: 500,
  maxHeight: 100,
  paddingRight: 6,
}));

const TitleTypography = styled(Typography)(() => ({
  width: "100%",
}));

const TooltipTitle = (props: { title: string }) => {
  const { title } = props;
  return (
    <React.Fragment>
      <TooltipTitleMain>
        <TitleTypography color="inherit" title={title}>
          {title ? title : "暂无错误信息"}
        </TitleTypography>
      </TooltipTitleMain>
    </React.Fragment>
  );
};

const sourceOptions: { [key: string]: string } = {
  upload: "个人上传",
  document: "加入资料库",
};

// 我的资料库--表格columns
export const columns: ColumnProps[] = [
  {
    dataKey: "title",
    label: "标题",
    width: 150,
    ellipsis: true,
    disablePadding: true,
    render: (row: anyValueProps) => {
      const splitTitle = row.title ? row.title.replace(/\|\|\|/g, "    ") : "";
      return <span title={splitTitle}>{splitTitle.replace(/#/g, "")}</span>;
    },
  },
  // {
  //   dataKey: "doi",
  //   label: "DOI",
  //   width: 150,
  //   ellipsis: true,
  // },
  {
    dataKey: "authors",
    label: "作者",
    width: 150,
    ellipsis: true,
    render: (row: anyValueProps) => {
      const authorsArray = row.authors ? JSON.parse(row.authors) : [];
      const joinedAuthors = authorsArray?.join(",") || "";
      const authors = joinedAuthors.replace(/,\|\|\|,?|,?\|\|\|,/g, "  ");
      return <span title={authors}>{authors}</span>;
    },
  },
  // {
  //   dataKey: "journal",
  //   label: "期刊",
  //   width: 100,
  //   ellipsis: true,
  // },
  { dataKey: "publishedYear", label: "出版年", width: 80 },
  {
    dataKey: "parseStatusCode",
    label: "解析状态",
    width: 120,
    render: (row: anyValueProps) => {
      const status = row.parseStatusCode;
      const statusInfo = parseStatusList.find((el: any) => el.value === status);

      return (
        <StatusLabel>
          <StatusBox status={status} />
          {(status === 4000 || status === 5000) && (
            <Tooltip
              placement="top"
              arrow
              title={<TooltipTitle title={row.message} />}
            >
              <ErrorIcon statusColor={statusInfo?.color} />
            </Tooltip>
          )}
        </StatusLabel>
      );
    },
  },
  {
    dataKey: "updateTime",
    label: "解析时间",
    width: 150,
    sortable: true,
  },
  {
    dataKey: "source",
    label: "来源",
    width: 100,
    render: (row: anyValueProps) =>
      row.source ? sourceOptions[row.source] : "未知",
  },
];
export const taskColumns: ColumnProps[] = [
  {
    dataKey: "pdfOriginName",
    label: "PDF名称",
    width: 170,
    ellipsis: true,
    disablePadding: true,
  },
  {
    dataKey: "title",
    label: "标题",
    width: 150,
    ellipsis: true,
    disablePadding: true,
    render: (row: anyValueProps) => {
      const splitTitle = row.title ? row.title.replace(/\|\|\|/g, "    ") : "";
      return <span title={splitTitle}>{splitTitle}</span>;
    },
  },
  // { dataKey: "doi", label: "DOI", width: 150, ellipsis: true },
  {
    dataKey: "authors",
    label: "作者",
    width: 150,
    ellipsis: true,
    render: (row: anyValueProps) => {
      const authorsArray = row.authors ? JSON.parse(row.authors) : [];
      const joinedAuthors = authorsArray?.join(",") || "";
      const authors = joinedAuthors.replace(/,\|\|\|,?|,?\|\|\|,/g, "  ");
      return <span title={authors}>{authors}</span>;
    },
  },
  // {
  //   dataKey: "journal",
  //   label: "期刊",
  //   width: 100,
  //   ellipsis: true,
  // },
  { dataKey: "publishedYear", label: "出版年", width: 60 },
  { dataKey: "relationUpdateTime", label: "上传时间", width: 150 },
  {
    dataKey: "parseStatusCode",
    label: "解析状态",
    width: 100,
    render: (row: anyValueProps) => {
      const status = row.parseStatusCode;
      const statusInfo = parseStatusList.find((el: any) => el.value === status);

      return (
        <StatusLabel>
          <StatusBox status={status} />
          {(status === 4000 || status === 5000) && (
            <Tooltip
              placement="top"
              arrow
              title={<TooltipTitle title={row.message} />}
            >
              <ErrorIcon statusColor={statusInfo?.color} />
            </Tooltip>
          )}
        </StatusLabel>
      );
    },
  },
];

export const searchColumns: FormColumnProps[] = [
  {
    name: "source",
    label: "来源",
    componentType: "select",
    defaultValue: 0,
    grid: 2,
    required: false,
    options: [
      { label: "全部", value: 0 },
      { label: "加入资料库", value: "document" },
      { label: "个人上传", value: "upload" },
    ],
  },
  {
    name: "status",
    label: "解析状态",
    componentType: "select",
    defaultValue: 1,
    grid: 3,
    required: false,
    options: [
      { label: "全部", value: 1 },
      { label: "未解析", value: 0 },
      { label: "解析中", value: 2000 },
      { label: "全部成功", value: 3000 },
      { label: "全部失败", value: 5000 },
      { label: "部分成功", value: 4000 },
    ],
  },
  {
    name: "date",
    label: "解析时间",
    componentType: "date",
    grid: 3.5,
  },
  {
    name: "fuzzyQuery",
    label: "模糊查询",
    componentType: "input",
    grid: 3,
    required: false,
    placeholder: "请输入关键词",
  },
];
export const taskSearchColumns: FormColumnProps[] = [
  {
    name: "status",
    label: "解析状态",
    componentType: "select",
    defaultValue: 1,
    grid: 3,
    required: false,
    options: [
      { label: "全部", value: 1 },
      { label: "未解析", value: 0 },
      { label: "解析中", value: 2000 },
      { label: "全部成功", value: 3000 },
      { label: "全部失败", value: 5000 },
      { label: "部分成功", value: 4000 },
    ],
  },
  {
    name: "date",
    label: "上传时间",
    componentType: "date",
    grid: 3,
  },
  {
    name: "fuzzyQuery",
    label: "模糊查询",
    componentType: "input",
    grid: 3,
    required: false,
    placeholder: "请输入关键词",
  },
];

export const defalutBreadcrumb: any = {
  MyDocDB: [
    {
      name: "我的资料库",
      path: "/paper-base/MyDocDB",
    },
  ],
  TopicDocDB: [
    {
      name: "资料库",
      path: "/paper-base/TopicDocDB",
    },
  ],
  "Co.DocDB": [
    {
      name: "公司资料库",
      path: "/paper-base/Co.DocDB",
    },
  ],
};

interface SelectItemProps {
  label: string;
  value: string;
  roleName?: string;
}
export const paperFormColumns = (
  roleInfo: anyValueProps,
): FormColumnProps[] => {
  const hasResource = !!roleInfo.resourceCode;
  return [
    {
      name: "date",
      label: "更新时间",
      componentType: "date",
      grid: !hasResource ? 4 : 6,
    },
    {
      name: "fuzzyQuery",
      label: "模糊查询",
      componentType: "input",
      grid: !hasResource ? 4 : 6,
      placeholder: "请输入关键词",
    },
    ...(!hasResource
      ? [
          {
            name: "resourceCode",
            label: "课题组",
            componentType: "select" as const,
            grid: 4,
            options: defaultSelectOptions,
            componentProps: {
              loadRequest: queryGroupList,
              labelKey: "groupName",
              valueKey: "resourceCode",
              initSearch: { isAll: true },
            },
          },
        ]
      : []),
  ];
};

const defaultSelectOptions: SelectItemProps[] = [{ label: "全部", value: "1" }];

export const ButtonColor = {
  ai: "linear-gradient(90deg, rgba(110, 84, 227, 1) 0%, rgba(27, 130, 227, 1) 100%)",
  primary:
    "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
  gary: "rgba(242, 242, 242, 1)",
};

export const buttonGroup: buttonGroupProps[] = [
  {
    name: "全选",
    keyword: "selectAll",
    role: PERMISSION_MENU["normal"],
  },
  {
    name: "反向选择",
    keyword: "reverseSelectAll",
    role: PERMISSION_MENU["normal"],
  },
  {
    name: "取消选择",
    keyword: "clearSelectAll",
    role: PERMISSION_MENU["normal"],
  },
  {
    name: "删除所选",
    keyword: "deleteSelectAll",
    role: PERMISSION_MENU["edit"],
  },
  {
    name: "下载所选",
    keyword: "downloadSelectAll",
    role: PERMISSION_MENU["edit"],
  },
];

export const filterButtonGroup = (
  buttons: buttonGroupProps[],
  isShare: string | null,
): buttonGroupProps[] =>
  isShare === "true"
    ? buttons.filter(
        (btn) =>
          !["deleteSelectAll", "downloadSelectAll"].includes(btn.keyword),
      )
    : buttons;

export const shareOperations: ActionButtonProps[] = [
  {
    role: PERMISSION_MENU["query"],
    type: "查看",
  },
  {
    role: PERMISSION_MENU["query"],
    type: "预览",
  },
];
export const documentOperations: ActionButtonProps[] = [
  {
    role: PERMISSION_MENU["query"],
    type: "查看",
  },
  {
    role: PERMISSION_MENU["query"],
    type: "预览",
  },
  {
    role: PERMISSION_MENU["edit"],
    type: "编辑",
  },
  {
    role: PERMISSION_MENU["edit"],
    type: "删除",
  },
  {
    role: PERMISSION_MENU["edit"],
    type: "下载",
  },
  {
    role: PERMISSION_MENU["edit"],
    type: "重试",
  },
];

export const taskOperations: ActionButtonProps[] = [
  {
    role: PERMISSION_MENU["query"],
    type: "查看",
  },
  {
    role: PERMISSION_MENU["query"],
    type: "预览",
  },
  {
    role: PERMISSION_MENU["edit"],
    type: "编辑",
  },
  {
    role: PERMISSION_MENU["edit"],
    type: "删除",
  },
  {
    role: PERMISSION_MENU["edit"],
    type: "重试",
  },
];
