import { getFile, getPdfParagraph } from "@/api/paperSearch";
// import PdfView from "@/components/PdfView";
// import { useLocation } from "react-router-dom";
// import { useAppDispatch } from "@/hooks";
// import { clearData } from "@/store/pdfSlice";
// import { anyValueProps } from "@/types/common";
import { CircularProgress } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import SwitchStyle from "@/assets/switch-style.svg";
import { chatUpdate, getPdfIds } from "@/api/chat";
import ArticleItem from "@/views/PaperSearch/components/ArticleItem";
import { useSelector } from "react-redux";
import { RootState, useAppDispatch } from "@/hooks";
import {
  setIsFirst,
  setPreviewItem,
  setSelectedBank,
} from "@/store/counterSlice";
import PDFVirtualList from "@/components/PdfVirtualList";
// interface BoxStyleProps {
//   width: number;
//   height: number;
//   left: number;
//   top: number;
// }

interface PdfDataListProps {
  coordsData: any[];
  pdfText: string;
  pdfUrl: string;
}

const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
  flexDirection: "column",
}));

const Main = styled("div", {
  shouldForwardProp: (prop) => prop !== "isListStyle",
})<{ isListStyle: boolean }>(({ isListStyle }) => ({
  flex: 1,
  borderRadius: isListStyle ? "20px" : "0px 20px 20px 20px",
  padding: isListStyle ? "18px" : "20px 18px",
  background:
    " radial-gradient(95.28% 59.87% at 17.***************% -40.***************%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  border: "2px solid rgba(255, 255, 255, 1)",
  boxSizing: "border-box",
  height: 0,
}));

const ListStyleDiv = styled("div")(() => ({
  width: "100%",
  height: "100%",
  overflow: "scroll",
  "::-webkit-scrollbar": {
    display: "none",
  },
}));

const ListItem = styled("div")(() => ({
  opacity: 1,
  borderRadius: "20px",
  background: "rgba(255, 255, 255, 1)",
  border: " 1px solid rgba(235, 235, 235, 1)",
  marginBottom: "20px",
  padding: "24px",
  boxSizing: "border-box",
}));

const LeftBox = styled("div")(() => ({
  width: "100%",
  height: "100%",
  position: "relative",
  display: "flex",
  ".react-pdf__Document": {
    display: "flex",
    flexDirection: "column",
    position: "relative",
  },
  ".react-pdf__Page": {
    borderRadius: "5px",
    boxShadow: "0 2px 4px 0 hsla(0,0%,70.6%,.5)",
  },
}));

const TabsContainer = styled("div", {
  shouldForwardProp: (prop) => prop !== "isListStyle",
})<{
  isListStyle: boolean;
}>(({ isListStyle }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: isListStyle ? "flex-end" : "space-between",
  height: 40,
}));

const Tabs = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
}));

const LoadingBox = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
}));

const TabsItem = styled("div")<{ active: boolean }>(({ active }) => ({
  width: 84,
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  padding: "0 10px",
  background: !active
    ? "rgba(240, 240, 240, 1)"
    : "radial-gradient(57.14% 60% at 23.809523809523807% -25%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  borderRadius: "14px 14px 0 0",
  marginLeft: "-4px",
  boxShadow: !active ? "8px 0px 20px  rgba(0, 0, 0, 0.15)" : "none",
  position: "relative",
  boxSizing: "border-box",
  border: active ? "2px solid rgba(255, 255, 255, 1)" : "",
  cursor: "pointer",
  ":first-of-type": {
    marginLeft: 0,
  },
  "& div": {
    color: !active ? "rgba(143, 143, 143, 1)" : "rgba(0, 0, 0, 1)",
  },
}));

const Title = styled("div")(() => ({
  fontSize: 14,
  fontWeight: 400,
  whiteSpace: "nowrap" /* 防止文本换行 */,
  overflow: "hidden" /* 隐藏超出的内容 */,
  textOverflow: "ellipsis",
  flex: 1,
}));

const CloseButton = styled(Button)(() => ({
  minWidth: 0,
  padding: 0,
  color: "#fff",
  borderRadius: "50%",
  background: "rgba(217, 217, 217, 1)",
  marginTop: 2,
}));

const SwitchBtn = styled(Button)(() => ({
  minWidth: "32px",
  height: "32px",
  borderRadius: "10px",
  padding: 0,
  background:
    "radial-gradient(118.75% 59.38% at 25% -25%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  border: "2px solid rgba(255, 255, 255, 1)",
}));

interface PdfChatProps {
  setTextValue: (value: string) => void;
  setInputValue: (value: string) => void;
  // tabs: number[];
  setPromptType: (value: undefined | string) => void;
  inputValue: string;
  fatherLoading: boolean;
}
const PdfChat: React.FC<PdfChatProps> = ({
  setTextValue,
  setPromptType,
  setInputValue,
  // tabs,
  inputValue,
  fatherLoading,
}) => {
  const leftBoxRef = useRef<HTMLDivElement>(null);
  const { active, selectedBank, isFirst } = useSelector(
    (state: RootState) => state.counter,
  );
  const dispatch = useAppDispatch();
  // const [boxStyleParams, setBoxStyleParams] = useState<BoxStyleProps>({
  //   width: 0,
  //   height: 0,
  //   left: 0,
  //   top: 0,
  // });
  const [loading, setLoading] = useState(false);
  const [activeId, setActiveId] = useState<number>(0);
  const [listTabs, setListTabs] = useState<any[]>([]);
  const [isListStyle, setIsListStyle] = useState(false);
  const [pdfDataList, setPdfDataList] = useState<PdfDataListProps>({
    coordsData: [],
    pdfText: "",
    pdfUrl: "",
  });
  const operation = [
    { type: "explain", label: "解释", disable: fatherLoading },
    { type: "translate", label: "中文翻译", disable: fatherLoading },
    { type: "questioning", label: "去提问" },
    { type: "add", label: "添加至" },
  ];

  const getPdf = async (url: string) => {
    try {
      setLoading(true);
      const [pdfFileResponse, pdfTextResponse] = await Promise.all([
        getFile(url),
        getPdfParagraph({ pdfId: activeId }),
      ]);
      const { data: pdfFileData } = pdfFileResponse;
      const { data: pdfTextData } = pdfTextResponse;
      if (!pdfTextData) {
        throw new Error("无法获取 PDF 文本数据");
      }
      const paragraphs = pdfTextData?.data;
      const formattedText = formatTextData(paragraphs);
      const pdfBlob = new Blob([pdfFileData], { type: "application/pdf" });
      const pdfDownloadUrl = window.URL.createObjectURL(pdfBlob);
      setPdfDataList({
        coordsData: paragraphs,
        pdfText: formattedText,
        pdfUrl: pdfDownloadUrl,
      });
    } catch (error) {
      message.error(
        `获取 PDF 失败: ${error instanceof Error ? error.message : error}`,
      );
    } finally {
      setLoading(false);
    }
  };

  const formatTextData = (params: any): string => {
    const pageTextMap: Map<number, Set<string>> = new Map();
    params.forEach((item: any) => {
      if (item.frameCoords) {
        item.frameCoords.forEach((ele: any) => {
          const page = ele.page;
          if (!pageTextMap.has(page)) {
            pageTextMap.set(page, new Set());
          }
          pageTextMap.get(page)?.add(item.paragraph);
        });
      }
    });
    let str = "";
    pageTextMap.forEach((textSet) => {
      textSet.forEach((text) => {
        str += text;
      });
    });

    return str;
  };

  const getParagraph = (value: string, type: string) => {
    let newValue = value;
    switch (type) {
      case "translate":
        setPromptType("中文翻译");
        setTextValue(newValue);
        break;
      case "explain":
        setPromptType("解释");
        setTextValue(newValue);
        break;
      case "questioning":
        setPromptType(undefined);
        setInputValue(newValue.slice(0, 1000));
        break;
      case "add":
        newValue = (inputValue.length ? inputValue + "\n" : "") + value;
        setPromptType(undefined);
        setInputValue(newValue.slice(0, 1000));
        break;
      default:
        return;
    }
  };

  // const getBoxStyle = () => {
  //   if (leftBoxRef.current) {
  //     const { offsetWidth: width, offsetHeight: height } = leftBoxRef.current;
  //     const rect = leftBoxRef.current.getBoundingClientRect();
  //     // setBoxStyleParams({ width, height, left: rect.left, top: rect.top });
  //   }
  // };

  // useEffect(() => {
  //   getBoxStyle();
  //   window.addEventListener("resize", getBoxStyle);
  //   return () => {
  //     window.removeEventListener("resize", getBoxStyle);
  //   };
  // }, [pdfDataList, selectedBank.externalIds]);

  useEffect(() => {
    getPdfMsg(activeId);

    dispatch(setPreviewItem({}));
  }, [activeId]);

  useEffect(() => {
    if (selectedBank.externalIds.length && !isListStyle) {
      setActiveId(selectedBank.externalIds[0]);
      dispatch(setPreviewItem({}));
    } else {
      getPdfMsg(selectedBank.externalIds);
    }
  }, [selectedBank.externalIds, isListStyle]);
  useEffect(() => {
    setIsListStyle(false);
  }, [active]);
  const getPdfMsg = async (externalIds: number[] | number) => {
    try {
      if (!activeId) return;
      const ids = typeof externalIds === "number" ? [externalIds] : externalIds;
      const { data } = await getPdfIds(ids);
      if (data.code === 200) {
        if (!isListStyle && data.data[0]?.pdfUrl) {
          const url = data.data[0]?.pdfUrl + "&pdfId=" + activeId;
          url && getPdf(url);
        } else {
          setListTabs(data.data);
        }
      }
    } catch (error) {
      message.error(`获取 PDF信息失败,${(error as Error).message}`);
    }
  };
  const closeTab = async (deleteId: number) => {
    const externalIds = selectedBank.externalIds.filter(
      (item: number) => item !== deleteId,
    );

    if (externalIds.length === 1) {
      setIsListStyle(false);
    }
    if (active) {
      await chatUpdate({
        chatId: active,
        externalIds,
      });
    }
    dispatch(setSelectedBank({ ...selectedBank, externalIds }));
    dispatch(setIsFirst(!isFirst));
    dispatch(setPreviewItem({}));
  };

  const handlePreview = (item: any) => {
    dispatch(setPreviewItem(item));
  };
  return (
    <Root>
      {selectedBank.externalIds.length > 1 && (
        <TabsContainer isListStyle={isListStyle}>
          {!isListStyle && (
            <Tabs>
              {selectedBank.externalIds.map((tab: any, index: number) => (
                <TabsItem
                  key={index}
                  active={activeId === tab}
                  onClick={() => {
                    setActiveId(tab);
                  }}
                  style={{
                    zIndex:
                      activeId === tab
                        ? 100
                        : selectedBank.externalIds.length - index,
                  }}
                >
                  <Title>资料-{index + 1}</Title>
                  <CloseButton
                    onClick={(e) => {
                      e.stopPropagation();
                      closeTab(tab);
                    }}
                  >
                    <CloseIcon style={{ fontSize: "14px" }} />
                  </CloseButton>
                </TabsItem>
              ))}
            </Tabs>
          )}

          <SwitchBtn onClick={() => setIsListStyle(!isListStyle)}>
            <img src={SwitchStyle} alt="" style={{ transform: "scale(0.7)" }} />
          </SwitchBtn>
        </TabsContainer>
      )}

      <Main
        isListStyle={selectedBank.externalIds.length === 1 ? true : isListStyle}
      >
        {!isListStyle ? (
          <LeftBox id="leftbox" ref={leftBoxRef}>
            {pdfDataList.pdfUrl && !loading ? (
              // <PdfView
              //   width={boxStyleParams.width}
              //   file={pdfDataList.pdfUrl}
              //   height={boxStyleParams.height}
              //   operation={operation}
              //   pdfData={pdfDataList.coordsData}
              //   getParagraph={getParagraph}
              //   left={boxStyleParams.left}
              //   top={boxStyleParams.top}
              // />
              <PDFVirtualList
                url={pdfDataList.pdfUrl}
                coordsData={pdfDataList.coordsData}
                getParagraph={getParagraph}
                operation={operation}
              />
            ) : (
              <LoadingBox>
                <CircularProgress color="inherit" />
                <Typography variant="h4" sx={{ ml: 2 }}>
                  PDF加载中...
                </Typography>
              </LoadingBox>
            )}
          </LeftBox>
        ) : (
          <ListStyleDiv>
            {listTabs.map((item, index) => (
              <ListItem key={index}>
                <ArticleItem
                  articleInfo={{
                    title: item.title,
                    authors: item.authors ? JSON.parse(item.authors) : "",
                    keywords: item.keywords ? JSON.parse(item.keywords) : "",
                    paragraph: item.firstPara,
                  }}
                  isTextTitle={true}
                  preview={true}
                  del={true}
                  closeTab={() => closeTab(item.id)}
                  handlePreview={() => handlePreview(item)}
                />
              </ListItem>
            ))}
          </ListStyleDiv>
        )}
      </Main>
    </Root>
  );
};
export default PdfChat;
