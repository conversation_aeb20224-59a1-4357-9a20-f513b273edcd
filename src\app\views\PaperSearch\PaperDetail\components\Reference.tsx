import { anyValueProps } from "@/types/common";
// import ArticleItem from "../../components/ArticleItem";
interface ReferenceProps {
  list: anyValueProps[];
  language: string;
}

const Root = styled("div")(({ theme }) => ({
  background: "#fff",
  paddingTop: theme.spacing(2),
}));

const BoxTitle = styled("div")(() => ({
  fontSize: 18,
  fontWeight: 700,
  lineHeight: "18px",
  color: "rgba(31, 31, 31, 1)",
  marginBottom: 14,
}));

// const ItemStyle = styled("div")(() => ({
//   // marginBottom: "10px",
//   width: "100%",
//   height: "100%",
//   // minHeight: 140,
//   background: "rgba(245, 245, 245, 1)",
//   borderRadius: 14,
//   display: "flex",
//   alignContent: "center",
//   justifyContent: "center",
//   boxSizing: "border-box",
//   padding: "12px 12px 10px",
// }));

const ItemStyle = styled("div")(() => ({
  marginBottom: "10px",
}));

const PaperBox = styled("div")(() => ({
  position: "relative",
  minHeight: 200,
  maxHeight: 500,
  overflow: "auto",
}));

const NodataDiv = styled("div")(() => ({
  color: "#67676A",
  position: "absolute",
  left: "50%",
  top: "50%",
  transform: "translate(-50%,-50%)",
}));

const Title = styled("div")(() => ({
  fontSize: 18,
  fontWeight: 500,
}));

const Reference: React.FC<ReferenceProps> = ({ list }) => (
  // const toViewDetail = (item: anyValueProps) => {
  //   window.console.log(item);
  // };
  <Root>
    <BoxTitle>参考资料 ({list.length})</BoxTitle>
    <PaperBox>
      {list.length ? (
        // <Grid
        //   container
        //   spacing={{ xs: 1, md: 3 }}
        //   columns={{ xs: 4, sm: 8, md: 8 }}
        // >
        //   {list.map((item, index) => (
        //     <Grid size={{ xs: 2, sm: 4, md: 4 }} key={index}>
        //       <ItemStyle>
        //         <SvgIcon sx={{ width: 16, height: 18 }}>
        //           <svg
        //             xmlns="http://www.w3.org/2000/svg"
        //             width="16"
        //             height="18"
        //             viewBox="0 0 16 18"
        //             fill="none"
        //           >
        //             <path
        //               d="M10.6263 2.75121C10.6263 4.06846 11.7599 5.14012 13.154 5.14012L13.154 5.1401L16 5.1401L16 16.5468C16 17.3495 15.3113 18 14.462 18L1.5384 18C0.688709 18 0 17.3495 0 16.5468L0 1.45424C0 0.651501 0.688673 0 1.5384 0L10.6263 0L10.6263 2.75121ZM11.6155 2.75121L11.6155 3.52349e-05L16 4.20446L13.154 4.20446C12.3042 4.20446 11.6155 3.55395 11.6155 2.75121ZM2.53738 8.52759L13.462 8.52759L13.462 7.5534L2.53738 7.5534L2.53738 8.52759ZM2.53738 11.6451L13.462 11.6451L13.462 10.671L2.53738 10.671L2.53738 11.6451ZM2.53738 14.7627L13.462 14.7627L13.462 13.7885L2.53738 13.7885L2.53738 14.7627Z"
        //               fillRule="evenodd"
        //               fill="#0068B1"
        //             ></path>
        //           </svg>
        //         </SvgIcon>
        //         <Box
        //           sx={{
        //             flex: 1,
        //             ml: "10px",
        //             display: "flex",
        //             flexDirection: "column",
        //             justifyContent: "space-between",
        //           }}
        //         >
        //           <Box sx={{ flex: 1 }}>
        //             <ArticleItem
        //               articleInfo={item}
        //               addArt={true}
        //               isTextTitle={item.externalId ? false : true}
        //               isFontSize={true}
        //             />
        //           </Box>
        //         </Box>
        //       </ItemStyle>
        //     </Grid>

        //   ))}
        // </Grid>
        list.map((item) => (
          <ItemStyle key={item.biblId}>
            {item.isLink ? (
              <Title
                sx={{ color: "#0000FF", cursor: "pointer" }}
                // onClick={() => toViewDetail(item)}
              >
                {item.title}
              </Title>
            ) : (
              <Title style={{ display: "flex" }}>
                {/* {language !== "zh" && (
                  <div style={{ marginRight: 5 }}>[{index + 1}]</div>
                )} */}
                <div>{item.rawReference}</div>
                {/* {`[${index + 1}]` + item.rawReference} */}
              </Title>
            )}
            {/* <AuthorMsg>
              {item.isLink && (
                <div>
                  <Button>+收藏</Button>
                  <Button>+知识库</Button>
                </div>
              )}
            </AuthorMsg> */}
          </ItemStyle>
        ))
      ) : (
        <NodataDiv>暂无数据</NodataDiv>
      )}
    </PaperBox>
  </Root>
);
export default Reference;
