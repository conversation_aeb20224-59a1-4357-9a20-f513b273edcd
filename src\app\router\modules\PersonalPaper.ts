import { RouteProps } from "..";
import paperIcon from "@/assets/paperbase.svg";
import UploadRecords from "@/views/PaperBase/components/PersonalPaper/components/UploadRecords";
import PersonalPaper from "@/views/PaperBase/components/PersonalPaper";
import PaperBase from "@/views/PaperBase";
import PaperDetail from "@/views/PaperSearch/PaperDetail";

export const personalRoute: Array<RouteProps> = [
  // {
  //   path: "/personal-paper/",
  //   name: "personal-paper",
  //   description: "资料库",
  //   lazyComponent: () => import("../../views/PersonalPaper"),
  //   components: PersonalPaper,
  //   hidden: false,
  //   icon: paperIcon,
  //   children: [
  //     {
  //       path: "/personal-paper/upload-records",
  //       name: "upload-records",
  //       description: "上传记录",
  //       hidden: true,
  //       lazyComponent: () =>
  //         import("../../views/PersonalPaper/components/UploadRecords"),
  //       components: UploadRecords,
  //     },
  //     {
  //       path: "/personal-paper/:taskId",
  //       name: "paper-detail",
  //       description: "资料任务详情",
  //       hidden: true,
  //       lazyComponent: () => import("../../views/PersonalPaper"),
  //       components: PersonalPaper,
  //     },
  //   ],
  // },
  {
    path: "/paper-base",
    name: "paper-base",
    description: "资料库",
    lazyComponent: () => import("@/views/PaperBase"),
    components: PaperBase,
    hidden: false,
    icon: paperIcon,
    menuCode: "DocDB",
    children: [
      {
        path: "/paper-base/:type",
        name: "paper-base-type",
        description: "",
        hidden: true,
        lazyComponent: () => import("@/views/PaperBase"),
        components: PaperBase,
      },
      {
        path: "/paper-base/:type/personal-paper/:documentId",
        name: "personal-paper",
        description: "资料库详细",
        hidden: true,
        lazyComponent: () =>
          import("../../views/PaperBase/components/PersonalPaper"),
        components: PersonalPaper,
      },
      {
        path: "/paper-base/:type/upload-records",
        name: "upload-records",
        description: "上传记录",
        hidden: true,
        lazyComponent: () =>
          import(
            "../../views/PaperBase/components/PersonalPaper/components/UploadRecords"
          ),
        components: UploadRecords,
      },
      {
        path: "/paper-base/:type/upload-records/:taskId",
        name: "upload-records",
        description: "上传记录详情",
        hidden: true,
        lazyComponent: () =>
          import("../../views/PaperBase/components/PersonalPaper"),
        components: PersonalPaper,
      },
      {
        path: "/paper-base/paper-details/:way",
        name: "paper-details",
        description: "",
        hidden: false,
        lazyComponent: () => import("../../views/PaperSearch/PaperDetail"),
        components: PaperDetail,
      },
    ],
  },
];
