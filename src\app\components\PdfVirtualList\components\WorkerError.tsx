import React from "react";

interface WorkerErrorProps {
  error: string;
  onRetry?: () => void;
}

export const WorkerError: React.FC<WorkerErrorProps> = ({ error, onRetry }) => {
  const handleRefresh = () => {
    window.location.reload();
  };

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      handleRefresh();
    }
  };

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "400px",
        flexDirection: "column",
        padding: "40px",
        textAlign: "center",
        backgroundColor: "#f8f9fa",
        border: "1px solid #dee2e6",
        borderRadius: "8px",
        margin: "20px",
      }}
    >
      <div
        style={{
          fontSize: "48px",
          marginBottom: "20px",
        }}
      >
        ⚠️
      </div>
      
      <h3
        style={{
          color: "#dc3545",
          marginBottom: "16px",
          fontSize: "18px",
          fontWeight: "600",
        }}
      >
        PDF Worker 加载失败
      </h3>
      
      <p
        style={{
          color: "#6c757d",
          marginBottom: "24px",
          fontSize: "14px",
          lineHeight: "1.5",
          maxWidth: "400px",
        }}
      >
        {error}
      </p>
      
      <div style={{ display: "flex", gap: "12px", flexWrap: "wrap", justifyContent: "center" }}>
        <button
          onClick={handleRetry}
          style={{
            padding: "10px 20px",
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "14px",
            fontWeight: "500",
            transition: "background-color 0.2s",
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = "#0056b3";
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = "#007bff";
          }}
        >
          重试
        </button>
        
        <button
          onClick={handleRefresh}
          style={{
            padding: "10px 20px",
            backgroundColor: "#6c757d",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "14px",
            fontWeight: "500",
            transition: "background-color 0.2s",
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = "#545b62";
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = "#6c757d";
          }}
        >
          刷新页面
        </button>
      </div>
      
      <details
        style={{
          marginTop: "24px",
          maxWidth: "500px",
          width: "100%",
        }}
      >
        <summary
          style={{
            cursor: "pointer",
            color: "#6c757d",
            fontSize: "12px",
            marginBottom: "8px",
          }}
        >
          故障排除建议
        </summary>
        <div
          style={{
            textAlign: "left",
            fontSize: "12px",
            color: "#6c757d",
            lineHeight: "1.4",
            padding: "12px",
            backgroundColor: "#ffffff",
            border: "1px solid #dee2e6",
            borderRadius: "4px",
          }}
        >
          <p><strong>可能的解决方案：</strong></p>
          <ul style={{ margin: "8px 0", paddingLeft: "20px" }}>
            <li>检查网络连接是否正常</li>
            <li>清除浏览器缓存后重试</li>
            <li>确认 PDF Worker 文件路径正确</li>
            <li>尝试使用其他浏览器</li>
            <li>联系技术支持获取帮助</li>
          </ul>
          <p style={{ marginTop: "12px" }}>
            <strong>技术信息：</strong><br />
            Worker 路径: {(window as any).pdfjs?.GlobalWorkerOptions?.workerSrc || "未设置"}
          </p>
        </div>
      </details>
    </div>
  );
};

export default WorkerError;
