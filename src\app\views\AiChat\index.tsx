import React, { useEffect, useState } from "react";
import { styled, Box, SelectChangeEvent } from "@mui/material";
import Footer from "./components/Footer";
import Message from "./components/Message";
import {
  deleteDetailChat,
  genExpstep,
  getChatDetailById,
  getChatHistoryDetail,
  getNewChatId,
  stopChat,
  templateSaveDataId,
} from "@/api/chat";
import {
  addCurrentChat,
  deleteCurrentChat,
  setActives,
  setIsFirst,
  setPreviewItem,
  changeValue,
  fetchStreamDataThunk,
  deleteCurrentLocalStorageChat,
} from "../../store/counterSlice";
import chatSvg from "@/assets/chat.svg";
import useScroll from "@/hooks/useScroll";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import SideBar from "./components/SideBar";
import SelectDialog from "./components/SelectDialog";
import Breadcrumb from "@/components/Breadcrumb";
import { leftHistoryListProps } from "./components/common";
import PdfChat from "./components/PdfChat";
import Preview from "./components/Preview";
import PdfDrawer from "../PaperBase/components/PersonalPaper/components/PdfDrawer";
import { anyValueProps } from "@/types/common";
import { getFile } from "@/api/paperSearch";
import PaperInfoList from "./components/PaperInfoList";
import StopButton from "./components/StopButton";
import SelectMenu from "./components/SelectMenu";
const Root = styled(Box)(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
  flexDirection: "column",
  boxSizing: "border-box",
}));
const RootDiv = styled("div")(() => ({
  flex: 1,
  display: "flex",
  padding: "18px 41px",
  boxSizing: "border-box",
  height: 0,
  columnGap: "10px",
}));
const PdfDiv = styled("div")(() => ({
  width: "40%",
  marginRight: "10px",
}));
const ChatRoot = styled(Box)(() => ({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  width: 0,
}));

const Mains = styled("div")(() => ({
  flex: 1,
  padding: "15px 0",
  boxSizing: "border-box",
  background:
    "radial-gradient(60.56% 59.81% at 17.077464788732392% -40.0709219858156%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  opacity: 1,
  border: "2px solid rgba(255, 255, 255, 1)",
  borderRadius: "20px",
  height: 0,
  display: "flex",
  flexDirection: "column",
}));
const Main = styled("div")(() => ({
  flex: 1,
  overflowY: "auto",
  "&::-webkit-scrollbar": {
    display: "none",
  },
}));

const MainContent = styled("div")(() => ({
  margin: "0 38px",
}));
const Text = styled("div")(() => ({
  display: "flex",
  justifyContent: "center",
}));

const MessageText = styled("div")(() => ({
  lineHeight: "30px",
  padding: "20px 0 0 0",
}));

const MessageTextBtn = styled("span")(() => ({
  margin: "0 10px",
  color: "rgba(24, 112, 199, 1)",
  cursor: "pointer",
}));
const MessageDiv = styled("div")(() => ({}));

const TimeDate = styled("div")(() => ({
  fontSize: "12px",
  color: "rgba(169,169,169)",
}));

const ResetDiv = styled("div")(() => ({
  border: "1px solid rgba(243, 237, 237, 1)",
  margin: "10px 0",
  position: "relative",
  marginTop: "20px",
}));

const ResetDivText = styled("div")(() => ({
  position: "absolute",
  left: "50%",
  top: "-8px",
  fontSize: "12px",
  backgroundColor: "#fff",
  transform: "translateX(-50%)",
  padding: "0 10px",
  boxSizing: "border-box",
  color: "#938e8e",
}));

const AiChat: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [protocolLoading, setProtocolLoading] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>("");
  const [textValue, setTextValue] = useState<string>("");
  const dispatch = useAppDispatch();
  const {
    appSetting,
    selectedBank,
    isFirst,
    previewItem,
    active,
    currentChat,
  } = useAppSelector((state: RootState) => state.counter);
  const {
    scrollToBottom,
    scrollRef,
    isScrollingRef,
    isScrolling,
    setIsScrolling,
  } = useScroll();
  const [previewOpen, setPreviewOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [previewTitle, setPreviewTitle] = useState<string>("");
  const [pdfUrl, setPdfUrl] = useState<string>("");
  const [detailId, setDetailId] = useState<string>("");
  const [currentPaperItemId, setCurrentPaperItemId] = useState<number>(0);
  const [status, setStatus] = useState<number>(0);
  const [open, setOpen] = useState<boolean>(false);
  const [historyData, setHistoryData] = useState<leftHistoryListProps[]>([]);
  const [selectValue, setSelectValue] = useState<number | string>("");
  const [promptType, setPromptType] = useState<undefined | string>(undefined);
  const [taskPromptValue, setTaskPromptValue] = useState<string>("");
  const [setting, setSetting] = useState<anyValueProps>();
  const controllerRef = useRef(new AbortController());
  const controller = controllerRef.current;
  const userRole = useAppSelector((state) => state.user.roleOption);
  const newCurrentChat = currentChat.filter((item) => item.active === active);

  const activeRef = useRef(active);
  const typeRef = useRef(selectedBank.bankType);
  const externalIdsRef = useRef(selectedBank.externalIds);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const apiTimerRef = useRef<NodeJS.Timeout | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);
  const [paperData, setPaperData] = useState<anyValueProps[]>([]);
  const [templateId, setTemplateId] = useState<string>("");
  const [aiChemUrl, setAiChemUrl] = useState<string>("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const handleClickOpen = () => {
    setOpen(true);
  };
  const dataList = useMemo(
    () => [...historyData, ...newCurrentChat],
    [historyData, newCurrentChat],
  );

  useEffect(() => {
    typeRef.current = selectedBank.bankType;
    externalIdsRef.current = selectedBank.externalIds;
  }, [selectedBank]); // 依赖项为 selectedBank
  const getChatHistory = async (isDefault?: boolean) => {
    try {
      if (active) {
        const {
          data: { data },
        } = await getChatHistoryDetail(active);
        const isProtocol = data.filter(
          (item: { status: number }) => item.status === 7,
        );
        const dataLast = data.length && data[data.length - 1];
        const { status, id, question, answer } = dataLast;
        if (dataLast && !isDefault && !isProtocol.length) {
          setDetailId(id);
          if (status === 4 || status === 5) {
            const newData = data.slice(0, -1);
            setHistoryData(newData);
            onConversation(question, id, answer, undefined);
          } else {
            setSelectValue("");
            setLoading(false);
            setHistoryData(data);
          }
        } else if (isProtocol.length && !isDefault) {
          setHistoryData(data);
          setDetailId(isProtocol[0].id);
          setStatus(isProtocol[0].status);
          setProtocolLoading(true);
        } else if (isDefault) {
          setHistoryData(data);
          setDetailId("");
        } else {
          setHistoryData([]);
        }
      }
    } catch (error) {
      message.error(`获取聊天记录失败${(error as Error)?.message}`);
    }
  };
  const getCurrent = async () => {
    try {
      if (!active || !detailId) return;
      const {
        data: { data },
      } = await getChatDetailById({ id: detailId });

      setStatus(data.status);
      setHistoryData((prevNumbers) =>
        prevNumbers.map((item) =>
          item.status === 7 ? { ...data, isLocalStorage: true } : item,
        ),
      );
    } catch (error) {
      message.error((error as Error).message);
    }
  };

  // 统一管理定时器的方法
  const startPolling = () => {
    stopPolling(); // 每次启动前先清除
    intervalRef.current = setInterval(getCurrent, 10000);
  };

  const stopPolling = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
      if (activeRef.current === active && !loading) {
        getChatHistory();
        dispatch(setIsFirst(!isFirst));
        setProtocolLoading(false);
      }
    }
  };

  useEffect(() => {
    if (status === 7) {
      startPolling();
    } else {
      stopPolling();
    }
    return () => {
      stopPolling();
    };
  }, [status]);
  // 生成实验方案模板id
  const createTemplateSaveDataId = async (key: string) => {
    try {
      const {
        data: { data, code },
      } = await templateSaveDataId(key);
      const isUrl = data.indexOf("://");
      if (code === 200) {
        if (isUrl === -1) {
          setTemplateId(data);
        } else {
          setAiChemUrl(data);
        }
      }
    } catch (error) {
      message.error((error as Error).message);
    }
  };

  const getChatId = async () => {
    const {
      data: { data },
    } = await getNewChatId();

    dispatch(setActives(data));
  };
  useEffect(() => {
    if (active === null) {
      getChatId();
    }
    createTemplateSaveDataId("ai.gen.task.template");
    createTemplateSaveDataId("ai.gen.jump.url");
    scrollToBottom();
    dispatch(deleteCurrentChat(active));
    return () => {
      controllerRef.current.abort();
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      if (apiTimerRef.current) {
        clearTimeout(apiTimerRef.current);
      }
    };
  }, []);
  useEffect(() => {
    activeRef.current = active; // 同步最新 active 到 ref
    setLoading(false);
    setInputValue("");
    dispatch(deleteCurrentChat(active));
    setPreviewOpen(false);
    dispatch(setPreviewItem({}));
    setSelectValue("");
    setPromptType(undefined);
    setTaskPromptValue("");
    setExpandedIndex(null);
    getCurrent();
    getChatHistory();

    requestAnimationFrame(() => {
      scrollToBottom();
    });
    return () => {
      controller.abort();
      controllerRef.current = new AbortController();
    };
  }, [active]);

  useEffect(() => {
    const data = historyData[historyData.length - 1];
    if (data) {
      ChangeSettingValue("temperature", data.temperature);
      ChangeSettingValue("normal", data.retrievalThreshold * 100);
    }
  }, [historyData]);
  const handleStop = async (item?: any) => {
    const { thinkText, answer, chatDetailId, progressText } =
      newCurrentChat[0] || {};
    const newAnswer = progressText + (thinkText || "") + (answer && answer);
    controller.abort();
    controllerRef.current = new AbortController(); // 重置控制器
    const param = {
      chatId: active,
      ...(!item.id ? { answer: newAnswer } : {}),
      ...(item.id ? { chatDetailId: item.id } : { chatDetailId }),
    };
    setSelectValue("");
    try {
      const {
        data: { code },
      } = await stopChat(param);
      if (code === 200) {
        setLoading(false);
        dispatch(deleteCurrentChat(active));
        setStatus(0);
        if (!item.id) {
          getChatHistory(true);
          scrollToBottom();
        }
      }
    } catch (error) {
      message.error("停止失败" + (error as Error).message);
    }
  };

  const onclose = () => {
    setLoading(false);
    if (activeRef.current === active) {
      getChatHistory(true);
    }
    dispatch(setIsFirst(!isFirst));
    dispatch(deleteCurrentChat(active));
    setSelectValue("");
  };

  const onerror = () => {
    dispatch(deleteCurrentChat(active));
    setLoading(false);
    getChatHistory(true);
    setSelectValue("");
    scrollToBottom();
  };

  const getUniformTimestamp = (date = new Date()) => {
    const options = {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    } as const;
    return date.toLocaleString("zh-CN", options).replace(/\//g, "-");
  };
  const onConversation = (
    value?: string,
    chatDetailId?: number,
    answer?: string,
    select?: string,
  ) => {
    if (!dataList.length) {
      timerRef.current = setTimeout(() => {
        dispatch(setIsFirst(!isFirst));
      }, 1000);
    }
    const date = new Date();
    dispatch(
      addCurrentChat({
        question: value ? value : inputValue,
        answer: "",
        createTime: getUniformTimestamp(),
        updateTime: getUniformTimestamp(),
        active,
        id: date,
        isLocalStorage: true,
      }),
    );
    setInputValue("");
    const { temperature, normal } = appSetting;
    const isProcessStart = answer?.match(/<chatProcess\s*[^>]*>/);
    const isProcessEnd = answer?.match(/<\s*\/chatProcess\s*>/);
    const textsProcess = isProcessEnd && answer?.split(/(?<=<\/chatProcess>)/);
    const isStart =
      textsProcess && textsProcess.length > 1
        ? textsProcess[1].match(/<think\s*[^>]*>/)
        : !isProcessStart && answer?.match(/<think\s*[^>]*>/)
          ? answer?.match(/<think\s*[^>]*>/)
          : false;
    const isEnd =
      textsProcess && textsProcess.length > 1
        ? textsProcess[1].match(/<\s*\/think\s*>/)
        : !isProcessStart && answer?.match(/<think\s*[^>]*>/)
          ? answer?.match(/<\s*\/think\s*>/)
          : false;

    const texts = isEnd && answer?.split(/(?<=<\/think>)/);
    const progressText =
      answer && !isProcessEnd
        ? answer
        : isProcessEnd && textsProcess
          ? textsProcess[0]
          : "";
    const buffer = texts
      ? texts[1]
      : isProcessEnd && !texts && textsProcess && textsProcess.length > 1
        ? textsProcess[1]
        : answer && !isProcessStart && !isStart
          ? answer
          : "";

    const thinkText =
      answer && isEnd && texts
        ? texts[0]
        : answer && isStart && !isEnd
          ? answer
          : "";

    const insideThink = isStart && !isEnd ? true : false;
    const insideProgress = isProcessStart && !isProcessEnd ? true : false;
    setLoading(true);
    const externalIds =
      selectedBank.bankType === 2
        ? selectedBank.externalIds
        : selectedBank.ids
          ? selectedBank.ids
          : [];
    const body = {
      chatId: active,
      externalIds: externalIdsRef.current
        ? externalIdsRef.current
        : externalIds,
      question: value ? value : inputValue,
      retrievalThreshold: normal / 100,
      hasHistory: select ? false : true,
      temperature,
      topK: 5,
      type: typeRef.current ? typeRef.current : 0,
      chatDetailId,
      ...(select ? { chatProcessType: select === "资料总结" ? 1 : 2 } : {}),
    };

    const text = {
      progressText,
      buffer,
      thinkText,
      insideThink,
      insideProgress,
    };
    dispatch(
      fetchStreamDataThunk({
        userRole,
        body,
        onclose,
        onerror,
        chatDetailId,
        scrollToBottom,
        isScrollingRef,
        controller,
        text,
      }),
    );
  };
  // const startRecording = async () => {
  //   recorderRef.current = Recorder({
  //     type: "wav",
  //     sampleRate: 16000,
  //     bitRate: 16,
  //   });

  //   if (recorderRef.current) {
  //     try {
  //       recorderRef.current.open(
  //         () => {
  //           window.console.log("recorder open");
  //           recorderRef.current.start();
  //           message.success("请开始描述你的问题");
  //           setIsRecording(true);
  //         },
  //         (msg: string, isUserNotAllow: any) => {
  //           message.error((isUserNotAllow ? "UserNotAllow，" : "") + msg);
  //         },
  //       );
  //     } catch (error) {
  //       message.error("无法录音:" + error);
  //     }
  //   }
  // };
  // const stopRecording = () => {
  //   if (!recorderRef.current) return;
  //   /* eslint-disable @typescript-eslint/no-unused-vars */
  //   recorderRef.current.stop(async (blob: any, __: any) => {
  //     const file = new File([blob], "recording.wav", {
  //       type: "audio/wav",
  //       lastModified: Date.now(),
  //     });
  //     const formData = new FormData();
  //     formData.append("file", file);
  //     try {
  //       const {
  //         data: { data },
  //       } = await speechRecognition(formData);
  //       setInputValue(data);
  //     } catch (error) {
  //       message.error(`语音识别失败：${error}`);
  //     }
  //     recorderRef.current.close();
  //   });
  //   setIsRecording(false);
  // };
  // const handleVoice = () => {
  //   if (isRecording) {
  //     stopRecording();
  //   } else {
  //     startRecording();
  //   }
  // };

  // pdf预览
  const viewPdf = async (item: anyValueProps) => {
    try {
      if (!item) return;
      const { pdfUrl, id } = item;
      if (!pdfUrl) return;
      if (selectedBank.bankType === 2) {
        setPreviewOpen(true);
      } else {
        setDrawerOpen(true);
      }
      const { data } = await getFile(pdfUrl + `&pdfId=${id}`);
      const pdfData = new Blob([data], { type: "application/pdf" });
      const pdfDownloadUrl = window.URL.createObjectURL(pdfData);
      setPdfUrl(pdfDownloadUrl);
      setPreviewTitle(item.title);
    } catch (error) {
      message.error("获取pdf失败" + error);
    }
  };

  useEffect(() => {
    if (!previewItem) return;
    viewPdf(previewItem);
  }, [previewItem]);

  // 生成实验方案
  const protocol = async () => {
    const externalIds =
      selectedBank.bankType === 2
        ? selectedBank.externalIds
        : selectedBank.ids
          ? selectedBank.ids
          : [];
    const param = {
      type: 2,
      question: "生成实验方案",
      chatId: active,
      externalIds,
    };

    await genExpstep(param);
  };

  const selectChange = async (event: SelectChangeEvent<unknown>) => {
    const value = event.target.value as string;
    setSelectValue(value);
    if (value !== "生成实验方案") {
      setPromptType(undefined);
      setTaskPromptValue("");
      const newValue = "请对以上" + value;
      const {
        data: { data },
      } = await getNewChatId();
      onConversation(newValue, data, undefined, value);
    } else {
      protocol();
      apiTimerRef.current = setTimeout(() => {
        getChatHistory(false);
        if (!historyData.length) {
          dispatch(setIsFirst(!isFirst));
        }
      }, 500);
      scrollToBottom();
    }
  };

  const handleDelete = async (
    id: number,
    isLocalStorage: boolean,
    index: number,
  ) => {
    if (expandedIndex && expandedIndex - 1 === index) {
      setExpandedIndex(null);
      setPaperData([]);
    }
    const lastId = historyData[index - 1]?.id;
    const note = historyData[index - 1]?.note;
    if (!isLocalStorage) {
      const param = {
        chatDetailId: id,
        ...(lastId && historyData[index].note === "reset" && note !== "reset"
          ? { lastChatDetailId: lastId }
          : {}),
      };
      try {
        const {
          data: { code },
        } = await deleteDetailChat(param);
        if (code === 200) {
          if (loading) {
            controller.abort();
            controllerRef.current = new AbortController();
            dispatch(deleteCurrentChat(active));
            getChatHistory();
          } else {
            getChatHistory(true);
          }
          message.success("删除成功");
        }
      } catch (error) {
        message.error(`${(error as Error)?.message}`);
      }
    } else {
      dispatch(deleteCurrentLocalStorageChat(id));
    }
  };

  useEffect(() => {
    setSetting(appSetting);
  }, [appSetting]);
  const ChangeSettingValue = (field: string, value: any) => {
    if (setting?.[field] !== value) {
      dispatch(changeValue({ field, value }));
    }
  };
  const getBoxHeight = () => {
    if (scrollRef.current && dropdownRef.current && dataList.length <= 1) {
      if (scrollRef.current.offsetHeight < dropdownRef.current.offsetHeight) {
        setIsScrolling(true);
      } else {
        setIsScrolling(false);
      }
    }
  };
  return (
    <Root>
      <Breadcrumb parent={[]} current={"AI对话"} />
      <RootDiv>
        <SideBar />
        {selectedBank.bankType === 2 && (
          <PdfDiv>
            <PdfChat
              inputValue={inputValue}
              setTextValue={setTextValue}
              setInputValue={setInputValue}
              setPromptType={setPromptType}
              fatherLoading={loading}
            />
          </PdfDiv>
        )}
        <ChatRoot>
          {Object.keys(previewItem).length > 0 && previewOpen ? (
            <Preview
              pdfUrl={pdfUrl}
              setPreviewOpen={setPreviewOpen}
              previewTitle={previewTitle}
            ></Preview>
          ) : (
            <>
              <Mains>
                {selectedBank.bankType === 2 && (
                  <SelectMenu
                    selectValue={selectValue}
                    selectChange={selectChange}
                    protocolLoading={protocolLoading}
                    loading={loading}
                  ></SelectMenu>
                )}

                <Main ref={scrollRef}>
                  <MainContent ref={dropdownRef}>
                    {historyData.length || newCurrentChat.length ? (
                      dataList.map((item: any, index: number) => (
                        <div key={index} style={{ marginBottom: "30px" }}>
                          <div
                            style={{
                              background: "rgba(243, 243, 243)",

                              padding: "10px 15px",
                              fontSize: "14px",
                            }}
                          >
                            <TimeDate>
                              {item.createTime && item.createTime}
                            </TimeDate>
                            <Message
                              inversion={true}
                              text={item.question}
                              dateTime={item.createTime}
                              isLocalStorage={item.isLocalStorage}
                              currentLoading={item.loading}
                              itemIndex={index + 1}
                              setExpandedIndex={setExpandedIndex}
                              expandedIndex={expandedIndex}
                              templateId={templateId}
                              aiChemUrl={aiChemUrl}
                            />
                            <Message
                              inversion={false}
                              text={item.answer}
                              dateTime={item.updateTime}
                              thinkText={item.thinkText}
                              progressText={item.progressText}
                              setDrawerOpen={setDrawerOpen}
                              setPdfUrl={setPdfUrl}
                              handleDelete={() =>
                                handleDelete(
                                  item.id,
                                  item.isLocalStorage,
                                  index,
                                )
                              }
                              additionalInfo={
                                item.additionalInfo &&
                                JSON.parse(item.additionalInfo).paragraphInfos
                              }
                              isLocalStorage={item.isLocalStorage}
                              currentLoading={item.loading}
                              loading={loading}
                              schemeLoading={
                                status && item.status === 7 ? true : false
                              }
                              count={
                                item.additionalInfo &&
                                JSON.parse(item.additionalInfo).trueFileCount
                              }
                              note={item.note}
                              setCurrentPaperItemId={setCurrentPaperItemId}
                              setHistoryData={setHistoryData}
                              itemIndex={index + 1}
                              setExpandedIndex={setExpandedIndex}
                              setPaperData={setPaperData}
                              onGetBoxHeight={getBoxHeight}
                              expandedIndex={expandedIndex}
                              stopMsg={
                                item.status === 6 &&
                                !item.answer.includes("已终止回答！")
                                  ? "已终止回答！"
                                  : ""
                              }
                              templateId={templateId}
                              aiChemUrl={aiChemUrl}
                            />
                          </div>
                          {item.note === "reset" && (
                            <ResetDiv>
                              <ResetDivText>重置聊天</ResetDivText>
                            </ResetDiv>
                          )}
                          {item.status === 7 ? (
                            <div style={{ marginTop: "20px" }}>
                              <StopButton
                                handleStop={() => handleStop(item)}
                              ></StopButton>
                            </div>
                          ) : (
                            ""
                          )}
                        </div>
                      ))
                    ) : (
                      <MessageDiv>
                        <Text
                          sx={{
                            fontSize: "18px",
                          }}
                        >
                          <img
                            src={chatSvg}
                            alt=""
                            style={{
                              width: "24px",
                              height: "24px",
                              marginRight: "5px",
                            }}
                          />
                          欢迎使用智能助手
                        </Text>
                        {selectedBank.bankType !== 2 && !selectedBank.data && (
                          <MessageText>
                            新建对话时，默认无资料库；若需选择资料库对话，请
                            <MessageTextBtn onClick={handleClickOpen}>
                              点击这里
                            </MessageTextBtn>
                          </MessageText>
                        )}
                      </MessageDiv>
                    )}
                    {loading ? (
                      <StopButton handleStop={handleStop}></StopButton>
                    ) : (
                      ""
                    )}
                  </MainContent>
                </Main>
              </Mains>
              <Footer
                setOpen={setOpen}
                setPromptType={setPromptType}
                setInputValue={setInputValue}
                onConversation={onConversation}
                setTaskPromptValue={setTaskPromptValue}
                getChatHistory={getChatHistory}
                loading={loading}
                textValue={textValue}
                inputValue={inputValue}
                historyData={historyData}
                taskPromptValue={taskPromptValue}
                promptType={promptType}
                isScrolling={!isScrolling}
                scrollToBottom={scrollToBottom}
                setExpandedIndex={setExpandedIndex}
                setPaperData={setPaperData}
              ></Footer>
            </>
          )}
        </ChatRoot>
        {expandedIndex ? (
          <PaperInfoList
            setExpandedIndex={setExpandedIndex}
            paperData={paperData}
            currentPaperItemId={currentPaperItemId}
          />
        ) : (
          ""
        )}
      </RootDiv>
      {!!open && <SelectDialog open={open} setOpen={setOpen} />}
      {!!drawerOpen && (
        <PdfDrawer
          setOpen={setDrawerOpen}
          width={800}
          title={previewTitle}
          pdfUrl={pdfUrl}
        />
      )}
    </Root>
  );
};

export default AiChat;
