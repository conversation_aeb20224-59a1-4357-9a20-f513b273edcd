import React, { useState } from "react";
import {
  styled,
  Dialog,
  DialogTitle,
  <PERSON>ton,
  DialogContent,
  DialogActions,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import Inputs from "@/components/Input";
import { fromColumns, knowledgeOptionProps } from "./common";
import { createKnowledge, updateKnowledge } from "@/api/knowledgeBase";
import LoadingButton from "@mui/lab/LoadingButton";
import { useAppDispatch } from "@/hooks";
import { editKbHistory } from "@/store/counterSlice";

interface PopUpProps {
  types: string;
  itemValue?: any;
  init: () => void;
  setKnowledgeOpen: (value: boolean) => void;
  knowledgeOption: knowledgeOptionProps[];
}
const StyledFormItem = styled("div")(() => ({}));
const TitleStyle = styled("div")(() => ({
  display: "flex",
  justifyContent: "space-between",
}));

const PopUp: React.FC<PopUpProps> = ({
  types,
  setKnowledgeOpen,
  init,
  itemValue,
}) => {
  const [searchInfo, setSearchInfo] = useState<any>({});
  const [handle, sethandle] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const handleClose = () => {
    setKnowledgeOpen(false);
  };
  const handleOk = async () => {
    sethandle(true);
    // setErrorMsg(false);
    const versionPattern = /^v\d+\.\d+(\.\d+)?$/i;
    if (types === "create") {
      try {
        const isVersionFormat = versionPattern.test(searchInfo.kb_version);
        if (searchInfo.kb_name && searchInfo.kb_version && isVersionFormat) {
          setKnowledgeOpen(false);
          const { data } = await createKnowledge(searchInfo);
          if (data.code === 200) {
            init();
            message.success("创建成功");
          }
        }
      } catch (error: any) {
        const { data } = error.response;
        const msg = "创建失败" + data.message;
        message.error(msg);
      }
    }
    if (types === "edit") {
      try {
        const editData = { ...itemValue, ...searchInfo };
        const isVersionFormat = versionPattern.test(editData.kb_version);

        if (editData.kb_name && editData.kb_version && isVersionFormat) {
          setKnowledgeOpen(false);
          const { data } = await updateKnowledge(editData);
          dispatch(editKbHistory(editData));
          if (data.code === 200) {
            init();
            message.success("编辑成功");
          } else if (
            data.message ===
            "Invalid knowledge database info, please check whether replicated or not."
          ) {
            message.error("无效的知识数据库信息，请检查是否已复制。");
          }
        }
      } catch (error: any) {
        message.error("编辑失败" + error.message);
      }
    }
  };
  const onSearchValueChange = (key: string, value: any) => {
    const tempInfo = { ...searchInfo, [key]: value };
    setSearchInfo(tempInfo);
  };

  return (
    <Dialog open={true}>
      <TitleStyle>
        <DialogTitle sx={{ fontSize: "16px", p: 2, fontWeight: 700 }}>
          {types === "create"
            ? "创建知识库"
            : types === "edit"
              ? "编辑知识库"
              : "上传资料"}
        </DialogTitle>
        <div style={{ margin: "5px 5px 0 0" }}>
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleClose}
            aria-label="close"
            size="small"
          >
            <CloseIcon />
          </IconButton>
        </div>
      </TitleStyle>

      <DialogContent
        sx={{
          px: 5,
          py: 1,
          width: "600px",
          boxSizing: "border-box",
          display: "flex",
          flexWrap: "wrap",
        }}
      >
        {fromColumns &&
          fromColumns.map((item) => (
            <StyledFormItem
              key={item.keyword}
              sx={{ width: item.type !== "textarea" ? "50%" : "100%" }}
            >
              <Inputs
                {...item}
                labelWidth={50}
                value={types === "edit" ? itemValue[item.keyword] : ""}
                placeholder={item.placeholder}
                error={
                  item.required && handle && !searchInfo[item.keyword]
                    ? item.placeholder
                    : ""
                }
                handle={handle}
                variant="outlined"
                onChangeValue={onSearchValueChange}
                size="small"
              />
            </StyledFormItem>
          ))}
      </DialogContent>

      <DialogActions>
        <Button variant="outlined" size="small" onClick={handleClose}>
          取消
        </Button>
        <LoadingButton variant="contained" size="small" onClick={handleOk}>
          确定
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
};
export default PopUp;
