body {
  width: 100%;
  height: 100%;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
#root {
  height: 100%;
  width: 100%;
}
html {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

* {
  box-sizing: content-box;
}
* {
  scrollbar-width: auto;
  scrollbar-color: auto;
}

/* * {
  box-sizing: border-box;
} */

.highlight em {
  font-style: normal;
  color: #d32f2f;
}
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
*::-webkit-scrollbar-thumb {
  background: rgb(200, 200, 200);
  border-radius: 3px;
}

.react-pdf__Page__textContent span::selection {
  background: rgba(24, 112, 199, 0.5) !important;
  color: transparent !important;
}
/* MuiPaper-elevation8 */
.MuiPopover-root
  .MuiModal-root
  .css-1i0e6f3-MuiModal-root-MuiPopover-root
  div[aria-hidden="true"] {
  background: red;
  display: none !important;
}

.MuiList-root {
  max-height: 250px;
}

.react-viewer-toolbar {
  display: flex;
  justify-content: center;
  align-items: center;
}

@font-face {
  font-family: "思源宋体 Medium";
  font-weight: 500;
  src:
    url("@/styles/font/SourceHanSans.woff2") format("woff2"),
    url("@/styles/font/SourceHanSans.woff") format("woff");
  font-display: swap;
}
