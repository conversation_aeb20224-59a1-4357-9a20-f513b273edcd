import { Route, Routes, Navigate, HashRouter } from "react-router-dom";
import { RouteProps, staticRouteList } from "../router";
import Layout from "../components/Layout";
import React, {
  Suspense,
  useEffect,
  useMemo,
  useState,
  useCallback,
} from "react";
import MessageManager from "@/components/MessageBox";
import { ThemeProvider } from "@mui/material/styles";
import customTheme from "../utils/theme";
import ProtectedRoute from "@/components/ProtectedRoute";
import { anyValueProps } from "@/types/common";
import { useAppSelector } from "@/hooks";
import { getRoleInfo, getToken } from "@/utils/auth";
import { isEqual } from "lodash";
import { CircularProgress, Box, Typography } from "@mui/material";

// 错误边界组件
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
        >
          <Typography color="error">出现了一些问题，请刷新页面重试</Typography>
        </Box>
      );
    }
    return this.props.children;
  }
}

// 加载组件
const LoadingSpinner: React.FC = () => (
  <Box
    display="flex"
    alignItems="center"
    justifyContent="center"
    minHeight="200px"
  >
    <CircularProgress />
  </Box>
);

/**
 * 递归展开路由列表
 */
const getRoutes = (dyRoutes: RouteProps[]): RouteProps[] =>
  dyRoutes.reduce((acc: any, item: any) => {
    if (item.path) {
      acc.push(item);
    }
    if (item.children?.length > 0) {
      acc.push(...getRoutes(item.children));
    }
    return acc;
  }, [] as RouteProps[]);

const App: React.FC = () => {
  const { systemRoute } = useAppSelector((state) => state.route);
  const { userInfo, roleOption } = useAppSelector((state) => state.user);
  const dynamicRoute = useMemo(() => getRoutes(systemRoute), [systemRoute]);
  const [timer, setTimer] = useState<number>(0);

  const checkExpired = useCallback(() => {
    if (document.visibilityState === "visible") {
      setTimer((prev) => prev + 1);
    }
  }, []);

  useEffect(() => {
    document.addEventListener("visibilitychange", checkExpired);
    return () => {
      document.removeEventListener("visibilitychange", checkExpired);
    };
  }, [checkExpired]);

  useEffect(() => {
    const currentToken = getToken();
    const currentRoleInfo = getRoleInfo();

    // token更换了
    const isTokenExpired =
      userInfo.token && currentToken && userInfo.token !== currentToken;
    // 角色切换
    const isRoleInfoExpired =
      roleOption.roleId &&
      currentRoleInfo &&
      !isEqual(roleOption, currentRoleInfo);

    if (isTokenExpired || isRoleInfoExpired) {
      window.location.reload();
    }
  }, [userInfo, roleOption, timer]);

  const defaultRoute = useMemo(
    () => dynamicRoute[0]?.path || "/paper-search",
    [dynamicRoute],
  );

  return (
    <ErrorBoundary>
      <ThemeProvider theme={customTheme}>
        <MessageManager>
          <HashRouter>
            <Suspense fallback={<LoadingSpinner />}>
              <Routes>
                {/* 处理静态路由 */}
                {staticRouteList.map((routeItem) => (
                  <Route
                    key={routeItem.path}
                    path={routeItem.path}
                    element={<routeItem.components />}
                  />
                ))}
                {/* 包裹动态路由的 Layout 组件 */}
                <Route
                  path="/*"
                  element={
                    <ProtectedRoute>
                      {dynamicRoute?.length === 0 ? (
                        <LoadingSpinner />
                      ) : (
                        <Layout>
                          <Routes>
                            {dynamicRoute.map((routeItem: anyValueProps) => (
                              <Route
                                key={routeItem.path}
                                path={routeItem.path}
                                element={<routeItem.components />}
                              />
                            ))}
                            <Route
                              path="/"
                              element={<Navigate to={defaultRoute} />}
                            />
                            <Route path="*" element={<Navigate to="/404" />} />
                          </Routes>
                        </Layout>
                      )}
                    </ProtectedRoute>
                  }
                />
              </Routes>
            </Suspense>
          </HashRouter>
        </MessageManager>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App;
