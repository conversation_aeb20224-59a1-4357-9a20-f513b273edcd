import { forwardRef } from "react";
import { IconButton } from "@mui/material";
import ArrowDropUp from "@mui/icons-material/ArrowDropUpOutlined";
import ArrowDropDown from "@mui/icons-material/ArrowDropDownOutlined";
import { sortState } from "../index";
const StyledIconButton = styled(IconButton)(() => ({
  padding: "0px",
  "& .MuiSvgIcon-root": {
    fontSize: 10,
    "& path": {
      transform: "scale(2.5)",
      transformOrigin: "center",
    },
  },
  "& .active": {
    color: `rgba(24, 112, 199, 1)`,
  },
}));

const Root = styled(Box, {
  shouldForwardProp: (prop) => prop !== "isBorder",
})<{ isBorder?: boolean }>(({ isBorder }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  width: 115,
  height: 40,
  borderRadius: 28,
  background: "#fff",
  border: isBorder ? " " : "1px solid rgba(207, 207, 207, 1)",
  cursor: "pointer",
  flexShrink: 0,
}));

interface SortPartProps {
  label: string;
  name: string;
  setSortInfo: (value: sortState) => void;
  isBorder?: boolean;
}

type SortType = 0 | 1 | 2; // 0 默认 1 正序 2 倒序
const SortPart = forwardRef((props: SortPartProps, ref) => {
  const { label, name, setSortInfo, isBorder } = props;
  const [sortType, setSortType] = useState<SortType>(0);
  const handleClick = () => {
    switch (sortType) {
      case 0:
        setSortType(1);
        setSortInfo({ name, order: "asc" });
        break;
      case 1:
        setSortType(2);
        setSortInfo({ name, order: "desc" });
        break;
      case 2:
        setSortType(0);
        setSortInfo({ name, order: "desc" });
        break;
    }
  };

  const reset = () => {
    setSortType(0);
    setSortInfo({ name, order: "desc" });
  };

  useImperativeHandle(ref, () => ({
    reset,
  }));
  return (
    <Root onClick={handleClick} isBorder={isBorder}>
      <Typography variant="body2">{label}</Typography>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          ml: 1,
        }}
      >
        <StyledIconButton>
          <ArrowDropUp
            color="inherit"
            className={sortType === 1 ? "active" : ""}
          />
        </StyledIconButton>
        <StyledIconButton>
          <ArrowDropDown
            color="inherit"
            className={sortType === 2 ? "active" : ""}
          />
        </StyledIconButton>
      </Box>
    </Root>
  );
});

export default SortPart;
