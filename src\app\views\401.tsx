import { Divider, Typography } from "@mui/material";
import React from "react";
import { styled } from "@mui/material/styles";
import { navigateToLogin } from "@/utils/auth";

const Root = styled("div")(() => ({
  width: "100%",
  height: "calc(100vh - 64px - 40px)",
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  color: "rgba(0, 0, 0, 0.8)",
}));

const Main = styled("div")(() => ({
  width: "300px",
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
}));

const MyDivider = styled(Divider)(({ theme }) => ({
  marginLeft: theme.spacing(2),
  marginRight: theme.spacing(2),
}));

const StyleButton = styled("div")(() => ({
  color: "rgba(24, 112, 199, 1)",
  cursor: "pointer",
}));

const NotRole: React.FC = () => {
  const handleClick = () => {
    navigateToLogin();
  };

  return (
    <Root>
      <Main>
        <Typography style={{ marginTop: "4px" }}>401</Typography>
        <MyDivider orientation="vertical" flexItem />
        <div style={{ flex: 1, display: "flex" }}>
          无系统权限，点击
          <StyleButton onClick={handleClick}>返回登录</StyleButton>
        </div>
      </Main>
    </Root>
  );
};

export default NotRole;
