import {
  Step,
  StepConnector,
  StepIconProps,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  stepConnectorClasses,
} from "@mui/material";

interface Props {
  stepOptions: string[];
  currentStep: number;
}

const StyleStepper = styled(Stepper, {
  shouldForwardProp: (props) => props !== "currentStep",
})<{ currentStep: number }>(({ currentStep }) => ({
  [`& .MuiStep-root:nth-of-type(${currentStep + 2}) .MuiStepConnector-line`]: {
    background:
      "linear-gradient(to right, rgba(24, 112, 199, 1) 50%, #eaeaf0 50%)",
  },
}));

const QontoConnector = styled(StepConnector)(() => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 7,
    left: "calc(-50% + 5px)",
    right: "calc(50% + 5px)",
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      background: "rgba(24, 112, 199, 1)",
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: "#4248B5",
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    width: "175px",
    height: "8px",
    background: "#eaeaf0",
    border: "none",
  },
}));

const QontoStepIconRoot = styled("div", {
  shouldForwardProp: (props) => props !== "ownerState",
})<{ ownerState: { active?: boolean } }>(({ theme }) => ({
  color: "#eaeaf0",
  display: "flex",
  height: 22,
  alignItems: "center",
  "& .QontoStepIcon-completedIcon": {
    color: "rgba(24, 112, 199, 1)",
    zIndex: 1,
    fontSize: 18,
  },
  "& .QontoStepIcon-circle": {
    width: 18,
    height: 18,
    borderRadius: "50%",
    backgroundColor: "currentColor",
  },
  "& .QontoStepIcon-completedCircle": {
    width: 18,
    height: 18,
    borderRadius: "50%",
    backgroundColor: "rgba(24, 112, 199, 1)",
  },
  ...theme.applyStyles("dark", {
    color: theme.palette.grey[700],
  }),
  variants: [
    {
      props: ({ ownerState }) => ownerState.active,
      style: {
        color: "rgba(24, 112, 199, 1)",
        zIndex: 100,
      },
    },
  ],
}));

function QontoStepIcon(props: StepIconProps) {
  const { active, completed, className } = props;

  return (
    <QontoStepIconRoot ownerState={{ active }} className={className}>
      {completed ? (
        <div className="QontoStepIcon-completedCircle" />
      ) : (
        <div className="QontoStepIcon-circle" />
      )}
    </QontoStepIconRoot>
  );
}

const StepLine: React.FC<Props> = ({ stepOptions, currentStep }) => (
  <StyleStepper
    alternativeLabel
    activeStep={currentStep}
    connector={<QontoConnector />}
    currentStep={currentStep}
  >
    {stepOptions.map((label) => (
      <Step key={label}>
        <StepLabel StepIconComponent={QontoStepIcon}>{label}</StepLabel>
      </Step>
    ))}
  </StyleStepper>
);
export default StepLine;
