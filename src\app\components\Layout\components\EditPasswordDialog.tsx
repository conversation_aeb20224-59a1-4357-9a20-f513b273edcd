import CustomDialog from "@/components/Dialog";
import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { Columns } from "./setting";
import { resetPassword } from "@/api/login";

interface Props {
  open: boolean;
  setOpen: (value: boolean) => void;
}

const ContentBox = styled("div")(() => ({
  height: "100%",
  padding: "15px 20px",
}));

const EditPasswordDialog: React.FC<Props> = ({ open, setOpen }) => {
  const formRef = useRef<RefProps>(null);
  // const [rowOption, setRowOption] = useState({});
  const handleOk = () => {
    formRef.current?.submit().then(async (res: any) => {
      const queryData = {
        password: res.password,
        newPassword: res.newPassword,
      };
      try {
        const { data } = await resetPassword(queryData);
        if (data.code === 200) {
          message.success("修改成功");
          window.location.reload();
          setOpen(false);
        } else {
          message.error(data.message);
        }
      } catch (error) {
        console.log(error);
      }
    });
  };
  return (
    <CustomDialog
      open={open}
      setDialogOpen={setOpen}
      title="修改密码"
      okButtonProps={{ onOk: handleOk }}
      cancelButtonProps={{ onCancel: () => setOpen(false) }}
      width={500}
    >
      <ContentBox slot="content">
        <DynamicForm
          ref={formRef}
          columns={Columns}
          size="small"
          // formData={rowOption}
          rowSpacing={0}
        />
      </ContentBox>
    </CustomDialog>
  );
};
export default EditPasswordDialog;
