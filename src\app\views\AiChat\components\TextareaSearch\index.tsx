import { FC } from "react";
import SearchIcon from "@/assets/chat-search.svg";
import { TextField, IconButton, InputAdornment } from "@mui/material";
interface TextFieldProps {
  value: string;
  onChange: (value: string) => void;
  maxLength?: number;
  onPressEnter?: (e: any) => void;
  onSubmit?: () => void;
  disabled?: boolean;
  placeholder: string;
  maxRows?: number;
  minRows?: number;
}

const IconButtonStyle = styled(IconButton)(({}) => ({
  borderRadius: "16px",
  background:
    "linear-gradient(90deg, rgba(110, 84, 227, 1) 0%, rgba(27, 130, 227, 1) 100%)",
  width: "66px",
  height: "32px",
  ":disabled": {
    cursor: " not-allowed",
    pointerEvents: "auto",
    background: "#ccc",
  },
}));

const TextFieldStyle = styled(TextField)(() => ({
  width: "100%",
  position: "relative",
  border: "none",
  "&>div": {
    minHeight: "45px",
    padding: "6px 15px",
    borderRadius: "20px",
    background:
      "radial-gradient(39.37% 346.97% at 11.645569620253164% -204.54545454545453%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
    border: "2px solid rgba(255, 255, 255, 1) !important",
  },
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      // 取消输入外框
      border: "none",
    },
  },
}));

const ImgStyle = styled("img")(() => ({}));
const MultilineTextFields: FC<TextFieldProps> = ({
  value,
  onChange,
  maxLength,
  onPressEnter,
  onSubmit,
  disabled,
  placeholder = "请输入",
  maxRows = 10,
  minRows = 1,
  ...props
}) => {
  const onChangeValue = (event: { target: { value: string } }) => {
    const newValue = (event.target.value as string).slice(0, 1000);
    onChange(newValue);
  };
  return (
    <TextFieldStyle
      id="outlined-multiline-flexible"
      multiline
      maxRows={maxRows}
      minRows={minRows}
      value={value}
      placeholder={placeholder}
      slotProps={{
        htmlInput: { maxLength },
        input: {
          endAdornment: (
            <InputAdornment position="end">
              <IconButtonStyle
                edge="end"
                onClick={onSubmit}
                disabled={disabled}
              >
                <ImgStyle src={SearchIcon} alt="search" />
              </IconButtonStyle>
            </InputAdornment>
          ),
        },
        formHelperText: {
          sx: {
            position: "absolute",
            right: 0,
            bottom: "-20px",
            fontSize: "0.75rem",
            color: "gray",
          },
        },
      }}
      onChange={onChangeValue}
      onKeyDown={onPressEnter}
      {...props}
      helperText={
        maxLength ? (
          <span style={{ textAlign: "right", display: "block" }}>
            {`${value.length}/${maxLength}`}
          </span>
        ) : (
          ""
        )
      }
    />
  );
};

export default MultilineTextFields;
