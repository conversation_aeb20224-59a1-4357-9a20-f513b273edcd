import { FormHelperText, styled } from "@mui/material";
import React from "react";

const StyledFormHelperText = styled(FormHelperText, {
  shouldForwardProp: (props) => props !== "label_width" && props !== "shorter",
})<{
  label_width?: number;
  shorter?: boolean;
}>(({ label_width, shorter = false }) => ({
  marginLeft: label_width ?? 0,
  marginTop: shorter ? 0 : 3,
  height: shorter ? 14 : 20,
  lineHeight: shorter ? "14px" : 1.66,
  color: "#d32f2f",
  paddingLeft: "10px",
}));

interface ErrorHintProps {
  error?: string;
  labelWidth?: number;
  shorter?: boolean;
}
const ErrorHint: React.FC<ErrorHintProps> = ({
  error,
  labelWidth,
  shorter,
}) => (
  <StyledFormHelperText
    label_width={labelWidth}
    shorter={shorter}
  >{`${error || " "}`}</StyledFormHelperText>
);

export default ErrorHint;
