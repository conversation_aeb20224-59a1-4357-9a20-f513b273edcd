import { CircularProgress, But<PERSON>, Icon<PERSON>utton } from "@mui/material";
import { styled } from "@mui/material/styles";
import CloseIcon from "@mui/icons-material/Close";
import { useNavigate } from "react-router-dom";
import { parseStatusList } from "@/views/PaperBase/components/PersonalPaper/setting";
import { reparsePdf } from "@/api/personalpaper";
import { message } from "@/components/MessageBox/message";

interface Props {
  style?: React.CSSProperties;
  status?: number;
  polling?: () => void;
  pdfId?: any;
  onClose?: () => void;
}

const Root = styled("div")(() => ({
  width: "calc(100% - 82px)",
  height: "calc(100% - 41px)",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  flexDirection: "column",
  background: "rgba(255, 255, 255, 0.7)",
  position: "absolute",
  top: 0,
  left: "41px",
  right: 0,
  bottom: 0,
  zIndex: 999,
  borderRadius: "20px",
}));

const Content = styled("div")(() => ({
  marginTop: 10,
  fontSize: 16,
}));

const StyleButton = styled(Button)(() => ({
  marginTop: 10,
}));

const CloseButton = styled(IconButton)(() => ({
  position: "absolute",
  top: 16,
  right: 16,
  backgroundColor: "rgba(255, 255, 255, 0.8)",
  "&:hover": {
    backgroundColor: "rgba(255, 255, 255, 0.9)",
  },
}));

const AnalyzingInProgress: React.FC<Props> = ({
  style,
  status,
  polling,
  pdfId,
  onClose,
}) => {
  const navigate = useNavigate();

  const handleClose = () => {
    if (status !== undefined && [0, 1000, 2000].includes(status)) {
      navigate(-1); // 返回上一页
    } else {
      onClose?.();
    }
  };
  const getStatusText = () => {
    const statusInfo = parseStatusList.find((el) => el.value === status);
    return statusInfo?.label || "";
  };

  const getStatusColor = () => {
    const statusInfo = parseStatusList.find((el) => el.value === status);
    return statusInfo?.color || "";
  };

  const reParsePdfs = async () => {
    try {
      const { data } = await reparsePdf({
        id: pdfId,
      });
      if (data.code === 200) {
        message.success("重试成功");
        polling?.();
      } else {
        message.error("重试失败");
      }
    } catch (e: any) {
      message.error("重试失败" + e.message);
    }
  };

  return (
    <Root style={style}>
      <CloseButton onClick={handleClose}>
        <CloseIcon />
      </CloseButton>
      <CircularProgress />
      {status !== undefined && [4000, 5000].includes(status) && (
        <StyleButton onClick={reParsePdfs}>重试</StyleButton>
      )}
      <Content style={{ color: getStatusColor() }}>{getStatusText()}</Content>
    </Root>
  );
};
export default AnalyzingInProgress;
