import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { saveDataId } from "@/api/chat";
import { getRoleInfo, getToken } from "@/utils/auth";
import Operate from "./Operate";
interface Props {
  data: any;
  stopMsg?: string;
  handleDelete?: () => void;
  templateId: string;
  aiChemUrl: string;
  loading?: boolean;
}

const Root = styled("div", {
  shouldForwardProp: (prop) => prop !== "isStyle",
})<{ isStyle: boolean }>(({ isStyle }) => ({
  height: "100%",
  boxSizing: "border-box",
  fontSize: "14px",
  display: isStyle ? "" : "inline-block",
}));

const Main = styled("div")(() => ({
  background: "#fff",
}));

const TableDiv = styled("div")(() => ({
  display: "flex",
  height: "35px",
  lineHeight: "35px",
  fontSize: "14px",
  background: "rgb(243, 243, 243)",
  border: "1px solid #eee7e7",
  boxSizing: "border-box",
}));

const TableDivItem = styled("div", {
  shouldForwardProp: (prop) => prop !== "currentIndex",
})<{ currentIndex: boolean }>(({ currentIndex }) => ({
  width: "20%",
  textAlign: "center",
  color: currentIndex ? "rgba(0, 104, 177)" : "",
  background: currentIndex ? "#fff" : "",
  cursor: "pointer",
  // borderBottom: currentIndex ? "2px solid rgba(134, 114, 211)" :'none',
}));

const SliderMain = styled("div", {
  shouldForwardProp: (prop) => prop !== "loading",
})<{ loading: boolean | undefined }>(({ loading }) => ({
  width: "300px",
  height: "400px",
  padding: "5px 20px",
  boxSizing: "border-box",
  cursor: loading ? "auto" : "pointer",
  fontSize: "14px",
}));

const StepDiv = styled("div")(() => ({
  height: "calc(100%)",
  overflowY: "auto",
  boxSizing: "border-box",
}));
const StepDivItem = styled("div")(() => ({
  margin: "10px 0",
  display: "flex",
  paddingRight: "10px",
}));

const StepDivItemLabel = styled("div")(() => ({
  width: "55px",
  textAlign: "right",
  marginRight: "10px",
}));
const StepDivItemText = styled("div")(() => ({
  flex: 1,
}));
const SliderStyle = styled(Slider)(() => ({
  ".slick-prev": {
    left: "-13px",
    top: "50%",
    zIndex: 20,
  },
  ".slick-next": {
    right: "-8px",
    top: "50%",
    zIndex: 20,
  },

  ".slick-prev:before,.slick-next:before": {
    color: "rgba(0, 104, 177)",
    fontSize: "30px",
  },
}));

const StopMsgDiv = styled("div")(() => ({
  fontSize: "14px",
  marginTop: "10px",
}));

const Index: React.FC<Props> = ({
  data,
  stopMsg,
  handleDelete,
  templateId,
  aiChemUrl,
  loading,
}) => {
  const [id, setId] = useState<string>("");

  const [currentIndex, setCurrentIndex] = useState(0);
  const currentToken = getToken();
  const currentRoleInfo = getRoleInfo();
  const sliderRef = useRef<Slider>(null);
  const settings = {
    dots: false, // 显示指示点
    infinite: false, // 无限循环
    speed: 500, // 切换速度
    slidesToShow: 1, // 每次显示一张幻灯片
    slidesToScroll: 1, // 每次滚动一张
    autoplay: false, // 自动播放
    draggable: false, // 禁止拖拽
    beforeChange: (_: number, next: number) => {
      setCurrentIndex(next);
    },
  };
  // 生成实验方案id
  const createDataId = async (workstationParams: any) => {
    try {
      const {
        data: { data, code },
      } = await saveDataId({
        data: workstationParams,
      });
      if (code === 200) {
        setId(data);
      }
    } catch (error) {
      message.error((error as Error).message);
    }
  };

  const getHashUrl = (url: string) => {
    const jumpUrl = url.endsWith("/") ? url : url + "/"; // 自动拼接 /
    const hashIndex = url.indexOf("#");
    return hashIndex === -1 ? jumpUrl + "#/" : jumpUrl;
  };

  useEffect(() => {
    if (id && templateId && aiChemUrl) {
      const url = checkHttpCode(aiChemUrl) as string;
      const hashUrl = getHashUrl(url);
      window.open(
        `${hashUrl}task/createTask?type=task&action=add&templateId=${templateId}&dataId=${id}&rw=w&system=aihub&token=${currentToken}&roleInfo=${JSON.stringify(currentRoleInfo)}`,
      );
    }
  }, [id]);
  const handleClick = (item: any) => {
    const { workstation_params, step_descriptions } = item;
    if (workstation_params && step_descriptions) {
      window.open(
        `http://************:8886/#/task/newTask?id=6671428565106688&taskType=custom&operateType=copy`,
      );
      return;
      createDataId(item.workstation_params);
    } else {
      message.error("该方案没有实验步骤，无法生成");
    }
  };

  const checkHttpCode = (data: string | string[]) => {
    if (!data) return "";
    const index = data.indexOf("://");
    if (index === -1) {
      return `${window.location.origin}${data}`;
    }
    return data;
  };

  const copyText = () => {
    const newData = data;
    const copyData = newData
      .map((item: any, index: number) => {
        const { step_descriptions, progress, error } = item;
        const numberedSteps = step_descriptions
          ? step_descriptions
              .map((step: string, index: number) => `步骤${index + 1}：${step}`)
              .join("\n")
          : "";
        const text = step_descriptions
          ? numberedSteps
          : !step_descriptions && progress && !error
            ? progress.replace(/<br \/>/g, "\n")
            : error;
        return `方案${index + 1}\n${text}`;
      })
      .join("\n");
    return copyData;
  };
  const handleTab = (index: number) => {
    setCurrentIndex(index);
    // 使用 ref 调用轮播图的切换方法
    if (sliderRef.current) {
      sliderRef.current.slickGoTo(index);
    }
  };
  return (
    <Root isStyle={data ? true : false}>
      <Main>
        <TableDiv>
          {data &&
            data.map((_: any, index: number) => (
              <TableDivItem
                key={index}
                currentIndex={currentIndex === index ? true : false}
                onClick={() => handleTab(index)}
              >
                方案{index + 1}
              </TableDivItem>
            ))}
        </TableDiv>
        {data ? (
          <SliderStyle {...settings} ref={sliderRef}>
            {data.map((item: any, index: number) => (
              <SliderMain
                key={index}
                onClick={loading ? undefined : () => handleClick(item)}
                loading={loading}
              >
                <StepDiv>
                  {item.step_descriptions ? (
                    item.step_descriptions.map(
                      (desc: string, descIndex: number) => (
                        <StepDivItem key={descIndex}>
                          <StepDivItemLabel>
                            步骤{descIndex + 1}:
                          </StepDivItemLabel>
                          <StepDivItemText>{desc}</StepDivItemText>
                        </StepDivItem>
                      ),
                    )
                  ) : !item.step_descriptions &&
                    item.progress &&
                    !item.error ? (
                    <div
                      dangerouslySetInnerHTML={{ __html: item.progress }}
                      className="markdown-body"
                    ></div>
                  ) : (
                    <>{item.error}</>
                  )}
                </StepDiv>
              </SliderMain>
            ))}
          </SliderStyle>
        ) : (
          <span>{data.error}</span>
        )}
      </Main>
      {stopMsg && <StopMsgDiv>{stopMsg}</StopMsgDiv>}
      {!loading && <Operate handleDelete={handleDelete} text={copyText()} />}
    </Root>
  );
};
export default Index;
