import { SnackbarMessage, useSnackbar, OptionsObject } from "notistack";
import React, { FC } from "react";
import { Alert } from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle"; // 成功图标
import ErrorIcon from "@mui/icons-material/Error"; // 错误图标
import InfoIcon from "@mui/icons-material/Info"; // 信息图标
import WarningIcon from "@mui/icons-material/Warning"; // 警告图标
let useSnackbarRef: ReturnType<typeof useSnackbar>; // 更新类型定义

const AlertStyle = styled(Alert)(() => ({
  padding: "1px 14px",
  borderRadius: "6px",
  background: "#fff",
  color: "#000",
  boxShadow: "0px 0px 3px rgba(0, 0, 0, 0.2)",
}));
export const SnackbarUtilsConfigurator: FC = () => {
  useSnackbarRef = useSnackbar();
  return null;
};

// 全局图标配置
const defaultIcons = {
  success: <CheckCircleIcon />,
  error: <ErrorIcon />,
  info: <InfoIcon />,
  warning: <WarningIcon />,
  default: <InfoIcon />,
};

const message = {
  success(
    msg: SnackbarMessage,
    options: OptionsObject = {},
    icon?: React.ReactElement,
  ) {
    this.toast(
      msg,
      { ...options, variant: "success" },
      icon || defaultIcons.success,
    );
  },
  error(
    msg: SnackbarMessage,
    options: OptionsObject = {},
    icon?: React.ReactElement,
  ) {
    this.toast(
      msg,
      { ...options, variant: "error" },
      icon || defaultIcons.error,
    );
  },
  info(
    msg: SnackbarMessage,
    options: OptionsObject = {},
    icon?: React.ReactElement,
  ) {
    this.toast(msg, { ...options, variant: "info" }, icon || defaultIcons.info);
  },
  warning(
    msg: SnackbarMessage,
    options: OptionsObject = {},
    icon?: React.ReactElement,
  ) {
    this.toast(
      msg,
      { ...options, variant: "warning" },
      icon || defaultIcons.warning,
    );
  },
  toast(
    msg: SnackbarMessage,
    options: OptionsObject = {},
    icon?: React.ReactElement,
  ) {
    const severity = options.variant as
      | "success"
      | "error"
      | "info"
      | "warning";
    useSnackbarRef.enqueueSnackbar(msg, {
      ...options,
      content: () => (
        <AlertStyle
          severity={severity}
          icon={icon} // 使用自定义图标
        >
          {msg}
        </AlertStyle>
      ),
    });
  },
};
export { message };
