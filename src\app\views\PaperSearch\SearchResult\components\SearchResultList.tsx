import React from "react";
import { styled, Box, Checkbox } from "@mui/material";
import { anyValueProps } from "@/types/common";
import ArticleItem from "../../components/ArticleItem";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import { useNavigate } from "react-router-dom";
import { setActives, setSelectedBank } from "@/store/counterSlice";
import { getNewChatId } from "@/api/chat";

const StyledArticleItem = styled("div")(() => ({
  display: "flex",
  borderRadius: 20,
  background: "#fff",
  border: "1px solid  rgba(235, 235, 235, 1)",
  marginBottom: 20,
  padding: "16px 29px 16px 38px",
  boxSizing: "border-box",
}));

const Main = styled("div")(() => ({
  marginLeft: 10,
  boxSizing: "border-box",
  flex: 1,
  width: 0,
}));

interface SearchResultListProps {
  data: anyValueProps[];
  selectedIds: number[];
  onChangeId: (id: number, selected: boolean) => void;
  searchId: string | undefined;
}

const SearchResultList: React.FC<SearchResultListProps> = ({
  data,
  selectedIds,
  onChangeId,
  searchId,
}) => {
  const dispatch = useAppDispatch();
  const navigator = useNavigate();
  const { listTotal } = useAppSelector((state: RootState) => state.counter);

  const getChatId = async () => {
    const {
      data: { data },
    } = await getNewChatId();
    dispatch(setActives(data));
  };
  const chatPaper = (id: number) => {
    if (listTotal >= 1000) {
      message.warning("聊天窗口已到达1000次,请删除之前创建的聊天窗口");
    } else {
      dispatch(setActives(null));
      dispatch(setSelectedBank({ externalIds: [id], bankType: 2 }));
      navigator("/ai-chat");
      getChatId();
    }
  };
  return (
    <Box sx={{ width: "100%" }}>
      {data.map((item) => (
        <StyledArticleItem key={item.pdfId}>
          <Checkbox
            sx={{
              width: 18,
              height: 18,
              "& .MuiSvgIcon-root": { fontSize: 18 },
            }}
            checked={selectedIds.some((el) => el === item.pdfId)}
            onChange={(event) => onChangeId(item.pdfId, event.target.checked)}
          />
          <Main>
            <ArticleItem
              articleInfo={item}
              addArt={true}
              addAi={true}
              chatPaper={chatPaper}
              type="searchResult"
              searchId={searchId}
              isSearch={true}
            />
          </Main>
        </StyledArticleItem>
      ))}
    </Box>
  );
};
export default SearchResultList;
