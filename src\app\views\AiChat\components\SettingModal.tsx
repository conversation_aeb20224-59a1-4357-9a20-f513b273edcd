import React, { useEffect, useState } from "react";
import { Box, DialogContent, Typography, styled, Slider } from "@mui/material";
import Clear from "@/assets/clear.svg";
import Export from "@/assets/export.svg";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import { changeValue } from "@/store/counterSlice";
import { anyValueProps } from "@/types/common";
import { clearChat, exportChat } from "@/api/chat";
import CustomDialog from "@/components/Dialog";
import { getCurrentDate } from "@/utils/currentDate";
const Root = styled("div")(() => ({
  padding: "15px 0",
  borderRadius: "20px",
  background:
    "radial-gradient(62.96% 59.83% at 17.130620985010705% -40.16620498614959%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  boxShadow: "0px 0px 30px  rgba(0, 0, 0, 0.15)",
  color: "#000",
  width: "450px",
  opacity: 1,
}));

const SettingTitle = styled("div")(() => ({
  height: "32px",
  lineHeight: "32px",
  paddingLeft: "20px",
  fontWeight: 700,
}));

const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  rowGap: theme.spacing(2),
  padding: "5px 24px 10px",
}));
const PartBox = styled("div")(() => ({
  fontSize: 14,
  display: "flex",
}));
const PartBoxStyle = styled(PartBox)(() => ({
  alignItems: "center",
}));
const Label = styled("div")(({ theme }) => ({
  width: 100,
  marginRight: theme.spacing(2),
  textAlign: "right",
}));
const Content = styled("div")(() => ({
  flex: 1,
  position: "relative",
}));
// const SwitchStyle = styled(Switch)(() => ({
//   position: "absolute",
//   top: "-50%",
//   left: "-12px",
//   color: "rgba(24, 112, 199, 1)",
//   "& .MuiSwitch-switchBase.Mui-checked": {
//     color: "rgba(24, 112, 199, 1)",
//     "&:hover": {
//       backgroundColor: "none",
//     },
//   },
//   "& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track": {
//     backgroundColor: "rgba(24, 112, 199, 1)",
//   },
// }));
const SliderBox = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
}));

const PrettoSlider = styled(Slider)({
  color: "rgba(24, 112, 199, 1)",
  height: 10,
  padding: 0,
  "& .MuiSlider-track": {
    border: "none",
  },
  "& .MuiSlider-rail": {
    backgroundColor: "rgba(235, 235, 235, 1)",
  },
  "& .MuiSlider-thumb": {
    height: 18,
    width: 18,
    backgroundColor: "rgba(255, 252, 252, 1)",
    boxShadow: "0px 2px 4px  rgba(0, 0, 0, 0.25)",
    "&:focus, &:hover, &.Mui-active, &.Mui-focusVisible": {
      boxShadow: "0px 2px 4px  rgba(0, 0, 0, 0.25)",
    },
    "&::before": {
      display: "none",
    },
  },
  "& .MuiSlider-valueLabel": {
    lineHeight: 1.2,
    fontSize: 12,
    background: "unset",
    padding: 0,
    width: 32,
    height: 32,
    borderRadius: "50% 50% 50% 0",
    backgroundColor: "rgba(24, 112, 199, 1)",
    transformOrigin: "bottom left",
    transform: "translate(50%, -100%) rotate(-45deg) scale(0)",
    "&::before": { display: "none" },
    "&.MuiSlider-valueLabelOpen": {
      transform: "translate(50%, -100%) rotate(-45deg) scale(1)",
    },
    "& > *": {
      transform: "rotate(45deg)",
    },
  },
});

// const ButtonBox = styled("div")(() => ({}));
const ButtonStyle = styled(Button)(() => ({
  opacity: " 1",
  borderRadius: "15px",
  width: "82px",
  height: "30px",
  color: "rgba(0, 0, 0, 1)",
  background: "rgba(255, 255, 255, 1)",
  border: "1px solid rgba(235, 235, 235, 1)",
  ":hover": {
    color: "rgba(24, 112, 199, 1)",
    border: "1px solid rgba(0, 104, 177, 1)",
    boxShadow: "0px 3px 8px  rgba(29, 90, 246, 0.18)",
  },
  ":hover img": {
    filter:
      "brightness(0) saturate(100%) invert(27%) sepia(46%) saturate(7008%) hue-rotate(222deg) brightness(100%) contrast(94%)",
  },
}));

const TypographyStyle = styled(Typography)(() => ({
  fontSize: 12,
}));

const ContentBox = styled("div")(() => ({
  height: "100px",
  lineHeight: "100px",
  paddingLeft: "50px",
}));
interface SettingModalProps {
  setAnchorEl: (value: null | HTMLButtonElement) => void;
  getChatHistory: (value?: boolean) => void;
  isDataLength: boolean;
  setExpandedIndex: (value: number | null) => void;
  setPaperData: (value: anyValueProps[]) => void;
}

const SettingModal: React.FC<SettingModalProps> = ({
  getChatHistory,
  setAnchorEl,
  isDataLength,
  setPaperData,
  setExpandedIndex,
}) => {
  const dispatch = useAppDispatch();
  const { appSetting, active, selectedBank } = useAppSelector(
    (state: RootState) => state.counter,
  );
  const [setting, setSetting] = useState<anyValueProps>();
  const [delOpen, setDelOpen] = useState<boolean>(false);

  useEffect(() => {
    setSetting(appSetting);
  }, [appSetting]);

  const ChangeSettingValue = (field: string, value: any) => {
    if (setting?.[field] !== value) {
      dispatch(changeValue({ field, value }));
    }
  };
  const handleExport = async () => {
    try {
      if (active && isDataLength) {
        const { data } = await exportChat(active);
        const date = getCurrentDate();
        const url = window.URL.createObjectURL(new Blob([data])); // 创建一个临时的URL表示这个blob对象
        const link = document.createElement("a"); // 创建一个隐藏的<a>元素
        link.href = url;
        link.setAttribute("download", `chat-store-${date}.zip`); // 设置下载后的文件名
        document.body.appendChild(link);
        link.click(); // 触发点击事件开始下载
        link.parentNode?.removeChild(link);
        window.URL.revokeObjectURL(url);
      } else {
        message.warning("没有可导出的数据");
        setAnchorEl(null);
      }
    } catch (error) {
      message.error(`${(error as Error)?.message}`);
    }
  };
  // const handleImport = () => {
  //   const fileInput = document.getElementById("fileInput") as HTMLElement;
  //   if (fileInput) fileInput.click();
  // };

  // const importData = (event: any) => {
  //   const target = event.target as HTMLInputElement;
  //   if (!target || !target.files) return;
  //   const file: File = target.files[0];
  //   window.console.log(file);
  //   if (!file) return;

  //   const reader: FileReader = new FileReader();
  //   reader.onload = async () => {
  //     try {
  //       const fileData = JSON.parse(reader.result as string);
  //       const data = await importChat({file:file});
  //       window.console.log(data);
  //     } catch (error) {
  //       message.error(`${(error as Error)?.message}`);
  //     }
  //   };
  //   reader.readAsText(file);
  // };
  const handleClear = () => {
    setDelOpen(true);
  };
  const handleOk = async () => {
    try {
      if (active && isDataLength) {
        const {
          data: { code },
        } = await clearChat(active);
        if (code === 200) {
          message.success("清空当前对话记录成功");
          getChatHistory(true);
          setAnchorEl(null);
          setDelOpen(false);
          setExpandedIndex(null);
          setPaperData([]);
        }
      } else {
        message.warning("没有可清空的数据");
        setDelOpen(false);
        setAnchorEl(null);
      }
    } catch (error) {
      message.error(`${(error as Error)?.message}`);
    }
  };

  function valueLabelFormat(value: number) {
    return `${value}%`;
  }
  return (
    <Root>
      <SettingTitle>设置</SettingTitle>

      <StyledDialogContent>
        <PartBoxStyle>
          <Label>聊天记录</Label>
          <Content>
            <ButtonStyle
              variant="outlined"
              size="small"
              onClick={handleExport}
              startIcon={<img src={Export} />}
            >
              导出
            </ButtonStyle>
            {/* <input
              id="fileInput"
              type="file"
              style={{ display: "none" }}
              onChange={importData}
            />
            <ButtonStyle
              variant="outlined"
              size="small"
              sx={{ ml: 1, mr: 1 }}
              startIcon={<img src={Download} />}
              onClick={handleImport}
            >
              导入
            </ButtonStyle> */}
            <ButtonStyle
              variant="outlined"
              size="small"
              sx={{ ml: 1 }}
              startIcon={<img src={Clear} />}
              onClick={handleClear}
            >
              清空
            </ButtonStyle>
          </Content>
        </PartBoxStyle>
        {/* <PartBoxStyle>
          <Label>主题</Label>
          <Content>
            <ButtonBox>
              <ButtonColorStyle
                variant="contained"
                size="small"
                onClick={() => ChangeSettingValue("theme", "light")}
                sx={{
                  mr: 1,
                }}
                isTheme={setting?.theme === "light" && true}
              >
                <LightModeOutlinedIcon />
              </ButtonColorStyle>
              <ButtonColorStyle
                variant="contained"
                size="small"
                onClick={() => ChangeSettingValue("theme", "dark")}
                isTheme={setting?.theme === "dark" && true}
              >
                <NightsStayOutlinedIcon />
              </ButtonColorStyle>
            </ButtonBox>
          </Content>
        </PartBoxStyle> */}
        <PartBox>
          <Label>知识检索阈值</Label>
          <Content>
            <SliderBox>
              <PrettoSlider
                value={setting?.normal ?? 0}
                sx={{ mr: 2 }}
                onChange={(event: Event, value: number | number[]) =>
                  ChangeSettingValue("normal", value)
                }
                valueLabelFormat={valueLabelFormat}
                min={50}
                max={75}
                disabled={!selectedBank.data && selectedBank.bankType !== 2}
                valueLabelDisplay="auto"
              />
              {setting?.normal}%
            </SliderBox>
            <TypographyStyle>推荐检索阈值范围50%-75%</TypographyStyle>
          </Content>
        </PartBox>
        <PartBox>
          <Label>对话温度</Label>
          <Content>
            <div style={{ marginRight: "43px" }}>
              <SliderBox>
                <PrettoSlider
                  value={setting?.temperature ?? 0}
                  onChange={(event: Event, value: number | number[]) =>
                    ChangeSettingValue("temperature", value)
                  }
                  min={0}
                  max={1}
                  step={0.1}
                  valueLabelDisplay="auto"
                />
              </SliderBox>
              <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                <TypographyStyle>严谨</TypographyStyle>
                <TypographyStyle>发散</TypographyStyle>
              </Box>
            </div>
          </Content>
        </PartBox>
        {/* <PartBox>
          <Label>开启上下文</Label>
          <Content>
            <SwitchStyle
              checked={setting?.switchState ?? true}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                ChangeSettingValue("switchState", event.target.checked);
              }}
              inputProps={{ "aria-label": "controlled" }}
            />
          </Content>
        </PartBox> */}
      </StyledDialogContent>
      <CustomDialog
        open={delOpen}
        setDialogOpen={setDelOpen}
        title="清空对话记录？"
        okButtonProps={{ onOk: handleOk }}
        cancelButtonProps={{ onCancel: () => setDelOpen(false) }}
      >
        <ContentBox slot="content">
          <span>清空对话记录后无法恢复和找回，请谨慎操作</span>
        </ContentBox>
      </CustomDialog>
    </Root>
  );
};

export default SettingModal;
