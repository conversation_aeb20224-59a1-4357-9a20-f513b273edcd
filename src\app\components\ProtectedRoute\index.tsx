import React, { useEffect, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { getInfoByToken } from "@/store/user";
import { getSystemMenu } from "@/store/route";
import {
  getRoleInfo,
  getToken,
  navigateToLogin,
  setRoleInfo,
  setToken,
} from "@/utils/auth";
import { useQueryParams } from "@/hooks/useQueryParams";
import { Box, CircularProgress } from "@mui/material";
import { useLocation } from "react-router-dom";
import { getLoginUrl } from "@/api/login";
// import { useNavigate, useLocation } from "react-router-dom";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const LoadingState: React.FC = () => (
  <Box
    display="flex"
    flexDirection="column"
    alignItems="center"
    justifyContent="center"
    minHeight="100vh"
    gap={2}
  >
    <CircularProgress />
  </Box>
);

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const addressParams = useQueryParams();
  const { userInfo, roleOption } = useAppSelector((state) => state.user);
  // const navigate = useNavigate();
  const location = useLocation();

  const [addressToken, setAddressToken] = useState(addressParams?.get("token"));
  const [addressRoleInfo, setAddressRoleInfo] = useState(
    addressParams?.get("roleInfo"),
  );

  const setStorageLoginUrl = async () => {
    const { data } = await getLoginUrl({ key: "loginUrl" });
    const result = data.result;
    const loginUrl = result
      ? result.endsWith("/")
        ? result
        : result + "/"
      : "";
    localStorage.setItem("loginUrl", loginUrl);
  };

  // 清除URL参数
  const clearUrlParams = useCallback(() => {
    const currentPath = window.location.pathname + location.hash;
    window.history.replaceState({}, "", currentPath);
  }, [location]);

  // 检查认证状态
  const checkAuthStatus = useCallback(() => {
    const currentToken = getToken();
    const currentRoleInfo = getRoleInfo();

    // 如果没有token，直接跳转登录
    if (!currentToken && !addressToken) {
      navigateToLogin();
      return false;
    }

    // 如果有token但没有角色信息，也跳转登录
    if (currentToken && !currentRoleInfo && !addressRoleInfo) {
      navigateToLogin();
      return false;
    }

    return true;
  }, [addressToken, addressRoleInfo]);

  useEffect(() => {
    setStorageLoginUrl();
  }, []);

  useEffect(
    () => {
      const handleAuthentication = async () => {
        try {
          // 处理URL中的认证信息
          if (addressToken) {
            setToken(addressToken, true);
            setAddressToken(null);
            if (addressRoleInfo) {
              setRoleInfo(addressRoleInfo);
              setAddressRoleInfo(null);
            }
            // 清除URL参数
            clearUrlParams();
          }

          // 检查认证状态
          if (!checkAuthStatus()) {
            return;
          }

          // 只有在有token且没有用户信息时才获取用户信息
          const currentToken = getToken();
          if (currentToken && !userInfo.name) {
            await dispatch(getInfoByToken()).unwrap();
          }
        } catch (error) {
          console.error("Authentication error:", error);
          navigateToLogin();
        }
      };

      handleAuthentication();
    },
    [
      // addressToken,
      // addressRoleInfo,
      // userInfo.name,
      // dispatch,
      // checkAuthStatus,
      // clearUrlParams,
    ],
  );

  useEffect(() => {
    const fetchSystemMenu = async () => {
      if (roleOption.roleId && getToken()) {
        // 添加token检查
        try {
          await dispatch(getSystemMenu(roleOption.roleId)).unwrap();
        } catch (error) {
          console.error("Failed to fetch system menu:", error);
          checkAuthStatus(); // 如果获取菜单失败，检查认证状态
        }
      }
    };

    fetchSystemMenu();
  }, [roleOption.roleId, dispatch, checkAuthStatus]);

  // 如果没有token，直接显示loading（会被checkAuthStatus重定向）
  if (!getToken() && !addressToken) {
    return <LoadingState />;
  }

  // 如果有token但没有用户信息，显示loading
  if (!userInfo.name) {
    return <LoadingState />;
  }

  return <>{children}</>;
};

export default React.memo(ProtectedRoute);
