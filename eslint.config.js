import js from "@eslint/js";
import globals from "globals";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import tseslint from "typescript-eslint";
import eslintConfigPrettier from "eslint-config-prettier";
import eslintPluginPrettierRecommended from "eslint-plugin-prettier/recommended";

export default tseslint.config({
  extends: [
    js.configs.recommended,
    ...tseslint.configs.recommended,
    eslintConfigPrettier,
    eslintPluginPrettierRecommended,
  ],
  files: ["**/*.{ts,tsx}"], // eslint 检测的文件，根据需要自行设置
  ignores: ["dist"],
  languageOptions: {
    ecmaVersion: "latest",
    globals: globals.browser,
  },
  plugins: {
    "react-hooks": reactHooks,
    "react-refresh": reactRefresh,
  },
  rules: {
    ...reactHooks.configs.recommended.rules,
    "prettier/prettier": "warn", // 默认为 error
    "arrow-body-style": "off",
    "prefer-arrow-callback": "off",
    "@typescript-eslint/no-explicit-any": "off", // allow any type
    "no-unused-expressions": "off", // 禁用 no-unused-expressions 规则
    "react/prop-types": "off", // 禁用 prop-types 规则
    "no-empty-pattern": "off", // 禁用空对象模式规则
    "react/react-in-jsx-scope": "off", // 关闭 React 作用域警告
    "object-shorthand": ["error", "always", { avoidQuotes: true }],
    "@typescript-eslint/interface-name-prefix": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-unused-expressions": "off",
    "no-undef": "off", // 关闭 no-undef 规则
    "arrow-body-style": ["error", "as-needed"], // 当有一个表达式时省略 {}
    "react-hooks/exhaustive-deps": "off", // 全局禁用 react-hooks/exhaustive-deps 规则
    "prettier/prettier": "error", // Prettier 规则
  },
});
