import React, { useState } from "react";
import { Box, Button, Checkbox, Typography, styled } from "@mui/material";
import SearchInput from "../components/SearchInput";
import { anyValueProps } from "../../../types/common";
// import CollapseCard from "./components/CollapseCard";
import SearchResultList from "./components/SearchResultList";
import MyPaperPagination from "@/components/MyPaperPagination";
import { getHybridSearch } from "@/api/paperSearch";
import useSearchRecord from "@/hooks/useSearchRecord";
import Breadcrumb from "@/components/Breadcrumb";
// import  { SortStatus } from "./components/SortButton";
import { setActives, setSelectedBank } from "@/store/counterSlice";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import { useNavigate, useParams } from "react-router-dom";
import { withPermission } from "@/components/HocButton";
import { PERMISSION_MENU } from "@/utils/permission";
import { getNewChatId } from "@/api/chat";
// const cateWidth = 340;

const Root = styled("div")(() => ({
  width: "100%",
  height: "calc(100% - 30px)",
}));

const Main = styled("div")(() => ({
  color: "#000",
  width: "80%",
  height: "100%",
  margin: "0 auto",
  display: "flex",
  flexDirection: "column",
  padding: "30px 0",
  boxSizing: "border-box",
}));

const SearchTotalBox = styled(Box)(({ theme }) => ({
  width: "100%",
  fontSize: 14,
  lineHeight: 1,
  margin: `${theme.spacing(2)} 0`,
  fontWeight: 700,
  color: "rgba(36, 36, 36, 1)",
}));
const TotalNum = styled("span")(() => ({
  color: "rgba(0, 48, 122, 1)",
  fontWeight: 700,
  margin: "0 4px",
}));

// const SearchCategoryDiv = styled("div")(() => ({
//   width: cateWidth,
//   marginRight: 10,
//   paddingRight: 10,
//   overflow: "auto",
//   scrollbarWidth: "none",
//   msOverflowStyle: "none",
//   "&::-webkit-scrollbar": {
//     display: "none",
//   },
// }));
const SearchContent = styled("div")(() => ({
  // width: `calc(100% - ${cateWidth}px)`,
  flex: 1,
  display: "flex",
  flexDirection: "column",
  boxSizing: "border-box",
  padding: "18px 43px 26px 35px",
  borderRadius: 20,
  background:
    " radial-gradient(59.15% 59.81% at 17.077464788732392% -40.0726392251816%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
}));

const ResultHeader = styled(Box)(() => ({
  height: 60,
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
  marginBottom: 14,
}));

const ResultContent = styled("div")(() => ({
  flex: 1,
  minHeight: 0,
  overflow: "auto",
  scrollbarWidth: "none",
  msOverflowStyle: "none",
  "&::-webkit-scrollbar": {
    display: "none",
  },
}));

const AlButton = styled(Button)(() => ({
  height: 32,
  borderRadius: 28,
  color: "#fff",
  fontSize: 14,
  fontWeight: 700,
  padding: "7px",
  boxSizing: "border-box",
}));

const WithoutData = styled("div")(({ theme }) => ({
  color: theme.palette.grey[500],
  padding: theme.spacing(15, 0),
  textAlign: "center",
  width: "100%",
}));
export interface CollapseCardProps {
  title: string;
  options: anyValueProps[] | string[];
  grid: number;
  prop: string;
}

export interface SearchRefProps {
  getSearchParam: () => any;
}

const AiChatButton = ({ action, data }: { action: any; data: number[] }) => {
  const StyleAiChatButton: React.FC<any> = ({ action, data }) => (
    <AlButton
      variant="contained"
      onClick={action}
      disabled={!data.length}
      sx={{
        background: !data.length
          ? "none"
          : "linear-gradient(90deg, rgba(110, 84, 227, 1) 0%, rgba(27, 130, 227, 1) 100%) ",
      }}
    >
      AI对话
    </AlButton>
  );
  const PermissionButton = withPermission(
    StyleAiChatButton,
    PERMISSION_MENU["chat"],
  );
  return <PermissionButton action={action} data={data} />;
};

const ResultHead: React.FC<{
  total: number;
  data: anyValueProps[];
  selectedIds: number[];
  setSelectedIds: React.Dispatch<React.SetStateAction<number[]>>;
}> = ({ total, data, selectedIds, setSelectedIds }) => {
  const dispatch = useAppDispatch();
  const navigator = useNavigate();
  const { listTotal } = useAppSelector((state: RootState) => state.counter);
  const pageIds = useMemo(() => data.map((item) => item.pdfId), [data]);

  // 当前页面的全选状态
  const allChecked = useMemo(() => {
    // pageIds是否为 selectedIds 的子集
    const isSubset = pageIds.every((id) => selectedIds.includes(id));
    return pageIds.length > 0 && isSubset;
  }, [pageIds, selectedIds]);

  const indeterminate = useMemo(() => {
    const hasSame = selectedIds.some((id) => pageIds.includes(id));
    return hasSame && !allChecked;
  }, [pageIds, selectedIds, allChecked]);

  const getChatId = async () => {
    const {
      data: { data },
    } = await getNewChatId();
    dispatch(setActives(data));
  };
  const addChat = () => {
    if (selectedIds.length > 5) {
      message.warning("最多选择5篇资料");
    } else if (listTotal >= 1000) {
      message.warning("聊天窗口已到达1000次,请删除之前创建的聊天窗口");
    } else {
      dispatch(setActives(null));
      dispatch(setSelectedBank({ externalIds: selectedIds, bankType: 2 }));
      navigator("/ai-chat");
      getChatId();
    }
  };

  const handleChange = (e: any) => {
    const checked = e.target.checked;
    if (checked) {
      setSelectedIds(Array.from(new Set([...selectedIds, ...pageIds])));
    } else {
      setSelectedIds([]);
    }
  };

  // const handleSort = (type: string, status: SortStatus) => {
  //   console.log(type, status);
  // };

  return (
    <ResultHeader>
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <Checkbox
          checked={allChecked}
          indeterminate={indeterminate}
          onChange={handleChange}
          sx={{
            width: 18,
            height: 18,
            "& .MuiSvgIcon-root": { fontSize: 18 },
          }}
        />
        <Typography
          variant="body1"
          sx={{ m: "0 10px 0 10px", lineHeight: 1, width: 69 }}
        >
          {selectedIds.length + " / " + total}
        </Typography>
        <AiChatButton action={addChat} data={selectedIds} />
      </Box>

      {/* <Box sx={{ display: "flex", alignItems: "center" }}>
        <SortButton
          label="最相关"
          onSort={(status: SortStatus) => handleSort("relevance", status)}
        />
        <SortButton
          label="最热"
          onSort={(status: SortStatus) => handleSort("hot", status)}
        />
      </Box> */}
    </ResultHeader>
  );
};

const SearchResult: React.FC = () => {
  const { searchRecord } = useSearchRecord();
  const [loading, setLoading] = useState<boolean>(false);
  // const [cateList] = useState<Array<CollapseCardProps>>([
  //   {
  //     title: "类别",
  //     prop: "cate",
  //     options: [
  //       { label: "科学", value: "1" },
  //       { label: "数学", value: "2" },
  //       { label: "地理", value: "3" },
  //     ],
  //     grid: 2,
  //   },
  //   {
  //     title: "期刊",
  //     prop: "article",
  //     options: [
  //       "Journal of Energy Chemistry",
  //       "Journal of Analysis and Testing",
  //       "Nano-Micro Lett",
  //     ],
  //     grid: 1,
  //   },
  //   {
  //     title: "出版年份",
  //     prop: "year",
  //     options: [
  //       { label: "2024(1000)", value: "2024" },
  //       { label: "2023(1000)", value: "2023" },
  //       { label: "2022(1000)", value: "2022" },
  //       { label: "2021(1000)", value: "2021" },
  //       { label: "2020(300)", value: "2020" },
  //       { label: "2018(300)", value: "2018" },
  //     ],
  //     grid: 2,
  //   },
  //   {
  //     title: "出版机构",
  //     prop: "organization",
  //     options: [
  //       "Northwestern University Judd A And Marjorie(1000)",
  //       "National University Of Singapore College Of Design(2000)",
  //       "Nanjing Normal University School Of Chemistry And Material Science(3000)",
  //     ],
  //     grid: 1,
  //   },
  // ]);
  const [pageInfo, setPageInfo] = useState({ page: 1, pageSize: 10 });
  const [total, setTotal] = useState(0);
  const [data, setData] = useState<anyValueProps[]>([]);
  const [currentData, setCurrentData] = useState<anyValueProps[]>([]);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [authorCount, setAuthorCount] = useState<any[]>([]);
  const { searchId } = useParams();
  const { minScore, topK } = useAppSelector((state: RootState) => state.search);
  const onPageChange = (pageParams: any) => {
    const { pageSize, page } = pageParams;
    const indexOfLastItem = page * pageSize;
    const indexOfFirstItem = indexOfLastItem - pageSize;
    const currentItems = data.slice(indexOfFirstItem, indexOfLastItem);
    setCurrentData(currentItems);
    setPageInfo(pageParams);
  };

  const filterHasContent = useMemo(() => {
    const newData = searchRecord.searches.filter((item) => item.content !== "");

    searchRecord.searches = newData;

    return searchRecord;
  }, [searchRecord]);

  const init = async () => {
    try {
      setLoading(true);
      const {
        data: { data, code, total },
      } = await getHybridSearch({
        minScore: minScore >= 0 ? minScore : 0.5,
        topK: topK ? topK : 20,
        ...filterHasContent,
      });
      if (code === 200) {
        const newData = data.slice(0, pageInfo.pageSize);
        setData(data);
        setCurrentData(newData);
        setTotal(total);
        setAuthorCount(authorCount);
      } else {
        return;
      }
    } catch (error) {
      message.error("获取检索结果失败，" + (error as Error)?.message);
      setData([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    init();
  }, [filterHasContent]);

  // const SearchConditionText = useMemo(
  //   () =>
  //     searchRecord.searches.reduce(
  //       (acc, cur, index) =>
  //         acc +
  //         " " +
  //         cur.content +
  //         (index !== searchRecord.searches.length - 1
  //           ? ` ${LogicMap[cur.logic]} `
  //           : ""),
  //       "",
  //     ),
  //   [searchRecord],
  // );

  const onChangeId = (id: number, selected: boolean) => {
    const newSelectedIds = selected
      ? Array.from(new Set([...selectedIds, id]))
      : selectedIds.filter((item) => item !== id);
    setSelectedIds(newSelectedIds);
  };

  return (
    <Root>
      <Breadcrumb
        parent={[
          {
            name: "首页",
            path: "/paper-search",
          },
        ]}
        current={`检索结果`}
      />
      <Main>
        <SearchInput
          authorCount={authorCount}
          total={total}
          setPageInfo={setPageInfo}
        />
        <SearchTotalBox>
          查询结果包含<TotalNum>{total}</TotalNum>条记录
        </SearchTotalBox>
        <Box
          sx={{
            flex: 1,
            minHeight: 0,
            display: "flex",
            boxSizing: "border-box",
            width: "100%",
          }}
        >
          {/* <SearchCategoryDiv>
            {cateList.map((item) => (
              <Box sx={{ mb: 1.5 }} key={item.prop}>
                <CollapseCard {...item} />
              </Box>
            ))}
          </SearchCategoryDiv> */}
          <SearchContent>
            <ResultHead
              total={total}
              data={currentData}
              selectedIds={selectedIds}
              setSelectedIds={setSelectedIds}
            />
            {loading && <WithoutData>加载中 请稍候...</WithoutData>}
            {!loading && currentData.length <= 0 && (
              <WithoutData>暂无数据</WithoutData>
            )}
            {!loading && currentData.length > 0 && (
              <>
                <ResultContent>
                  <SearchResultList
                    selectedIds={selectedIds}
                    onChangeId={onChangeId}
                    data={currentData}
                    searchId={searchId}
                  />
                </ResultContent>
                <MyPaperPagination
                  boundaryCount={0}
                  total={total}
                  page={pageInfo.page}
                  pageSizeOptions={[10, 20, 50]}
                  onChangePage={onPageChange}
                  pageSize={pageInfo.pageSize}
                />
              </>
            )}
          </SearchContent>
        </Box>
      </Main>
    </Root>
  );
};

export default SearchResult;
