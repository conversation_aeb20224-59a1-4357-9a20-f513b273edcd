import React, { useEffect, useState } from "react";
import { TextField, styled, Typography, Menu, MenuItem } from "@mui/material";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import {
  deleteCurrentChat,
  setActives,
  setSelectedBank,
  changeValue,
  setListTotal,
} from "@/store/counterSlice";
import MoreVertTwoToneIcon from "@mui/icons-material/MoreVertTwoTone";
import PdfIcon from "@/assets/pdf-icon.svg";
import PdfMultiple from "@/assets/pdf-multiple.svg";
import Disabled from "@/assets/disabled.svg";
import FileIcon from "@/assets/file-icon.svg";
import {
  chatUpdate,
  getChatHistory,
  getNewChatId,
  getPaperItem,
} from "@/api/chat";
import { leftHistoryListProps } from "./common";
import SelectDialog from "./SelectDialog";
import CustomDialog from "@/components/Dialog";
import { useNavigate } from "react-router-dom";
import GroupPopover from "./GroupPopover";
import { operateList } from "./common";
const Root = styled("div")(() => ({
  height: "100%",
  overflowY: "scroll",
}));

const ListBox = styled("div", {
  shouldForwardProp: (props) => props !== "isActive",
})<{ isActive: boolean }>(({ theme, isActive }) => ({
  height: 40,
  fontSize: 14,
  padding: `2px 12px ${theme.spacing(0.5)} 12px`,
  margin: `${theme.spacing(1.5)} 0`,
  boxSizing: "border-box",
  display: "flex",
  justifyContent: "space-between",
  alignItems: isActive ? "center" : "",
  cursor: "pointer",
  borderRadius: "20px",
  background: isActive ? "rgba(255, 255, 255, 1)" : "",
  "&:hover": {
    background: "rgba(255, 255, 255, 1)",
  },
}));

const MenuStyles = styled(Menu)(() => ({
  ".css-1tktgsa-MuiPaper-root-MuiPopover-paper-MuiMenu-paper": {
    width: 120,
  },
  ".css-1toxriw-MuiList-root-MuiMenu-list": {
    padding: "8px 4px",
  },
}));
const IconStyle = styled("img")(() => ({
  width: 16,
  height: 18,
  margin: "2px 5px 0 0",
}));

const TimeStyle = styled("div")(() => ({
  fontSize: 12,
}));
const ListItem = styled("div")(() => ({
  color: "rgba(64, 64, 64, 1)",
  flex: 1,
  overflow: "hidden",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
  fontSize: "14px",
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  "& .MuiOutlinedInput-input": {
    height: 22,
    padding: `0 ${theme.spacing(1)}`,
    fontSize: 12,
  },
}));
const ContentBox = styled("div")(() => ({
  height: "100px",
  lineHeight: "100px",
  paddingLeft: "50px",
}));

// const IconStyles = styled("div")(() => ({
//   marginLeft: "3px",
//   background: "rgba(240,240,240)",
//   width: "18px",
//   height: "18px",
//   borderRadius: "50%",
//   display: "flex",
//   alignItems: "center",
//   justifyContent: "center",
// }));

interface Props {
  setTotal: (value: number) => void;
  total: number;
}
const ChatList: React.FC<Props> = ({ setTotal, total }) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [open, setOpen] = useState<boolean>(false);
  const [delOpen, setDelOpen] = useState<boolean>(false);
  const { active, isFirst, appSetting } = useAppSelector(
    (state: RootState) => state.counter,
  );
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const buttonOpen = Boolean(anchorEl);
  const [data, setData] = useState<leftHistoryListProps[]>([]);
  const [dataItem, setDataItem] = useState<leftHistoryListProps>();
  const [isEdit, setIsEdit] = useState(false);
  const [value, setValue] = useState<string>("");
  const [info, setInfo] = useState<{ page: number; size: number }>({
    page: 1,
    size: 15,
  });
  const [popoverOpen, setPopoverOpen] = useState<HTMLButtonElement | null>(
    null,
  );
  const [chatId, setChatId] = useState<string>("");
  useEffect(() => {
    getScrollList();
  }, [info, isFirst]);
  useEffect(() => {
    setIsEdit(false);
  }, [active]);

  const getScrollList = async () => {
    try {
      const {
        data: { data, total },
      } = await getChatHistory({
        ...info,
      });
      if (data.length) {
        setData(data);
      } else {
        setData([]);
      }
      dispatch(setListTotal(total));
      setTotal(total);
    } catch (error) {
      dispatch(setSelectedBank({}));
      message.error("获取聊天记录失败" + `${(error as Error)?.message}`);
    }
  };

  const handleClick = async (item: leftHistoryListProps) => {
    const { id, type, externalIds } = item;
    dispatch(setActives(id));
    try {
      if (type !== 0) {
        const ids = JSON.parse(externalIds);
        const {
          data: { data, code },
        } = await getPaperItem(ids);
        if (code === 200) {
          const name = data.map((item: any) => item.name).join("+");
          dispatch(
            setSelectedBank({
              data,
              bankType: type,
              externalIds: ids,
              ids,
              name,
            }),
          );
        }
      } else {
        dispatch(setSelectedBank({ bankType: type }));
      }
    } catch (error) {
      message.error("获取库信息失败" + `${(error as Error)?.message}`);
    }
    navigate(`/ai-chat/${id}`);
  };
  const handleEdit = (
    e:
      | React.FocusEvent<HTMLInputElement | HTMLTextAreaElement, Element>
      | React.MouseEvent<HTMLImageElement, MouseEvent>,
    item: string,
  ) => {
    e.stopPropagation();
    setIsEdit(true);
    setValue(item);
  };

  const handleBlur = (item: leftHistoryListProps) => {
    setIsEdit(false);
    if (item.name !== value) {
      updateMsg(item);
    }
  };

  const updateMsg = async (item: leftHistoryListProps) => {
    try {
      if (value) {
        const { data } = await chatUpdate({
          chatId: item.id,
          newName: value,
        });
        if (data.code === 200) {
          message.success("修改成功");
          getScrollList();
          setIsEdit(false);
        }
      }
    } catch (error) {
      message.error("修改失败" + `${(error as Error)?.message}`);
    }
  };
  const handleEnterKey = async (
    event: { key: string },
    item: leftHistoryListProps,
  ) => {
    if (event.key === "Enter") {
      // 处理按下Enter键的逻辑
      updateMsg(item);
    }
  };
  const handleChange = (value: string) => {
    setValue(value);
  };

  const handleBtnItem = (item: leftHistoryListProps) => {
    setDelOpen(true);
    setDataItem(item);
  };

  const handleShare = (e: any, id: string) => {
    setPopoverOpen(e.currentTarget);
    setChatId(id);
  };

  const ChangeSettingValue = (field: string, value: number) => {
    if (appSetting?.[field] !== value) {
      dispatch(changeValue({ field, value }));
    }
  };
  const getChatId = async () => {
    const {
      data: { data },
    } = await getNewChatId();
    dispatch(setActives(data));
  };
  const handleOk = async () => {
    try {
      const { data } = await chatUpdate({
        chatId: dataItem?.id,
        deleted: 1,
      });
      if (data.code === 200) {
        message.success("删除成功");
        dispatch(setActives(null));
        setDelOpen(false);
        getScrollList();
        dispatch(deleteCurrentChat(active));
        dispatch(setSelectedBank({}));
        ChangeSettingValue("temperature", 0.2);
        ChangeSettingValue("normal", 50);
        getChatId();
      }
    } catch (error) {
      message.error("删除失败" + `${(error as Error)?.message}`);
    }
  };

  const handleTopUp = async (item: leftHistoryListProps) => {
    try {
      const { data } = await chatUpdate({
        chatId: item.id,
        topped: item.topped === 1 ? 0 : 1,
      });
      if (data.code === 200) {
        getScrollList();
      }
    } catch (error) {
      message.error("置顶失败" + `${(error as Error)?.message}`);
    }
  };
  // 添加资料
  const handleClickOpen = () => {
    setOpen(true);
  };

  // 监听滚动事件
  const handleScroll = (e: any) => {
    const bottom =
      e.target.scrollHeight === e.target.scrollTop + e.target.clientHeight;
    const { page, size } = info;
    // 判断是否加载数据
    const isHasMoreData = total > size;
    if (bottom && isHasMoreData) {
      setInfo({ page, size: size + 15 });
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const buttonHandleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleSelect = (e: any, type: string, item: leftHistoryListProps) => {
    switch (type) {
      case "del":
        handleBtnItem(item);
        handleClose();
        break;
      case "copy":
        handleShare(e, item.id);
        break;
      case "edit":
        handleEdit(e, item.name);
        handleClose();
        break;
      case "topUp":
        handleTopUp(item);
        handleClose();
        break;
      case "add":
        handleClickOpen();
        handleClose();
        break;
      default:
        break;
    }
  };
  return (
    <Root onScroll={handleScroll}>
      {data.length > 0 &&
        data.map((item, index) => (
          <ListBox
            isActive={active === item.id}
            key={index}
            onClick={() => handleClick(item)}
          >
            {item.type === 2 && JSON.parse(item.externalIds).length === 1 ? (
              <IconStyle src={PdfIcon} />
            ) : item.type === 2 && JSON.parse(item.externalIds).length > 1 ? (
              <IconStyle src={PdfMultiple} />
            ) : item.type === 1 ? (
              <IconStyle src={FileIcon} />
            ) : (
              <IconStyle src={Disabled} />
            )}
            {isEdit && active === item.id ? (
              <StyledTextField
                variant="outlined"
                defaultValue={item.name}
                onBlur={() => handleBlur(item)}
                onChange={(e) => handleChange(e.target.value)}
                onKeyDown={(e) => handleEnterKey(e, item)}
              />
            ) : active !== item.id ? (
              <ListItem title={item.name}>
                {item.name}
                <TimeStyle>{item.lastChatTime}</TimeStyle>
              </ListItem>
            ) : (
              <ListItem title={item.name}>{item.name}</ListItem>
            )}
            {active === item.id && (
              <div>
                <Typography
                  aria-owns={"mouse-over-popover"}
                  aria-haspopup="true"
                  sx={{
                    color: "#ccc",
                    cursor: "pointer",
                    display: "flex",
                    alignItems: "center",
                  }}
                  onClick={buttonHandleClick}
                >
                  <MoreVertTwoToneIcon fontSize="small" />
                </Typography>
                <MenuStyles
                  id="basic-menu"
                  anchorEl={anchorEl}
                  open={buttonOpen}
                  onClose={handleClose}
                  anchorOrigin={{
                    vertical: "top",
                    horizontal: "right",
                  }}
                  transformOrigin={{
                    vertical: "top",
                    horizontal: "left",
                  }}
                  autoFocus={false}
                  aria-hidden={false}
                >
                  {operateList
                    .filter(
                      (operate) => !(item.type !== 2 && operate.key === "add"),
                    )
                    .map((operate, operateIndex) => (
                      <MenuItem
                        key={`${operate.key}-${operateIndex}`}
                        onClick={(e) => handleSelect(e, operate.key, item)}
                      >
                        {item.topped === 1 && operate.key === "topUp"
                          ? operate.cancelName
                          : operate.name}
                      </MenuItem>
                    ))}
                </MenuStyles>
              </div>
            )}
          </ListBox>
        ))}

      {Boolean(popoverOpen) && (
        <GroupPopover
          anchorEl={popoverOpen}
          chatId={chatId}
          open={Boolean(popoverOpen)}
          onClose={() => {
            setPopoverOpen(null);
            handleClose();
          }}
          anchorOrigin={{ vertical: "top", horizontal: "right" }}
        />
      )}

      <CustomDialog
        open={delOpen}
        setDialogOpen={setDelOpen}
        title="删除对话记录？"
        okButtonProps={{ onOk: handleOk }}
        cancelButtonProps={{ onCancel: () => setDelOpen(false) }}
      >
        <ContentBox slot="content">
          <span>删除对话记录后无法恢复和找回，请谨慎操作</span>
        </ContentBox>
      </CustomDialog>
      <SelectDialog open={open} setOpen={setOpen} isAddPaper={true} />
    </Root>
  );
};

export default ChatList;
