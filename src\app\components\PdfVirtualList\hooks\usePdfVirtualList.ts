import { useState, useRef, useCallback, useEffect } from "react";
import { PageLayout } from "../utils/pdfUtils";
import {
  formatAnnotations,
  clearAnnotationCache,
} from "../utils/annotationUtils";
import {
  FormattedAnnotation,
  PageDimensions,
  VisibleRange,
} from "../types/types";
import { useOptimizedScroll } from "./useOptimizedScroll";
import { usePageCache } from "./usePageCache";

interface UsePdfVirtualListProps {
  initialVisiblePages: number;
  containerRef: React.RefObject<HTMLDivElement>;
  coordsData?: any[];
}

export const usePdfVirtualList = ({
  initialVisiblePages,
  containerRef,
  coordsData,
}: UsePdfVirtualListProps) => {
  // 基础状态
  const [numPages, setNumPages] = useState<number>(0);
  const [pageLayouts, setPageLayouts] = useState<PageLayout[]>([]);
  const [visibleRange, setVisibleRange] = useState<VisibleRange>({
    start: 0,
    end: 0,
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [scale, setScale] = useState(1);
  const [containerWidth, setContainerWidth] = useState(0);
  const [isDocumentLoaded, setIsDocumentLoaded] = useState(false);
  const [formattedAnnotations, setFormattedAnnotations] = useState<
    FormattedAnnotation[][]
  >([]);

  // 缓存和性能相关的refs
  const pageHeightCache = useRef<Record<number, number>>({});
  const pageDimensions = useRef<Record<number, PageDimensions>>({});

  // 使用优化的缓存Hook
  const pageCache = usePageCache();

  // 优化的滚动处理回调
  const handleVisibleRangeChange = useCallback((range: VisibleRange) => {
    setVisibleRange(range);
  }, []);

  const handleCurrentPageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // 使用优化的滚动Hook
  const { scrollToPage, getPerformanceMetrics } = useOptimizedScroll({
    containerRef,
    pageLayouts,
    numPages,
    initialVisiblePages,
    onVisibleRangeChange: handleVisibleRangeChange,
    onCurrentPageChange: handleCurrentPageChange,
  });

  // 更新布局
  const updatePageHeight = useCallback(
    (index: number, actualHeight: number) => {
      if (pageHeightCache.current[index] === actualHeight) return;

      pageHeightCache.current[index] = actualHeight;

      setPageLayouts((prev) => {
        const newLayouts = [...prev];
        const pageGap = 20; // 调整页面间距为20px
        newLayouts[index] = { ...newLayouts[index], height: actualHeight };

        // 重新计算所有页面位置
        let currentY = 0;
        for (let i = 0; i < newLayouts.length; i++) {
          // 先更新Y坐标
          newLayouts[i] = {
            ...newLayouts[i],
            y: currentY,
          };

          // 然后计算下一个Y坐标的起始位置
          const gap = i < newLayouts.length - 1 ? pageGap : 0;
          currentY += newLayouts[i].height + gap;

          // 添加布局计算日志
        }

        // 检查相邻页面之间的实际间距
        // for (let i = 0; i < newLayouts.length - 1; i++) {
        //   const currentPage = newLayouts[i];
        //   const nextPage = newLayouts[i + 1];
        //   const actualGap = nextPage.y - (currentPage.y + currentPage.height);

        //   // 只记录与当前修改页面相关的间距
        // }

        return newLayouts;
      });
    },
    [scale],
  ); // 添加scale作为依赖，确保缩放变化时更新布局

  const updatePageDimensions = useCallback(
    (pageIndex: number, width: number, height: number) => {
      const prevDimensions = pageDimensions.current[pageIndex];
      // 只有当尺寸发生变化时才更新
      if (
        !prevDimensions ||
        prevDimensions.width !== width ||
        prevDimensions.height !== height
      ) {
        // 确保使用正确的方式更新 ref
        pageDimensions.current = {
          ...pageDimensions.current,
          [pageIndex]: { width, height },
        };
        if (coordsData && coordsData.length > 0) {
          const formatted = formatAnnotations(
            coordsData,
            pageDimensions.current,
          );
          setFormattedAnnotations(formatted);
        }
      }
    },
    [coordsData],
  );

  // 测量容器宽度
  useEffect(() => {
    if (containerRef.current) {
      const updateWidth = () => {
        if (containerRef.current) {
          const width = containerRef.current.clientWidth - 20; // 减去左右padding (20px * 2)

          setContainerWidth(width);

          // 当容器宽度变化时，更新所有页面的尺寸
          if (Object.keys(pageDimensions.current).length > 0) {
            // 更新所有页面的宽度，保持高度不变
            const updatedDimensions = { ...pageDimensions.current };
            Object.keys(updatedDimensions).forEach((pageIndex) => {
              const page = updatedDimensions[Number(pageIndex)];
              const aspectRatio = page.height / page.width;
              updatedDimensions[Number(pageIndex)] = {
                width,
                height: width * aspectRatio,
              };
            });
            pageDimensions.current = updatedDimensions;

            // 重新格式化coordsData
            if (coordsData && coordsData.length > 0) {
              const formatted = formatAnnotations(
                coordsData,
                pageDimensions.current,
              );
              setFormattedAnnotations(formatted);
            }
          }
        }
      };

      updateWidth();

      // 安全检查 ResizeObserver 是否存在
      if (typeof ResizeObserver !== "undefined") {
        const resizeObserver = new ResizeObserver(updateWidth);
        resizeObserver.observe(containerRef.current);

        return () => {
          if (containerRef.current) {
            resizeObserver.unobserve(containerRef.current);
          }
        };
      } else {
        // 降级方案：使用窗口大小变化事件
        window.addEventListener("resize", updateWidth);
        return () => {
          window.removeEventListener("resize", updateWidth);
        };
      }
    }
  }, [containerRef]);

  // 处理缩放比例变化
  useEffect(() => {
    // 只有在页面已加载且有页面维度数据时才处理
    if (isDocumentLoaded && Object.keys(pageDimensions.current).length > 0) {
      // 更新所有页面的尺寸，保持原始宽高比
      const updatedDimensions = { ...pageDimensions.current };

      // 清除现有的页面高度缓存，以便重新计算所有页面高度
      pageHeightCache.current = {};

      // 清除页面渲染缓存
      pageCache.clearCache();

      // 强制重新计算所有页面的布局
      const pageOrder: number[] = [];

      // 遍历并更新每个页面的尺寸
      Object.keys(updatedDimensions).forEach((pageIndexStr) => {
        const pageIndex = Number(pageIndexStr);
        pageOrder.push(pageIndex);
        const page = updatedDimensions[pageIndex];

        // 计算新的尺寸
        const baseWidth = containerWidth;
        const aspectRatio = page.height / page.width;

        // 根据缩放比例计算新的尺寸
        const newWidth = baseWidth * scale;
        const newHeight = newWidth * aspectRatio;

        // 更新页面维度
        updatedDimensions[pageIndex] = {
          width: newWidth,
          height: newHeight,
        };
      });

      // 按照页码顺序依次更新页面高度，确保布局正确计算
      pageOrder.sort((a, b) => a - b);
      pageOrder.forEach((pageIndex) => {
        const page = updatedDimensions[pageIndex];
        updatePageHeight(pageIndex, page.height);
      });

      // 更新页面维度引用
      pageDimensions.current = updatedDimensions;

      // 重新格式化注释数据
      if (coordsData && coordsData.length > 0) {
        const formatted = formatAnnotations(coordsData, pageDimensions.current);
        setFormattedAnnotations(formatted);
      }

      // 移除这段代码，避免循环更新
      /* 
      setTimeout(() => {
        if (pageLayouts.length > 1) {
          for (let i = 0; i < pageLayouts.length - 1; i++) {
            const currentPage = pageLayouts[i];
            const nextPage = pageLayouts[i + 1];
            const actualGap = nextPage.y - (currentPage.y + currentPage.height);
            if (actualGap !== 20) {
              // 如果发现间距不正确，尝试强制修复
              forceUpdatePageGaps();
            }
          }
        }
      }, 100);
      */
    }
  }, [scale, isDocumentLoaded, containerWidth, updatePageHeight, coordsData]); // 移除 pageLayouts 依赖

  // 强制更新页面间距
  const forceUpdatePageGaps = useCallback(() => {
    if (pageLayouts.length <= 1) return;

    const pageGap = 20; // 增加页面间距，从20改为40

    // 先创建新的布局副本
    const newLayouts = [...pageLayouts];

    // 重新计算所有页面位置，确保间距正确
    let currentY = 0;
    for (let i = 0; i < newLayouts.length; i++) {
      // 更新Y坐标
      newLayouts[i] = {
        ...newLayouts[i],
        y: currentY,
      };

      // 计算下一个页面的Y坐标起点
      const gap = i < newLayouts.length - 1 ? pageGap : 0;
      currentY += newLayouts[i].height + gap;
    }

    // 更新布局
    setPageLayouts(newLayouts);
  }, [pageLayouts]);

  // 初始化页面布局
  useEffect(() => {
    if (numPages > 0) {
      const initialHeight = 800; // 初始估计高度
      const pageGap = 20; // 调整页面间距为40px，从20改为40
      const initialLayouts = Array.from({ length: numPages }, (_, i) => ({
        y: i * (initialHeight + pageGap),
        height: initialHeight,
      }));
      setPageLayouts(initialLayouts);
      setIsDocumentLoaded(true);
    }
  }, [numPages]);

  // 注意：滚动处理现在由useOptimizedScroll Hook处理

  // 页码变化处理 - 使用优化的滚动函数
  const handlePageChange = useCallback(
    (page: number) => {
      if (page < 1 || page > numPages) return;
      scrollToPage(page, "smooth");
    },
    [numPages, scrollToPage],
  );

  // 文档加载完成处理
  const handleDocumentLoadSuccess = useCallback(
    ({ numPages }: { numPages: number }) => {
      setNumPages(numPages);
    },
    [],
  );

  // 捕获加载失败错误
  const handleDocumentLoadError = useCallback((error: Error) => {
    console.error("文档加载失败:", error);
  }, []);

  // 计算总高度
  const calculateTotalHeight = useCallback(() => {
    if (pageLayouts.length === 0) {
      return 800 * Math.max(1, numPages);
    }

    const totalHeight = pageLayouts.reduce((sum, layout, index) => {
      // 只在非最后一页添加间距
      const gap = index < pageLayouts.length - 1 ? 20 : 0; // 调整页面间距为20px
      return sum + layout.height + gap;
    }, 0);

    return totalHeight;
  }, [pageLayouts, numPages]);

  // 清理函数 - 在组件卸载时清理缓存
  useEffect(() => {
    return () => {
      pageCache.clearExpiredCache();
      clearAnnotationCache();
    };
  }, [pageCache]);

  // 获取性能统计信息
  const getStats = useCallback(() => {
    return {
      pageCache: pageCache.getCacheStats(),
      scroll: getPerformanceMetrics(),
    };
  }, [pageCache, getPerformanceMetrics]);

  return {
    // 基础状态
    numPages,
    pageLayouts,
    visibleRange,
    currentPage,
    scale,
    containerWidth,
    isDocumentLoaded,
    formattedAnnotations,

    // 核心功能函数
    updatePageHeight,
    updatePageDimensions,
    handlePageChange,
    handleDocumentLoadSuccess,
    handleDocumentLoadError,
    setScale,
    calculateTotalHeight,
    forceUpdatePageGaps,

    // 新增的优化功能
    scrollToPage,
    getStats,
    clearCaches: () => {
      pageCache.clearCache();
      clearAnnotationCache();
    },
  };
};
