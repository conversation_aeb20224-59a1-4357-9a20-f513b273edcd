import DynamicForm, { RefProps } from "@/components/DynamicForm";
import Breadcrumb from "@/components/Breadcrumb";
import { BasicColumns, EmailColumns, FormRefProps } from "./setting";
import { useQuery } from "@tanstack/react-query";
import { editDepartMent, editUserInfo, getUserInfomation } from "@/api/login";
import { anyValueProps } from "@/types/common";

const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  boxSizing: "border-box",
}));

const ContentPaper = styled("div")(() => ({
  width: "100%",
  height: "calc(100% - 60px)",
  boxSizing: "border-box",
  overflow: "auto",
  padding: "20px 146px",
}));

const InfoBox = styled("div")(() => ({
  width: "100%",
  height: "100%",
  boxSizing: "border-box",
  background:
    "radial-gradient(50.17% 59.87% at 17.118307997356247% -40.128755364806864%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0.5) 100%), rgba(247, 247, 247, 1)",
  borderRadius: 20,
  padding: "0 48px",
}));

const InfoContent = styled("div")(() => ({
  width: "100%",
  height: "calc(100% - 86px)",
  boxSizing: "border-box",
  display: "flex",
  justifyContent: "center",
}));

const FormContent = styled("div")(() => ({
  width: 471,
  height: "100%",
  boxSizing: "border-box",
}));

const TitleBox = styled("div")(() => ({
  width: "100%",
  height: 58,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: 18,
  fontWeight: 700,
  color: "rgba(64, 64, 64, 1)",
}));

const BasicInfoBox = styled("div")(() => ({
  width: "100%",
  height: 458,
  background: "rgba(255, 255, 255, 1)",
  border: "1px solid rgba(242, 242, 242, 1)",
  borderRadius: 19,
  boxSizing: "border-box",
  paddingTop: 24,
  paddingRight: 56,
}));

const AcountInfoBox = styled("div")(() => ({
  width: "100%",
  height: 103,
  background: "rgba(255, 255, 255, 1)",
  border: "1px solid rgba(242, 242, 242, 1)",
  boxSizing: "border-box",
  borderRadius: 19,
  paddingRight: 56,
  paddingTop: 31,
}));

const SubmitContent = styled("div")(() => ({
  width: "100%",
  height: "86px",
  boxSizing: "border-box",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  borderTop: "1px dashed rgba(207, 207, 207, 1)",
}));

const SubmitButton = styled(Button)(() => ({
  width: 105,
  height: 44,
  background:
    "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
  borderRadius: 28,
  boxShadow: "0px 0px 12px  rgba(24, 112, 199, 0.3)",
  color: "#fff",
  fontSize: 16,
  fontWeight: 400,
}));

const PersonCenter: React.FC = () => {
  const breadcrumbInfo = useMemo(
    () => ({
      current: "账号设置",
      parent: [
        {
          name: "个人中心",
          path: "/person-center",
        },
      ],
    }),
    [],
  );
  const formObjsRef = useRef<FormRefProps>({});
  const [rowOption, setRowOption] = useState<anyValueProps>({});
  const handleRefAssignment = (key: string) => (ref: RefProps | null) => {
    if (ref) {
      formObjsRef.current[key] = ref;
    }
  };

  const mergeArrayToObject = (
    array: Array<Record<string, any>>,
  ): Record<string, any> =>
    array.reduce((acc, current) => ({ ...acc, ...current }), {});

  const handleSubmit = async () => {
    try {
      const promises = [
        formObjsRef.current.basic.submit(),
        formObjsRef.current.email.submit(),
      ];
      const results = await Promise.all(promises);
      const data = mergeArrayToObject(results);
      const params = {
        gender: data.gender,
        name: data.name,
        phone: data.phone,
      };
      const depaertmentParams = [
        {
          position: data.position,
          organization: data.company,
        },
      ];

      const [departmentRes, userInfoRes] = await Promise.all([
        editDepartMent(depaertmentParams), // 发送部门编辑请求
        editUserInfo(params), // 发送用户信息编辑请求
      ]);

      // 处理 departmentRes
      if (departmentRes.data.code !== 200) {
        message.error(departmentRes.data.message || "部门修改失败");
      }

      // 处理 userInfoRes
      if (userInfoRes.data.code !== 200) {
        message.error(userInfoRes.data.message || "用户信息修改失败");
      }

      if (departmentRes.data.code === 200 && userInfoRes.data.code === 200) {
        message.success("修改成功");
      }
      refetch();
    } catch (error) {
      window.console.error(error);
    }
  };

  const queryRequest = async () => {
    const response = await getUserInfomation();
    return response.data;
  };

  const { data, status, error, refetch } = useQuery({
    queryKey: ["getUserInfo"],
    queryFn: queryRequest,
  });

  const dataProcessing = (data: anyValueProps) => {
    const dataProcessing = {
      basic: {
        username: data?.name,
        name: data?.name,
        phone: data?.phone,
        company: data?.affiliations[0].organization,
        position: data?.affiliations[0].position,
        gender: data?.gender,
      },
      email: {
        email: data?.email,
      },
    };
    setRowOption(dataProcessing);
    return dataProcessing;
  };

  useEffect(() => {
    switch (status) {
      case "success":
        window.console.log(data.result);
        dataProcessing(data.result);
        break;
      case "error":
        message.error("获取资料列表失败, " + error.message);
        break;
    }
  }, [data, status]);
  return (
    <Root>
      <Breadcrumb
        parent={breadcrumbInfo.parent}
        current={breadcrumbInfo.current}
      />
      <ContentPaper>
        <InfoBox>
          <InfoContent>
            <FormContent>
              <TitleBox>基本信息</TitleBox>
              <BasicInfoBox>
                <DynamicForm
                  // size="small"
                  columns={BasicColumns}
                  ref={handleRefAssignment("basic")}
                  labelWidth={100}
                  formData={rowOption["basic"]}
                  rowSpacing={0}
                />
              </BasicInfoBox>
              <TitleBox>账号信息</TitleBox>
              <AcountInfoBox>
                <DynamicForm
                  columns={EmailColumns}
                  ref={handleRefAssignment("email")}
                  labelWidth={100}
                  formData={rowOption["email"]}
                  rowSpacing={0}
                />
              </AcountInfoBox>
            </FormContent>
          </InfoContent>
          <SubmitContent>
            <SubmitButton onClick={handleSubmit}>保存</SubmitButton>
          </SubmitContent>
        </InfoBox>
        {/* {InfoOptions.map((item) => (
          <InfoBox key={item.type}>
            <TitleBox variant="outlined">{item.title}</TitleBox>
            <DynamicForm
              size="small"
              columns={item.columns}
              ref={handleRefAssignment(item.type)}
              labelWidth={100}
              formData={rowOption[item.type]}
            />
          </InfoBox>
        ))}
        <SubmitBar>
          <SubmitButton variant="contained" onClick={handleSubmit}>
            保存
          </SubmitButton>
        </SubmitBar>
        <MemberBar elevation={1}>
          <MemberTitle>会员信息</MemberTitle>
          <MemberContent>
            <DeadlineImg></DeadlineImg>
            <MemberInfo>
              <TimeTitle>权限有效期至</TimeTitle>
              <TimeLabel>
                2024-11-30<ExpireNote>权益即将到期</ExpireNote>
              </TimeLabel>
              <DeadLineNote>
                权益即将到期，请联系销售续费后继续使用
              </DeadLineNote>
            </MemberInfo>
          </MemberContent>
        </MemberBar> */}
      </ContentPaper>
    </Root>
  );
};
export default PersonCenter;
