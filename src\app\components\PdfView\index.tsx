/**
 * PDF组件参数
 * file: PDF url参数
 * width: 当前页的宽度
 * height: 当前页的高度
 * pdfData: 对话页中解析的pdf数据
 * operation: 操作按钮数组
 * getParagraph：点击操作按钮时触发的回调参数为value(string)和type(string)
 */

import { Virtuoso } from "react-virtuoso";
import "react-pdf/dist/Page/TextLayer.css";
import "react-pdf/dist/Page/AnnotationLayer.css";
import { pdfjs, Document, Page } from "react-pdf";
import {
  ParagraphProps,
  AnnotationProps,
  OperationProps,
  TextSelectionProp,
} from "./common";
import CloseIcon from "@mui/icons-material/Close";
import { debounce } from "lodash";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
pdfjs.GlobalWorkerOptions.workerSrc = window.APP_CONFIG.BASE_PATH
  ? `${window.APP_CONFIG.BASE_PATH}/pdf.worker.min.js`
  : "/pdf.worker.min.js";

interface PdfViewProps {
  file?: string;
  width: number;
  height?: number;
  left?: number;
  top?: number;
  pdfData?: any;
  operation?: OperationProps[];
  getParagraph?: (value: string, type: string) => void;
}

const PageDiv = styled("div", {
  shouldForwardProp: (props) => props !== "PageHeight",
})<{ PageHeight: number }>(({ PageHeight }) => ({
  height: `${PageHeight ? PageHeight : 800}px`,
  marginBottom: "10px",
  position: "relative",
  overflowX: "hidden",
  overflowY: "hidden",
}));

const AnnotationDiv = styled("div", {
  shouldForwardProp: (props) => props !== "StyleGroup",
})<{ StyleGroup: AnnotationProps }>(({ StyleGroup }) => ({
  width: StyleGroup.x[1] - StyleGroup.x[0] + "px",
  height: StyleGroup.y[1] - StyleGroup.y[0] + "px",
  top: `${StyleGroup.y[0]}px`,
  left: `${StyleGroup.x[0]}px`,
  position: "absolute",
  background: "none",
  zIndex: 1,
}));

const PdfButtonGroup = styled("div")(() => ({
  display: "none",
  position: "absolute",
  zIndex: 1000,
  top: "0",
  left: "0",
}));

const Buttongroup = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  height: "36px",
  width: "330px",
  background: "#fff",
  boxShadow: "2px 4px 9px 0 rgba(25, 45, 79, .5)",
  borderRadius: "20px",
  paddingRight: "10px",
  border: "1px solid #e8e8e8",
  position: "relative",
}));

const ButtonItem = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const OperationButton = styled(Button)(() => ({
  borderRadius: "20px",
  outline: "none",
  border: "none",
  boxShadow: "none",
  cursor: "pointer",
  lineHeight: "36px",
  padding: "0 14px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: "14px",
  fontWeight: "400",
  color: "#192d4f",
}));

const ButtonLine = styled("span")(() => ({
  height: "12px",
  display: "block",
  borderLeft: "1px solid #e8e8e8",
}));

const CloseButton = styled("div")(({ theme }) => ({
  marginLeft: theme.spacing(0.7),
  cursor: "pointer",
  fontSize: "16px",
  lineHeight: "16px",
}));

const PageOperationBox = styled("div")(() => ({
  width: 340,
  height: 56,
  background: "#fff",
  border: "1px solid rgba(235, 235, 235, 1)",
  borderRadius: 28,
  boxShadow: "0px 0px 16px  rgba(0, 0, 0, 0.15)",
  position: "absolute",
  bottom: 21,
  left: "calc(50% - 170px)",
  padding: "0 31px",
  boxSizing: "border-box",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  zIndex: 100,
  userSelect: "none",
}));

const OperationNav = styled("div")(() => ({
  width: 72,
  height: 18,
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
}));

const StyleBotton = styled("div")(() => ({
  width: 18,
  height: 18,
  borderRadius: "50%",
  background: "#fff",
  border: "1px solid rgba(235, 235, 235, 1)",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  boxSizing: "border-box",
  cursor: "pointer",
}));

const StyleLabel = styled("div")(() => ({
  fontSize: 14,
  fontWeight: 400,
  color: "rgba(0, 0, 0, 1)",
}));

const PdfView: React.FC<PdfViewProps> = ({
  file,
  width,
  height,
  pdfData,
  operation,
  getParagraph,
  left,
  top,
}) => {
  const divRef = useRef<HTMLDivElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const pageRefs = useRef<Array<React.RefObject<HTMLDivElement>>>([]);
  const btnRef = useRef<HTMLDivElement>(null);
  const virtuosoRef = useRef<any>(null);
  const [numPages, setNumPages] = useState<number>(2);
  const [pageHeight, setPageHeight] = useState<number>(0);
  const [scrollTop, setScrollTop] = useState<number>(0);
  const [coordData, setCoordData] = useState<AnnotationProps[][]>([]);
  const [hoverItem, setHoverItem] = useState<AnnotationProps | null>(null);
  const [isTextSelected, setIsTextSelected] = useState<boolean>(false);
  const [textSelected, setTextSelected] = useState<TextSelectionProp | null>(
    null,
  );

  const [indexPage, setIndexPage] = useState<number>(0);
  const [scale, setScale] = useState<number>(1);

  const onDocumentLoadSuccess = useCallback(
    ({ numPages }: { numPages: number }) => {
      setNumPages(numPages);
      setIndexPage(0);
      pageRefs.current = Array(numPages)
        .fill(0)
        .map((_, i) => pageRefs.current[i] || createRef<HTMLDivElement>());
    },
    [],
  );

  const formatPdfData = (params: ParagraphProps[]): AnnotationProps[][] => {
    const annotationsByPage: AnnotationProps[][] = [];
    const pageInHeight = canvasRef.current?.offsetHeight || 0;
    const pageInWidth = canvasRef.current?.offsetWidth || 0;
    if (!canvasRef.current) return annotationsByPage;
    params.forEach((item) => {
      if (item.frameCoords) {
        item.frameCoords.forEach((coord) => {
          const xMin = pageInWidth * coord.upLeftScale[0];
          const xMax = pageInWidth * coord.lowRightScale[0];
          const yMin = pageInHeight * coord.upLeftScale[1];
          const yMax = pageInHeight * coord.lowRightScale[1];

          const annotation: AnnotationProps = {
            id: `annotation_${item.paragraphId}`,
            text: item.paragraph,
            x: [xMin, xMax],
            y: [yMin, yMax],
            page: coord.page,
          };

          annotationsByPage[coord.page - 1] ||= [];
          annotationsByPage[coord.page - 1].push(annotation);
        });
      }
    });

    window.console.log(annotationsByPage);
    return annotationsByPage;
  };

  const mouseMove = (e: React.MouseEvent, index: number) => {
    if (!pdfData || !coordData[index]) return;
    const pageDom = pageRefs.current[index]?.current;
    if (isTextSelected) {
      e.preventDefault();
      return;
    }
    if (!pageDom) return;
    const { left, top } = pageDom.getBoundingClientRect();
    const relativeX = e.clientX - left;
    const relativeY = e.clientY - top;
    const annotationDoms = Array.from(
      document.getElementsByClassName("chat-paper__annotation"),
    ) as HTMLElement[];
    annotationDoms.forEach((eleItem) => {
      if (eleItem.id !== hoverItem?.id) {
        eleItem.style.background = "none";
      }
    });
    for (const item of coordData[index]) {
      const isWithinBounds =
        relativeX >= item.x[0] &&
        relativeX <= item.x[1] &&
        relativeY >= item.y[0] &&
        relativeY <= item.y[1];
      if (isWithinBounds) {
        const hoverItems = document.querySelectorAll<HTMLElement>(
          `#${item.id}`,
        );
        hoverItems.forEach((eleItem) => {
          eleItem.style.background = "rgba(187, 214, 251, 0.19)";
        });
        if (btnRef.current) {
          btnRef.current.style.display = "block";
          btnRef.current.style.left = `${item.x[1] - 130 * scale}px`;
          if (canvasRef.current) {
            btnRef.current.style.top = `${canvasRef.current.clientHeight * index - scrollTop + Number(item.y[1])}px`;
          }
        }
        setHoverItem(item);
        return;
      }
    }
  };

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = event.currentTarget.scrollTop;
    const indexPage = Math.floor(scrollTop / pageHeight);
    setIndexPage(indexPage);
    setScrollTop(scrollTop);
    if (hoverItem && scrollTop && btnRef.current && canvasRef.current) {
      const { clientHeight } = canvasRef.current;
      const { page, y } = hoverItem;
      const topValue = clientHeight * (page - 1) - scrollTop + y[1] + 5;
      btnRef.current.style.top = `${topValue}px`;
    }
    if (textSelected) {
      const selection = window.getSelection();
      if (selection && selection.toString() !== "") {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        const x = rect.left;
        const y = rect.bottom;
        const rectWidth = rect.width;
        if (!btnRef.current) return;
        const withinBounds = x > 0 && x < width + 40;
        if (withinBounds) {
          btnRef.current.style.left = `${x + rectWidth / 2 - 80 * scale}px`;
          btnRef.current.style.top = `${y - 30 * scale}px`;
          setTextSelected({ ...textSelected, y: y - 30 * scale });
        } else {
          setIsTextSelected(false);
          setTextSelected(null);
        }
      }
    }
  };

  const submit = (type: string) => {
    const paragraphStr = textSelected ? textSelected.text : hoverItem?.text;
    if (paragraphStr && getParagraph) getParagraph(paragraphStr, type);
  };

  const close = () => {
    if (btnRef.current) {
      btnRef.current.style.display = "none";
    }
    const element: any = Array.from(
      document.querySelectorAll(`#${hoverItem?.id}`),
    );
    if (element) {
      element.forEach((item: any) => {
        item.style.background = "none";
      });
    }
  };

  // PDF 加载成功后执行的操作
  const pageLoad = useCallback(
    (page: any) => {
      window.console.log(page.height);
      if (canvasRef.current) {
        setPageHeight(canvasRef.current.offsetHeight);
      }
      if (pdfData) {
        const formatNewData: AnnotationProps[][] = formatPdfData(pdfData) ?? [];
        setCoordData(formatNewData);
      }
    },
    [pageHeight],
  );

  useEffect(() => {
    const handleSelection = debounce((event) => {
      event.preventDefault();
      event.stopPropagation();
      const selection = window.getSelection();
      if (!selection || selection.toString() === "") {
        setIsTextSelected(false);
        setTextSelected(null);
        if (!hoverItem && btnRef.current) {
          btnRef.current.style.display = "none";
        }
        return;
      }
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      const x = rect.left;
      const y = rect.bottom;
      if (!btnRef.current) return;
      const offsetLeft = left || 40;
      const offsetTop = top || 40;
      const isInner = divRef.current?.contains(range.startContainer);
      if (isInner) {
        btnRef.current.style.left = `${x - offsetLeft}px`;
        btnRef.current.style.top = `${y - offsetTop}px`;
        btnRef.current.style.display = "block";
        setIsTextSelected(true);
        setTextSelected({ text: selection.toString(), y });
      } else {
        setIsTextSelected(false);
        setTextSelected(null);
      }
    }, 100);
    document.addEventListener("selectionchange", handleSelection);
    return () =>
      document.removeEventListener("selectionchange", handleSelection);
  }, []);

  // const handlePageClick = (type: string) => {
  //   if (!virtuosoRef.current) return;
  //   if (type === "prev") {
  //     if (indexPage === 0) return;
  //     virtuosoRef.current.scrollToIndex({
  //       index: indexPage - 1,
  //       behavior: "smooth",
  //     });
  //   } else {
  //     if (indexPage + 1 === numPages) return;
  //     virtuosoRef.current.scrollToIndex({
  //       index: indexPage + 1,
  //       behavior: "smooth",
  //     });
  //   }
  //   close();
  // };

  const handleScaleClick = (type: string) => {
    if (type === "prev") {
      if (scale <= 0.1) return;
      setScale(scale - 0.1);
    } else {
      if (scale >= 1.5) return;
      setScale(scale + 0.1);
    }

    // 缩放后回到顶部
    // reset();
    virtuosoRef.current.getState((value: any) => {
      setScrollTop(value.scrollTop);
    });

    close();
  };

  // const reset = () => {
  //   if (virtuosoRef.current) {
  //     virtuosoRef.current.scrollToIndex({
  //       index: 0,
  //       behavior: "auto",
  //       align: "start",
  //     });
  //     setTimeout(() => {
  //       virtuosoRef.current.scrollToIndex({
  //         index: indexPage,
  //         behavior: "smooth",
  //       });
  //     }, 1000);
  //   }
  // };

  // useEffect(() => {
  //   window.addEventListener("resize", () => reset());
  //   return () => {
  //     window.removeEventListener("resize", () => reset());
  //   };
  // }, []);

  const toPercentageString = (value: number) => `${(value * 100).toFixed(0)}%`;
  return (
    <div style={{ position: "relative" }} ref={divRef}>
      <Document file={file} onLoadSuccess={onDocumentLoadSuccess} loading="">
        <Virtuoso
          style={{
            height: `${height ? height : 782}px`,
            width: width + "px",
          }}
          totalCount={numPages}
          onScroll={handleScroll}
          ref={virtuosoRef}
          itemContent={(index) => (
            <PageDiv
              id={"page_" + index}
              key={index}
              ref={pageRefs.current[index]}
              sx={{ width: width * scale - 10 }}
              onMouseMove={(e) => mouseMove(e, index)}
              PageHeight={pageHeight}
            >
              {pdfData &&
                coordData.length !== 0 &&
                coordData[index] &&
                coordData[index].map((item, i) => (
                  <AnnotationDiv
                    id={item.id}
                    className="chat-paper__annotation"
                    key={item.id + i}
                    StyleGroup={item}
                  ></AnnotationDiv>
                ))}
              <Page
                width={width}
                canvasRef={canvasRef}
                pageNumber={index + 1}
                onLoadSuccess={(page) => pageLoad(page)}
                renderAnnotationLayer={false}
                scale={scale}
                loading=""
              />
            </PageDiv>
          )}
        />
        {operation && (
          <PdfButtonGroup id="chat-paper__tipBox" ref={btnRef}>
            <Buttongroup>
              {operation.map((item) => (
                <ButtonItem key={item.type}>
                  <OperationButton onClick={() => submit(item.type)}>
                    {item.label}
                  </OperationButton>
                  <ButtonLine />
                </ButtonItem>
              ))}
              <CloseButton onClick={close}>
                <CloseIcon style={{ fontSize: "16px" }} />
              </CloseButton>
            </Buttongroup>
          </PdfButtonGroup>
        )}
        <PageOperationBox>
          <OperationNav>
            {/* <StyleBotton onClick={() => handlePageClick("prev")}>
              <KeyboardArrowUpIcon sx={{ fontSize: 14 }} />
            </StyleBotton> */}
            <StyleLabel>{indexPage + 1 + "/" + numPages}</StyleLabel>
            {/* <StyleBotton onClick={() => handlePageClick("next")}>
              <KeyboardArrowDownIcon sx={{ fontSize: 14 }} />
            </StyleBotton> */}
          </OperationNav>
          <OperationNav>
            <StyleBotton onClick={() => handleScaleClick("next")}>
              <KeyboardArrowUpIcon sx={{ fontSize: 14 }} />
            </StyleBotton>
            <StyleLabel>{toPercentageString(scale)}</StyleLabel>
            <StyleBotton onClick={() => handleScaleClick("prev")}>
              <KeyboardArrowDownIcon sx={{ fontSize: 14 }} />
            </StyleBotton>
          </OperationNav>
        </PageOperationBox>
      </Document>
    </div>
  );
};

export default PdfView;
