import { anyValueProps } from "@/types/common";
import AddPaperBaseDialog from "@/views/PaperBase/components/PersonalPaper/components/PersonalPaperDialog/AddPaperBaseDialog";
import {
  Checkbox,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Popover,
  TextField,
} from "@mui/material";
import GroupIcon from "@mui/icons-material/Group";
import { useQuery } from "@tanstack/react-query";
import { getChatShare, getUserQueryByMenu } from "@/api/chat";
import SearchIcon from "@mui/icons-material/Search";
interface AnchorOriginProps {
  vertical: "top" | "bottom";
  horizontal: "left" | "right";
}

interface PaperPopoverIProps {
  anchorEl: HTMLButtonElement | null;
  id?: "simple-popover" | undefined;
  open: boolean;
  onClose: () => void;
  anchorOrigin?: AnchorOriginProps;
  chatId?: string;
}

const Root = styled("div")(() => ({
  width: 350,
  maxHeight: 500,
}));

const PopoverHeader = styled("div")(() => ({
  width: "100%",
  height: 40,
  background: "#f5f5f5",
  fontSize: 20,
  fontWeight: 600,
  lineHeight: "40px",
  paddingLeft: "10px",
}));

const PopoverContent = styled("div")(() => ({
  width: "100%",
  // maxHeight: "calc(100% - 120px)",
  height: 300,
  overflow: "auto",
  background: "#fff",
  padding: "0 10px",
  boxSizing: "border-box",
}));

const EmptyTypography = styled(Typography)(() => ({
  width: "100%",
  textAlign: "center",
  margin: "20px 0",
}));

const PopoverFooter = styled("div")(() => ({
  width: "100%",
  // height: 90,
  background: "#f5f5f5",
  padding: "5px 10px",
  boxSizing: "border-box",
}));

const ButtonGroup = styled("div")(() => ({
  display: "flex",
  justifyContent: "flex-end",
  marginTop: 5,
}));

const StyledList = styled(List)(() => ({
  height: "100%",
  // overflow: "auto",
  padding: 0,
}));

const StyledListItemButton = styled(ListItemButton)(() => ({
  paddingTop: 2,
  paddingBottom: 2,
  paddingLeft: 0,
}));

const StyledListItemIcon = styled(ListItemIcon)(() => ({
  minWidth: "30px",
}));

const StyledCheckbox = styled(Checkbox)(({ theme }) => ({
  padding: 0,
  margin: `${theme.spacing()} ${theme.spacing()} ${theme.spacing()} 0`,
}));

const ListItemTextInfo = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
}));

const StyledTextField = styled(TextField)(() => ({
  height: "100%",
  "& .MuiInputBase-input": {
    paddingTop: 0,
    paddingBottom: 0,
    height: "100%",
  },
  "& .MuiInputBase-root": {
    height: "100%",
    borderRadius: 28,
  },
}));

const SearchDiv = styled("div")(() => ({
  display: "flex",
  justifyContent: "center",
  height: "40px",
  margin: "5px 0",
}));

const GroupPopover: React.FC<PaperPopoverIProps> = (props) => {
  const {
    id,
    open,
    onClose,
    anchorEl,
    anchorOrigin = { vertical: "bottom", horizontal: "left" },
    chatId,
  } = props;
  const [list, setList] = useState<any[]>([]);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [value, setValue] = useState<string>("");

  const [info, setInfo] = useState<{ page: number; size: number }>({
    page: 1,
    size: 10,
  });
  const scrollRef = useRef<HTMLDivElement>(null);
  const queryRequest = async () => {
    const response = await getUserQueryByMenu({
      ...info,
      menuId: 5,
      name: value,
    });
    return response.data;
  };

  const { data, status, refetch, error } = useQuery({
    queryKey: ["getUserQueryByMenu", info, value],
    queryFn: queryRequest,
  });

  const handleScroll = (total: number) => {
    if (scrollRef.current) {
      const scrollTop = scrollRef.current.scrollTop;
      const scrollHeight = scrollRef.current.scrollHeight;
      const clientHeight = scrollRef.current.clientHeight;
      if (scrollTop + clientHeight >= scrollHeight - 3 && total) {
        const { page, size } = info;
        if (size <= total) {
          setInfo({ page, size: size + 10 });
        }
      }
    }
  };

  useEffect(() => {
    switch (status) {
      case "success": {
        setList(data.result);
        break;
      }
      case "error":
        message.error("获取用户列表失败" + error.message);
        break;
      default:
        break;
    }
    scrollRef.current?.addEventListener("scroll", () =>
      handleScroll(data?.total ?? 0),
    );
    return () => {
      scrollRef.current?.removeEventListener("scroll", () =>
        handleScroll(data?.total ?? 0),
      );
    };
  }, [data, status, open, scrollRef.current]);

  const saveLiterature = async () => {
    try {
      const userList = list.filter((item) => item.exist).map((item) => item.id);
      if (userList.length) {
        const chatParams = {
          chatId,
          userIds: userList,
        };
        const { data } = await getChatShare(chatParams);
        if (data.code === 200) {
          message.success("复制成功");
          handleClose();
        }
      } else {
        message.error("请选择要复制的用户");
      }
    } catch (error) {
      message.error("复制对话记录失败" + `${(error as Error)?.message}`);
    }
  };

  const handleSubmit = () => {
    saveLiterature();
  };

  const handleCheck = (item: anyValueProps, index: number) => {
    const itemTemp = { ...item, exist: item.exist ? !item.exist : true };
    const collectListTemp = [...list];
    collectListTemp[index] = itemTemp;
    setList(collectListTemp);
  };

  const handleClose = () => {
    setList([]);
    onClose();
  };

  const searchChang = (value: string) => {
    setInfo({ page: 1, size: 10 });
    setValue(value);
  };

  return (
    <Popover
      id={id}
      open={open}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={anchorOrigin}
      sx={{ ml: "4px" }}
    >
      <Root>
        <PopoverHeader>对话复制到其他用户</PopoverHeader>
        <SearchDiv>
          <StyledTextField
            placeholder="请输入关键词"
            autoComplete="off"
            slotProps={{
              input: {
                startAdornment: <SearchIcon />,
              },
            }}
            onChange={(e) => searchChang(e.target.value)}
          />
        </SearchDiv>
        <PopoverContent ref={scrollRef}>
          {list.length > 0 && (
            <StyledList>
              {list.map((item, index) => (
                <ListItem key={item.id} disablePadding>
                  <StyledListItemButton
                    onClick={() => handleCheck(item, index)}
                  >
                    <StyledListItemIcon>
                      <StyledCheckbox
                        edge="start"
                        checked={item.exist ?? false}
                      />
                    </StyledListItemIcon>
                    <ListItemText>
                      <ListItemTextInfo>
                        <GroupIcon
                          fontSize="small"
                          sx={{
                            mr: 1,
                            color: item.exist ? "rgba(0, 104, 177)" : "gray",
                          }}
                        />
                        {item.groupName ?? item.name}
                      </ListItemTextInfo>
                    </ListItemText>
                  </StyledListItemButton>
                </ListItem>
              ))}
            </StyledList>
          )}
          {list.length === 0 && (
            <EmptyTypography>暂无其他用户信息</EmptyTypography>
          )}
        </PopoverContent>
        <PopoverFooter>
          <ButtonGroup>
            <Button
              variant="outlined"
              sx={{ mr: 1, height: 33 }}
              onClick={handleClose}
            >
              取消
            </Button>
            <Button
              variant="contained"
              sx={{ height: 33 }}
              onClick={handleSubmit}
            >
              确定
            </Button>
          </ButtonGroup>
        </PopoverFooter>
        <AddPaperBaseDialog
          open={dialogOpen}
          setOpen={setDialogOpen}
          reload={refetch}
          type="add"
        />
      </Root>
    </Popover>
  );
};
export default GroupPopover;
