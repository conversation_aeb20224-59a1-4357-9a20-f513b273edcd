import React, { useEffect, useState } from "react";
import TextField, { TextFieldProps } from "@mui/material/TextField";
import {
  FormControl,
  InputAdornment,
  TextareaAutosize,
  styled,
} from "@mui/material";
import FormLabel from "../Label";
import ErrorHint from "../ErrorHint";
const StyledFormControl = styled(FormControl)(() => ({
  width: "100%",
}));

const StyledInput = styled("div", {
  shouldForwardProp: (p) => p !== "type",
})<{ type?: string }>(({ type }) => ({
  display: "flex",
  flexDirection: "row",
  alignItems: type !== "textarea" ? "center" : "",
  justifyContent: "space-between",
  width: "100%",
}));

const InputMainBody = styled("div")<{ labelwidth?: number }>(
  ({ labelwidth }) => ({
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: `calc(100% - ${labelwidth || 0}px)`,
  }),
);

const StyledTextField = styled(TextField, {
  shouldForwardProp: (p) => p !== "rightBorderRadius",
})<{ rightBorderRadius?: boolean }>(({ rightBorderRadius, theme }) => ({
  "& fieldset": {
    borderWidth: "1px !important",
  },
  "&>div": {
    borderRadius: rightBorderRadius ? "4px" : "4px 0 0 4px",
    height: 30,
  },
  "&>div:hover": {
    borderColor: `${theme.palette.primary.main} !important`,
  },
}));

const Textarea = styled(TextareaAutosize)(
  ({ theme }) => `
  box-sizing: border-box;
  width: 100%;
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  padding: 8px 12px;
  border-radius: 8px;
  background: ${theme.palette.mode === "dark" ? "#ccc" : "#fff"};
  border: 1px solid #ccc;
  &:hover {
      border-color: ${theme.palette.primary.main};
    }
    &:focus {
      border-color: ${theme.palette.primary.main};

    }
    // firefox
    &:focus-visible {
      outline: 0;
    }
`,
);
type InputItemProps = Omit<any, "type" | "required"> & {
  error?: any;
  types?: string;
  placeholder?: string;
  size?: "small" | "medium";
  rightBorderRadius?: boolean;
  labelWidth?: number;
  onChangeValue: (key: string, value: any) => void;
  variant?: "standard" | "outlined" | "filled";
  required?: boolean;
  initValue?: string | number;
  value?: string | number;
  isMaxLength?: boolean;
  maxLength?: number;
};

const InputItem: React.FC<InputItemProps & TextFieldProps> = ({
  error = "",
  label,
  keyword,
  size = "small",
  placeholder,
  labelWidth,
  rightBorderRadius = true,
  readonly = false,
  onChangeValue,
  variant,
  required,
  type,
  initValue,
  value,
  isMaxLength = false,
  maxLength,
}) => {
  const [myValue, setMyValue] = useState<string | number>("");
  const [errorCurrent, setErrorCurrent] = useState(false);
  const onValueChange = (value: string, keyword: string) => {
    const versionPattern = /^v\d+\.\d+(\.\d+)?$/i;
    if (!value) {
      setMyValue("");
      onChangeValue(keyword, "");
      return;
    }
    if (keyword === "kb_version" && !versionPattern.test(value)) {
      setErrorCurrent(true);
    } else {
      setErrorCurrent(false);
    }
    if (
      (maxLength && value.length <= maxLength) ||
      (keyword === "kb_description" && maxLength && value.length <= maxLength)
    ) {
      setMyValue(value);
      onChangeValue(keyword, value);
    }
  };
  useEffect(() => {
    if (initValue !== undefined) {
      setMyValue(initValue);
      onChangeValue(keyword, initValue);
      return;
    }
  }, [initValue]);
  return (
    <StyledFormControl error={Boolean(error)}>
      <StyledInput type={type}>
        {label && (
          <FormLabel
            label={label}
            labelwidth={labelWidth}
            required={required}
          />
        )}
        <InputMainBody labelwidth={labelWidth}>
          {type === "input" && (
            <StyledTextField
              disabled={readonly}
              fullWidth
              error={Boolean(error || errorCurrent)}
              defaultValue={value ?? myValue}
              rightBorderRadius={rightBorderRadius}
              size={size}
              autoComplete={"off"}
              placeholder={placeholder || "请输入"}
              variant={variant || "outlined"}
              onChange={(e) => onValueChange(e.target.value, keyword)}
              slotProps={{
                input: {
                  endAdornment: isMaxLength && (
                    <InputAdornment
                      position="end"
                      style={{ color: "rgba(0, 0, 0, 0.45)" }}
                    >
                      {typeof myValue === "string" && myValue.length}/
                      {maxLength}
                    </InputAdornment>
                  ),
                },
              }}
            />
          )}
          {type === "textarea" && (
            <div style={{ width: "100%" }}>
              <Textarea
                minRows={2}
                maxRows={3}
                defaultValue={value ?? myValue}
                maxLength={maxLength}
                placeholder={placeholder || "请输入"}
                onChange={(e) => onValueChange(e.target.value, keyword)}
              />
              <div
                style={{
                  color: "rgba(0, 0, 0, 0.45)",
                  display: "flex",
                  justifyContent: "flex-end",
                }}
              >
                {typeof myValue === "string" && myValue.length}/{maxLength}
              </div>
            </div>
          )}
        </InputMainBody>
      </StyledInput>
      {(required && Boolean(error)) || errorCurrent ? (
        <ErrorHint
          error={errorCurrent ? "格式不正确,例如:v1.0或v1.0.0" : error}
          labelWidth={labelWidth}
        />
      ) : (
        <ErrorHint />
      )}
    </StyledFormControl>
  );
};

export default InputItem;
