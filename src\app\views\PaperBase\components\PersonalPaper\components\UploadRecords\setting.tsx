import { ColumnProps } from "@/components/CustomTable";
import { FormColumnProps } from "@/components/DynamicForm";
import { Divider, Tooltip, TooltipProps, tooltipClasses } from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import React from "react";
import { queryGroupList } from "@/api/knowledgeBase";

const STATUS_LABEL: any = {
  "-1": "等待中",
  "0": "解析中",
  "1": "解析完成",
  "2": "已清空",
};
interface SelectItemProps {
  label: string;
  value: string;
  roleName?: string;
}
export const recordsSearchColumns = (isAdmin: boolean): FormColumnProps[] => [
  {
    name: "status",
    label: "任务状态",
    componentType: "select",
    grid: isAdmin ? 4 : 6,
    options: [
      { label: "全部", value: "3" },
      { label: "已清空", value: "2" },
      { label: "解析中", value: "0" },
      { label: "等待中", value: "-1" },
      { label: "解析完成", value: "1" },
    ],
  },
  {
    name: "fuzzyQuery",
    label: "模糊查询",
    componentType: "input",
    grid: isAdmin ? 4 : 6,
    required: false,
    placeholder: "请输入关键词",
  },
  ...(isAdmin
    ? [
        {
          name: "resourceCode",
          label: "课题组",
          componentType: "select" as const,
          grid: 4,
          options: defaultSelectOptions,
          componentProps: {
            loadRequest: queryGroupList,
            labelKey: "groupName",
            valueKey: "resourceCode",
            initSearch: { isAll: true },
          },
        },
      ]
    : []),
];

const defaultSelectOptions: SelectItemProps[] = [{ label: "全部", value: "1" }];

const splitErrorMsg = (msg: string) =>
  msg.split("|||").filter((item) => item !== "");

const PdfNumLabel = styled("span")(() => ({
  display: "flex",
  width: "100%",
  justifyContent: "center",
  alignItems: "center",
}));

const ErrorIcon = styled(ErrorOutlineIcon)(() => ({
  fontSize: "20px",
  color: "red",
  marginLeft: "5px",
}));

const HtmlTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#f5f5f9",
    color: "rgba(0, 0, 0, 0.87)",
    maxWidth: 500,
    fontSize: theme.typography.pxToRem(12),
    border: "1px solid #dadde9",
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: "#dadde9",
  },
}));

const TooltipTitleMain = styled("div")(() => ({
  minWidth: 350,
  maxWidth: 500,
  maxHeight: 100,
  overflow: "auto",
  paddingRight: 6,
}));

const TitleTypography = styled(Typography)(() => ({
  width: "100%",
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
}));

const TooltipTitleMore = styled("div")(() => ({
  width: "100%",
  margin: "5px 0",
  display: "flex",
  fontSize: 16,
  justifyContent: "space-between",
  paddingRight: 15,
  boxSizing: "border-box",
}));

const TooltipTitle = (props: { title: string }) => {
  const { title } = props;
  const errorList = splitErrorMsg(title);
  return (
    <React.Fragment>
      <TooltipTitleMain>
        {errorList.map((el, index) => (
          <TitleTypography color="inherit" key={index} title={el}>
            {el}
          </TitleTypography>
        ))}
      </TooltipTitleMain>
      <Divider style={{ marginTop: "10px" }} />
      <TooltipTitleMore>
        <span>非PDF或重复PDF(共{errorList.length}个)</span>
      </TooltipTitleMore>
    </React.Fragment>
  );
};

const StatusDiv = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const MyBadge = styled("span")<{ status: number }>(({ status }) => ({
  display: "inline-block",
  width: 10,
  height: 10,
  marginRight: 5,
  borderRadius: "50%",
  marginLeft: 8,
  backgroundColor:
    status === 0
      ? "rgba(255, 195, 0, 1)"
      : status === 1
        ? "rgba(67, 207, 124, 1)"
        : "grey",
}));

const PaperBox = styled("span")(() => ({
  width: "100%",
}));

export const columns = (isAdmin: boolean): ColumnProps[] => [
  {
    dataKey: "name",
    label: "任务名称",
    width: 150,
    ellipsis: true,
    disablePadding: true,
    align: "center",
  },
  {
    dataKey: "pdfNum",
    label: "PDF数量",
    width: 150,
    ellipsis: true,
    disablePadding: true,
    align: "center",
    render: (row) => (
      <PdfNumLabel>
        {row.pdfNum}
        {row.errorFiles ? (
          <HtmlTooltip
            placement="top"
            arrow
            title={<TooltipTitle title={row.errorFiles} />}
          >
            <ErrorIcon />
          </HtmlTooltip>
        ) : (
          ""
        )}
      </PdfNumLabel>
    ),
  },
  {
    dataKey: "paperBase",
    label: "资料库",
    width: 100,
    align: "center",
    render: (row) => {
      const documentName = row.documentName || "";
      const documentVersion = row.documentVersion || "";
      const fullName = `${documentName} ${documentVersion}`.trim();
      return <PaperBox>{fullName || "暂无数据"}</PaperBox>;
    },
  },
  ...(isAdmin
    ? [
        {
          dataKey: "resourceName" as const,
          label: "课题组",
          width: 100,
          align: "center" as const,
        },
      ]
    : []),
  {
    dataKey: "createTime",
    label: "任务创建时间",
    width: 200,
    sortable: true,
    align: "center",
  },
  {
    dataKey: "status",
    label: "任务状态",
    width: 120,
    align: "center",
    render: (row) => (
      <StatusDiv>
        <MyBadge status={row.status} />
        {STATUS_LABEL[row.status]}
      </StatusDiv>
    ),
  },
];
