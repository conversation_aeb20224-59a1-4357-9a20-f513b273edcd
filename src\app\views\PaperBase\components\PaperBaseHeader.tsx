import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { paperFormColumns } from "./PersonalPaper/setting";
import { SearchParamProp } from "./PersonalPaper";
import { useNavigate } from "react-router-dom";
import UploadDialog from "./PersonalPaper/components/PersonalPaperDialog/UploadDialog";
import dayjs from "dayjs";
import SortPart from "@/views/KnowledgeBase/components/SortPart";
import { anyValueProps } from "@/types/common";
import React from "react";
import { withPermission } from "@/components/HocButton";
import { useAppSelector } from "@/hooks";
import { checkPermission } from "@/utils/auth";

interface Props {
  searchParam: SearchParamProp;
  setSearchParam: (param: SearchParamProp) => void;
  setAddOpen: (open: boolean) => void;
  setAddType: (type: string) => void;
  refetch: () => void;
  type: string;
}

const HEIGHT = 40;

const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
  alignItems: "center",
}));

const FormBox = styled("div")(() => ({
  height: "100%",
  display: "flex",
  alignItems: "center",
  borderRadius: 16,
  background: "#fff",
  flex: 1,
  minWidth: "1200px",
}));

const ButtonGroup = styled("div")(() => ({
  width: "100%",
  height: HEIGHT,
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
}));

const StyleButton = styled(Button, {
  shouldForwardProp: (prop) => prop !== "MyType",
})<{ MyType?: string; width?: number }>(({ MyType, width }) => ({
  width: width || 60,
  height: 32,
  background:
    MyType == "gary"
      ? "rgba(242, 242, 242, 1)"
      : "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
  color: MyType == "gary" ? "#000" : "#fff",
  borderRadius: 28,
  cursor: "pointer",
}));

const ButtonBox = styled("div")(() => ({
  height: "100%",
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
  borderRadius: 16,
  background: "#fff",
  boxSizing: "border-box",
  marginLeft: 20,
  padding: "0 13px 0 19px",
}));

const UploadButton = ({ action }: { action: any }) => {
  const StyleUploadButton: React.FC<any> = ({ action }) => (
    <StyleButton onClick={action}>上传</StyleButton>
  );

  const PermissionButton = withPermission(StyleUploadButton, "EDIT");
  return <PermissionButton action={action} />;
};

const AddButton = ({ action }: { action: any }) => {
  const StyleAddButton: React.FC<any> = ({ action }) => (
    <StyleButton width={100} onClick={action} sx={{ marginLeft: "8px" }}>
      添加资料库
    </StyleButton>
  );

  const PermissionButton = withPermission(StyleAddButton, "EDIT");
  return <PermissionButton action={action} />;
};

const PaperBaseHeader: React.FC<Props> = ({
  searchParam,
  setSearchParam,
  setAddOpen,
  setAddType,
  refetch,
  type,
}) => {
  const formRef = useRef<RefProps>();
  const sortRef = useRef<any>();
  const navigator = useNavigate();
  const [uploadDialogOpen, setUploadDialogOpen] = useState<boolean>(false);
  const taskTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
  const taskName = `任务${taskTime}`;
  const { roleOption } = useAppSelector((state) => state.user);
  const selectColumns = useMemo(() => paperFormColumns(roleOption), []);
  const { buttonPermMenus } = useAppSelector((state) => state.route);

  const handleChange = (value: anyValueProps) => {
    const { name, order } = value;
    const newSearchParam = { ...searchParam, sort: name, asc: order };
    setSearchParam(newSearchParam);
  };

  const handelReset = () => {
    sortRef.current?.reset();
    setSearchParam({
      ...(checkPermission(roleOption) ? { resourceCode: "1" } : {}),
    });
  };

  const handleUpoad = () => {
    setUploadDialogOpen(true);
  };

  const handleSubmit = () => {
    formRef.current?.submit().then((res) => {
      const newSearchParam = { ...searchParam, ...(res || {}) };
      setSearchParam(newSearchParam);
    });
  };

  const handleAddPaperBase = () => {
    setAddOpen(true);
    setAddType("add");
  };

  return (
    <Root>
      <FormBox>
        <Box sx={{ width: "70%", height: HEIGHT, mr: 4 }}>
          <DynamicForm
            ref={formRef}
            columns={selectColumns}
            formData={searchParam}
            // onChange={onChange}
            size="small"
            labelWidth={80}
          />
        </Box>
        <Box sx={{ width: "30%", height: HEIGHT, mr: "31px", display: "flex" }}>
          <SortPart
            label="更新时间"
            name="updateTime"
            setSortInfo={handleChange}
            ref={sortRef}
          />
          <ButtonGroup>
            <StyleButton MyType="gary" sx={{ mr: 1.25 }} onClick={handelReset}>
              重置
            </StyleButton>
            <StyleButton onClick={handleSubmit}>查询</StyleButton>
          </ButtonGroup>
        </Box>
      </FormBox>
      {buttonPermMenus.includes("EDIT") && (
        <ButtonBox>
          <StyleButton
            MyType="gary"
            width={80}
            sx={{ mr: 1.25 }}
            onClick={() => navigator(`/paper-base/${type}/upload-records`)}
          >
            上传记录
          </StyleButton>
          <UploadButton action={() => handleUpoad()} />
          <AddButton action={() => handleAddPaperBase()} />
          {/* <StyleButton onClick={() => setUploadDialogOpen(true)}>
          上传
        </StyleButton> */}
        </ButtonBox>
      )}

      {uploadDialogOpen && (
        <UploadDialog
          open={uploadDialogOpen}
          setOpen={setUploadDialogOpen}
          reload={refetch}
          rowOption={{ name: taskName }}
        />
      )}
    </Root>
  );
};
export default PaperBaseHeader;
