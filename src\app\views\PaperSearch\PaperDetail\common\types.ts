// 目录项数据类型定义
export interface TableOfContentsItem {
  title: string;
  page: string;
  subTitle?: TableOfContentsItem[] | null;
}

// 目录组件属性类型
export interface TableOfContentsProps {
  data: TableOfContentsItem[];
  onItemClick?: (item: TableOfContentsItem) => void;
  maxHeight?: number | string;
}

// 目录项组件属性类型
export interface TableOfContentsItemProps {
  item: TableOfContentsItem;
  level: number;
  onItemClick?: (item: TableOfContentsItem) => void;
  activeItem?: string;
}
