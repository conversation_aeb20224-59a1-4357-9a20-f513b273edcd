import { RootState, useAppSelector } from "@/hooks";
import TextareaSearch from "../TextareaSearch";
import SettingModal from "../SettingModal";
import { Popover } from "@mui/material";
import { getChatPrompt, getNewChatId, resetSession } from "@/api/chat";
import { anyValueProps } from "@/types/common";
import { leftHistoryListProps } from "../common";
import SetUpUrl from "@/assets/set-up.svg";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
const Footer = styled("div")(({ theme }) => ({
  position: "relative",
  display: "flex",
  borderColor: theme.palette.primary.main,
  marginTop: "10px",
}));

const TextFieldButton = styled("div")(() => ({
  display: "flex",
  justifyContent: "center",
  marginRight: "15px",
}));

const SelectBtn = styled(But<PERSON>, {
  shouldForwardProp: (p) => p !== "active",
})<{
  active?: boolean;
}>(({ active }) => ({
  color: !active ? "rgba(0, 0, 0, 1)" : "#fff !important",
  borderRadius: "12px",
  height: "45px",
  border: "2px solid rgba(255, 255, 255, 1)",
  background: !active
    ? "radial-gradient(77.59% 200% at 24.568965517241377% -109.09090909090908%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)"
    : "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
  ":disabled": {
    cursor: " not-allowed",
    pointerEvents: "auto",
  },
  whiteSpace: "nowrap", // 禁止换行
  overflow: "hidden", // 隐藏溢出内容
  textOverflow: "ellipsis", // 显示省略号
  display: "inline-block", // 确保宽度生效
}));

const TaskDiv = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  marginTop: "15px",
  flexWrap: "wrap",
}));

const TaskPromptItem = styled("div", {
  shouldForwardProp: (p) => p !== "activeIndex" && p !== "index",
})<{
  activeIndex: number | null;
  index: number;
}>(({ activeIndex, index, theme }) => ({
  position: "relative",
  padding: "0 10px 0 15px",
  fontSize: "12px",
  color: "#8a9191",
  lineHeight: "24px",
  cursor: "pointer",
  borderRadius: activeIndex === index ? "20px" : "",
  backgroundColor: activeIndex === index ? theme.palette.primary.main : "",
  ":before": {
    content: '" "',
    display: "block",
    width: "5px",
    height: "5px",
    backgroundColor: "#8a9191",
    borderRadius: "50%",
    position: "absolute",
    top: "10px",
    left: "5px",
  },
  ":hover": {
    borderRadius: "20px",
    backgroundColor: theme.palette.primary.main,
  },
}));

const TaskBtn = styled("div", {
  shouldForwardProp: (p) => p !== "disabled",
})<{ disabled: boolean }>(({ theme, disabled }) => ({
  marginLeft: 10,
  fontSize: "12px",
  color: "#8a9191",
  border: "1px solid #ccc",
  padding: "2px 10px",
  borderRadius: "10px",
  cursor: disabled ? "not-allowed" : "pointer",

  ":hover": {
    backgroundColor: theme.palette.primary.main,
    color: "#fff",
  },
}));

const SetUpBtDiv = styled("div")(() => ({
  position: "relative",
  marginLeft: "15px",
}));

const SetUpBtn = styled(Button)(() => ({
  width: "45px",
  height: "45px",
  borderRadius: "12px",
  background: "rgba(252, 252, 252, 1)",
  border: "2px solid rgba(255, 255, 255, 1)",
  position: "relative",
  ":disabled": {
    cursor: " not-allowed",
    pointerEvents: "auto",
    background: "#ccc",
  },
}));

const SettingModalDiv = styled(Popover)(() => ({
  marginTop: "-10px",
  "& .css-1dfcyzw-MuiPaper-root-MuiPopover-paper": {
    borderRadius: "20px",
  },
}));

const FooterBottomBtn = styled("div")(() => ({
  width: "32px",
  height: "32px",
  background: "#fff",
  position: "absolute",
  top: "-64px",
  right: "96px",
  border: "1px solid #e8eaf2",
  borderRadius: "50%",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  cursor: "pointer",
}));

interface Props {
  setOpen: (value: boolean) => void;
  setInputValue: (value: string) => void;
  onConversation: (value: string, chatDetailId?: number) => void;
  setTaskPromptValue: (value: string) => void;
  setPromptType: (value: undefined | string) => void;
  getChatHistory: (value?: boolean) => void;
  taskPromptValue: string;
  promptType: undefined | string;
  inputValue: string;
  loading: boolean;
  textValue: string;
  historyData: leftHistoryListProps[];
  isScrolling: boolean;
  scrollToBottom: () => void;
  setExpandedIndex: (value: number | null) => void;
  setPaperData: (value: anyValueProps[]) => void;
}
const Index: React.FC<Props> = ({
  setOpen,
  setPromptType,
  setInputValue,
  onConversation,
  setTaskPromptValue,
  getChatHistory,
  setPaperData,
  setExpandedIndex,
  loading,
  textValue,
  inputValue,
  historyData,
  promptType,
  taskPromptValue,
  isScrolling,
  scrollToBottom,
}) => {
  const { selectedBank, active } = useAppSelector(
    (state: RootState) => state.counter,
  );
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [pdfPrompt, setPdfPrompt] = useState<anyValueProps[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLButtonElement>(null);
  useEffect(() => {
    getChatPrompts();
  }, []);

  useEffect(() => {
    setActiveIndex(null);
  }, [active]);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
  };

  const handleSubmit = async () => {
    const newValue = taskPromptValue && taskPromptValue + inputValue;
    const {
      data: { data },
    } = await getNewChatId();
    if (!loading) {
      onConversation(newValue, data);
    }
  };

  const handleEnterKey = (event: any) => {
    if (loading || !inputValue || inputValue.trim() === "") return;
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSubmit();
    } else {
      if (event.key === "Enter" && event.ctrlKey) {
        event.preventDefault();
        handleSubmit();
      }
    }
  };

  const handleTask = (taskPrompt: string, index: number) => {
    if (activeIndex === index) {
      setActiveIndex(null);
      setTaskPromptValue("");
    } else {
      setActiveIndex(index);
      setTaskPromptValue(taskPrompt);
    }
  };

  const getChatPrompts = async () => {
    try {
      const {
        data: { data },
      } = await getChatPrompt();
      setPdfPrompt(data);
    } catch (error) {
      message.error(`获取Prompt失败${(error as Error)?.message}`);
    }
  };

  const handleExport = async () => {
    const chatDetailId = historyData[historyData.length - 1].id;
    const note = historyData[historyData.length - 1].note;
    if (active && note !== "reset") {
      const {
        data: { code },
      } = await resetSession({ active, chatDetailId });
      if (code === 200) {
        getChatHistory(true);
      }
    } else {
      message.warning("当前已重置");
    }
    return;
  };

  // const startRecording = async () => {
  //   recorderRef.current = Recorder({
  //     type: "wav",
  //     sampleRate: 16000,
  //     bitRate: 16,
  //   });

  //   if (recorderRef.current) {
  //     try {
  //       recorderRef.current.open(
  //         () => {
  //           window.console.log("recorder open");
  //           recorderRef.current.start();
  //           message.success("请开始描述你的问题");
  //           setIsRecording(true);
  //         },
  //         (msg: string, isUserNotAllow: any) => {
  //           message.error((isUserNotAllow ? "UserNotAllow，" : "") + msg);
  //         },
  //       );
  //     } catch (error) {
  //       message.error("无法录音:" + error);
  //     }
  //   }
  // };
  // const stopRecording = () => {
  //   if (!recorderRef.current) return;
  //   /* eslint-disable @typescript-eslint/no-unused-vars */
  //   recorderRef.current.stop(async (blob: any, __: any) => {
  //     const file = new File([blob], "recording.wav", {
  //       type: "audio/wav",
  //       lastModified: Date.now(),
  //     });
  //     const formData = new FormData();
  //     formData.append("file", file);
  //     try {
  //       const {
  //         data: { data },
  //       } = await speechRecognition(formData);
  //       setInputValue(data);
  //     } catch (error) {
  //       message.error(`语音识别失败：${error}`);
  //     }
  //     recorderRef.current.close();
  //   });
  //   setIsRecording(false);
  // };
  // const handleVoice = () => {
  //   if (isRecording) {
  //     stopRecording();
  //   } else {
  //     startRecording();
  //   }
  // };

  useEffect(() => {
    if (typeof promptType !== "undefined") {
      const len = pdfPrompt.findIndex((item) => item.taskName === promptType);
      const newInputValue = pdfPrompt[len].taskPrompt + textValue;
      if (!loading) {
        onConversation(newInputValue);
        setPromptType(undefined);
      }
    }
  }, [promptType]);
  return (
    <Footer>
      {isScrolling && (
        <FooterBottomBtn onClick={() => scrollToBottom()}>
          <KeyboardArrowDownIcon sx={{ fontSize: 30 }}></KeyboardArrowDownIcon>
        </FooterBottomBtn>
      )}

      {selectedBank.bankType !== 2 && (
        <TextFieldButton>
          <SelectBtn
            onClick={handleClickOpen}
            sx={{ width: "150px" }}
            title={selectedBank.name && selectedBank.name}
            active={
              selectedBank.bankType === 1 || selectedBank.bankType === 3
                ? true
                : false
            }
            disabled={
              selectedBank.bankType === 1 ||
              (selectedBank.bankType === 3 && true)
            }
          >
            {selectedBank.name ? selectedBank.name : "无资料库"}
          </SelectBtn>
        </TextFieldButton>
      )}

      <div style={{ flex: "1" }}>
        <TextareaSearch
          value={inputValue}
          onChange={handleInputChange}
          onPressEnter={handleEnterKey}
          onSubmit={handleSubmit}
          disabled={loading || !inputValue || inputValue.trim() === ""}
          placeholder="请输入搜索内容"
          maxLength={1000}
        />

        <TaskDiv>
          {selectedBank.bankType === 2
            ? pdfPrompt.map((item, index) => (
                <TaskPromptItem
                  key={index}
                  onClick={() => handleTask(item.taskPrompt, index)}
                  activeIndex={activeIndex}
                  index={index}
                >
                  {item.taskName}
                </TaskPromptItem>
              ))
            : ""}

          {active && historyData.length ? (
            <TaskBtn
              onClick={loading ? () => {} : handleExport}
              disabled={loading}
            >
              重置聊天
            </TaskBtn>
          ) : (
            ""
          )}
        </TaskDiv>
      </div>
      <SetUpBtDiv>
        <SetUpBtn
          onClick={(event) => setAnchorEl(event.currentTarget)}
          disabled={loading}
        >
          <img
            src={SetUpUrl}
            alt=""
            style={{
              transform: "scale(0.7)",
              filter: loading ? "brightness(0) invert(1)" : "none",
            }}
          />
        </SetUpBtn>

        <SettingModalDiv
          id={anchorEl ? "simple-popovers" : undefined}
          open={Boolean(anchorEl)}
          onClose={() => setAnchorEl(null)}
          anchorEl={anchorEl}
          anchorOrigin={{
            vertical: "top", // 锚点在按钮的顶部
            horizontal: "right", // 水平居中
          }}
          transformOrigin={{
            vertical: "bottom", // Popover 从底部变换
            horizontal: "right", // 水平居中
          }}
        >
          <SettingModal
            getChatHistory={getChatHistory}
            setAnchorEl={setAnchorEl}
            isDataLength={historyData.length > 0}
            setExpandedIndex={setExpandedIndex}
            setPaperData={setPaperData}
          />
        </SettingModalDiv>
      </SetUpBtDiv>
      {/* <SetUpBtn style={{ marginLeft: "15px" }} onClick={handleVoice}>
        <img src={!isRecording ? voiceStartSvg : voicePauseSvg} alt="" />
      </SetUpBtn> */}
    </Footer>
  );
};
export default Index;
