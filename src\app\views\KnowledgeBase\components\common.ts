import { FormColumnProps } from "@/components/DynamicForm";
import { PERMISSION_MENU } from "@/utils/permission";

export interface knowledgeOptionProps {
  label: string;
  value: number;
}

export interface buttonGroupProps {
  name: string;
  keyword: string;
  operation?: () => void;
  role: string;
}
export interface knowledgeDataProps {
  kb_name: string;
  kb_version: string;
  kb_description: null | string;
  id: number;
}

const fromColumns = [
  {
    keyword: "kb_name",
    label: "名称",
    type: "input",
    required: true,
    isMaxLength: true,
    maxLength: 15,
    placeholder: "请输入名称",
  },
  {
    keyword: "kb_version",
    label: "版本",
    type: "input",
    required: true,
    isMaxLength: true,
    maxLength: 15,
    placeholder: "请输入版本",
  },
  {
    keyword: "kb_description",
    label: "描述",
    type: "textarea",
    required: false,
    isMaxLength: true,
    maxLength: 100,
    placeholder: "请输入描述信息",
  },
];

const searchColumns: FormColumnProps[] = [
  {
    name: "range_value",
    label: "更新时间",
    componentType: "date",
    grid: 6,
    required: false,
  },
  {
    name: "kb_name",
    label: "模糊查询",
    componentType: "input",
    grid: 6,
    required: false,
    placeholder: "请输入关键词",
  },
];

const buttonGroup: buttonGroupProps[] = [
  {
    name: "全选",
    keyword: "selectAll",
    role: "NORMAL",
  },
  {
    name: "反向选择",
    keyword: "reverseSelectAll",
    role: "NORMAL",
  },
  {
    name: "取消选择",
    keyword: "clearSelectAll",
    role: "NORMAL",
  },
  {
    name: "删除所选",
    keyword: "deleteSelectAll",
    role: PERMISSION_MENU["edit"],
  },
  {
    name: "下载所选",
    keyword: "downloadSelectAll",
    role: PERMISSION_MENU["edit"],
  },
];

const databaseButtonGroup = [
  {
    name: "我的实验数据库",
    keyword: "myDataBase",
  },
  {
    name: "部门实验数据库",
    keyword: "departmentDataBase",
  },
  {
    name: "公司实验数据库",
    keyword: "companyDataBase",
  },
];

export { fromColumns, searchColumns, buttonGroup, databaseButtonGroup };
