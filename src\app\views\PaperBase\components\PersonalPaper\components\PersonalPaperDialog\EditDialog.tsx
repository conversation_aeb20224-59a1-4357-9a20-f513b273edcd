import { editPdfInfo } from "@/api/personalpaper";
import CustomDialog from "@/components/Dialog";
import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { anyValueProps } from "@/types/common";
import { EditColumns } from "./setting";
import { cloneDeep } from "lodash";

interface Props {
  open: boolean;
  setOpen: (value: boolean) => void;
  reload: () => void;
  rowOption: any;
}

const ContentBox = styled("div")(() => ({
  height: "100%",
  padding: "15px 20px",
}));

const EditDialog: React.FC<Props> = ({ open, setOpen, rowOption, reload }) => {
  const formRef = useRef<RefProps>(null);
  const formatColumns = useMemo(() => EditColumns(rowOption.language), []);
  // const validateEmpty = (res: anyValueProps) => {
  //   const allValuesEmpty = Object.values(res).every((value) => value === "");
  //   if (allValuesEmpty) {
  //     throw new Error("值不可全为空");
  //   }
  //   return res;
  // };

  const updateWithRowOptions = (
    target: anyValueProps,
    rowOptions: anyValueProps,
  ) => {
    Object.keys(target).forEach(() => {
      // if (!target[key] && rowOptions[key]) {
      //   target[key] = rowOptions[key];
      // }
      target["id"] = rowOptions["id"];
    });
    return target;
  };

  const handleOk = () => {
    formRef.current?.submit().then(async (res: any) => {
      try {
        const newData = updateWithRowOptions(cloneDeep(res), rowOption);

        if (newData.language === "zh") {
          if (newData.title && newData.zhTitle) {
            // 两个都有值时，拼接
            newData.title = `${newData.zhTitle}|||${newData.title}`;
          } else if (newData.zhTitle) {
            // 只有zhTitle有值时，赋值给title
            newData.title = newData.zhTitle;
          } else if (newData.title && !newData.zhTitle) {
            // zhTitle为空但title有值时，拼接空+|||+title
            newData.title = `|||${newData.title}`;
          }
          // 删除zhTitle字段
          if (newData.zhTitle !== undefined) {
            delete newData.zhTitle;
          }

          // 处理authors和zhAuthors
          if (newData.authors && newData.zhAuthors) {
            // 两个都有值时，拼接
            const cleanZhAuthors = newData.zhAuthors.replace(/^,+|,+$/g, "");
            const cleanAuthors = newData.authors.replace(/^,+|,+$/g, "");
            newData.authors = `${cleanZhAuthors},|||,${cleanAuthors}`;
          } else if (newData.zhAuthors) {
            // 只有zhAuthors有值时，赋值给authors（去除首尾逗号）
            newData.authors = newData.zhAuthors.replace(/^,+|,+$/g, "");
          } else if (newData.authors && !newData.zhAuthors) {
            // zhAuthors为空但authors有值时，拼接空+,|||,+authors
            const cleanAuthors = newData.authors.replace(/^,+|,+$/g, "");
            newData.authors = `,|||,${cleanAuthors}`;
          }
          // 删除zhAuthors字段
          if (newData.zhAuthors !== undefined) {
            delete newData.zhAuthors;
          }
        }

        const { data } = await editPdfInfo(newData);
        if (data.code === 200) {
          message.success("修改成功");
          reload();
          setOpen(false);
        }
      } catch (error: any) {
        message.error(error.message);
      }
    });
  };

  return (
    <CustomDialog
      open={open}
      setDialogOpen={setOpen}
      title="编辑资料"
      okButtonProps={{ onOk: handleOk }}
      cancelButtonProps={{ onCancel: () => setOpen(false) }}
      width={700}
    >
      <ContentBox slot="content">
        <DynamicForm
          ref={formRef}
          columns={formatColumns}
          size="small"
          formData={rowOption}
          rowSpacing={0}
        />
      </ContentBox>
    </CustomDialog>
  );
};
export default EditDialog;
