import React, { useState } from "react";
import { IconButton, InputBase, Paper, styled } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import SearchIcon from "@mui/icons-material/Search";

const StyledIconButton = styled(IconButton)({
  width: 30,
  height: 30,
  marginLeft: 10,
});

interface SearchInputProps {
  searchValue: string;
  onSearch: (value: string) => void;
}
const SearchInput: React.FC<SearchInputProps> = ({ onSearch, searchValue }) => {
  const [value, setValue] = useState<string>(searchValue);

  const handleSearch = () => {
    onSearch(value);
  };

  return (
    <Paper
      component="div"
      sx={{
        p: "6px 12px",
        display: "flex",
        alignItems: "center",
        width: "100%",
        height: "100%",
        borderRadius: "20px",
        boxSizing: "border-box",
      }}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          handleSearch();
        }
      }}
    >
      <InputBase
        value={value}
        sx={{ ml: 1, flex: 1, height: "100%" }}
        placeholder="请输入检索内容"
        inputProps={{ "aria-label": "search content" }}
        onChange={(e) => setValue(e.target.value)}
      />

      {value && value.length > 0 && (
        <StyledIconButton
          color="primary"
          aria-label="clear"
          onClick={() => setValue("")}
        >
          <CloseIcon />
        </StyledIconButton>
      )}

      <StyledIconButton
        type="button"
        aria-label="search"
        onClick={handleSearch}
      >
        <SearchIcon />
      </StyledIconButton>
    </Paper>
  );
};

export default SearchInput;
