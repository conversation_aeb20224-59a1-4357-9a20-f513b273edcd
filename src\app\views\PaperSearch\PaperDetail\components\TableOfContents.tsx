import React from "react";
import { styled } from "@mui/material/styles";
import {
  Box,
  Typography,
  ListItem,
  ListItemText,
  ListItemButton,
} from "@mui/material";
import {
  TableOfContentsProps,
  TableOfContentsItemProps,
} from "../common/types";

// 样式组件
const StyledContainer = styled(Box)(({ theme }) => ({
  backgroundColor: "#fff",
  borderRadius: "8px",
  padding: "16px",
  fontFamily: theme.typography.fontFamily,
  overflow: "visible",
  width: "100%",
  height: "auto",
  boxSizing: "border-box",
}));

const StyledListItemButton = styled(ListItemButton)<{ level: number }>(
  ({ level }) => ({
    paddingLeft: `${16 + level * 16}px`,
    paddingRight: "8px",
    paddingTop: "8px",
    paddingBottom: "8px",
    minHeight: "32px",
    height: "auto",
    borderRadius: "4px",
    margin: "0",
    overflow: "visible",
    cursor: "default",
    display: "flex",
    alignItems: "center",
    width: "100%",
    boxSizing: "border-box",
    backgroundColor: "transparent",
    position: "relative",
    zIndex: 1,
    "&:hover": {
      backgroundColor: "transparent",
    },
    "&:focus": {
      backgroundColor: "transparent",
    },
    "&:active": {
      backgroundColor: "transparent",
    },
    "&.Mui-selected": {
      backgroundColor: "transparent",
    },
    "&.Mui-focusVisible": {
      backgroundColor: "transparent",
    },
    "&.MuiListItemButton-root": {
      backgroundColor: "transparent",
    },
  }),
);

const StyledListItemText = styled(ListItemText)<{ level: number }>(
  ({ level }) => ({
    margin: 0,
    flex: 1,
    minWidth: 0,
    overflow: "visible",
    height: "auto",
    "& .MuiTypography-root": {
      fontSize: level === 0 ? "13px" : level === 1 ? "12px" : "11px",
      fontWeight: level === 0 ? 500 : 400,
      color: level === 0 ? "#333" : level === 1 ? "#555" : "#666",
      lineHeight: 1.6,
      wordBreak: "break-word",
      whiteSpace: "normal",
      overflowWrap: "break-word",
      hyphens: "auto",
      height: "auto",
      minHeight: "auto",
      display: "block",
    },
  }),
);

const PageNumber = styled(Typography)<{ level: number }>(({ level }) => ({
  fontSize: level === 0 ? "12px" : "11px",
  color: "#999",
  fontWeight: 400,
  minWidth: "50px",
  textAlign: "right",
  marginLeft: "8px",
  flexShrink: 0,
  whiteSpace: "nowrap",
  alignSelf: "flex-start",
  marginTop: "1px",
}));

const StyledListItem = styled(ListItem)(() => ({
  padding: 0,
  margin: "0 0 4px 0",
  backgroundColor: "transparent",
  overflow: "visible",
  width: "100%",
  height: "auto",
  minHeight: "auto",
  display: "block",
  position: "relative",
  "&:hover": {
    backgroundColor: "transparent",
  },
  "&:focus": {
    backgroundColor: "transparent",
  },
  "&.Mui-selected": {
    backgroundColor: "transparent",
  },
}));

// 单个目录项组件
const TableOfContentsItemComponent: React.FC<TableOfContentsItemProps> = ({
  item,
  level,
}) => {
  const hasSubItems = item.subTitle && item.subTitle.length > 0;

  return (
    <>
      <div
        style={{
          width: "100%",
          marginBottom: "4px",
          position: "relative",
          overflow: "visible",
        }}
      >
        <StyledListItem disablePadding>
          <StyledListItemButton level={level}>
            <StyledListItemText level={level} primary={item.title} />
            <PageNumber level={level}>第{item.page}页</PageNumber>
          </StyledListItemButton>
        </StyledListItem>
      </div>

      {hasSubItems && (
        <>
          {item.subTitle!.map((subItem, index) => (
            <TableOfContentsItemComponent
              key={`${subItem.title}-${index}`}
              item={subItem}
              level={level + 1}
            />
          ))}
        </>
      )}
    </>
  );
};

// 主目录组件 - 支持自适应高度和滚动
const TableOfContents: React.FC<TableOfContentsProps> = ({
  data,
  maxHeight = "none",
}) => {
  const hasMaxHeight = maxHeight !== "none";

  return (
    <StyledContainer>
      <Box
        sx={{
          maxHeight: hasMaxHeight ? maxHeight : "unset",
          height: "auto",
          width: "100%",
          overflowY: hasMaxHeight ? "auto" : "visible",
          overflowX: "visible",
          position: "relative",
          ...(hasMaxHeight && {
            "&::-webkit-scrollbar": {
              width: "6px",
            },
            "&::-webkit-scrollbar-track": {
              backgroundColor: "#f1f1f1",
              borderRadius: "3px",
            },
            "&::-webkit-scrollbar-thumb": {
              backgroundColor: "#c1c1c1",
              borderRadius: "3px",
              "&:hover": {
                backgroundColor: "#a8a8a8",
              },
            },
          }),
        }}
      >
        <div style={{ width: "100%", height: "auto", position: "relative" }}>
          {data.map((item, index) => (
            <TableOfContentsItemComponent
              key={`${item.title}-${index}`}
              item={item}
              level={0}
            />
          ))}
        </div>
      </Box>
    </StyledContainer>
  );
};

export default TableOfContents;
export type {
  TableOfContentsItem,
  TableOfContentsProps,
} from "../common/types";
