import { FormColumnProps } from "@/components/DynamicForm";
import * as yup from "yup";
export const Columns: FormColumnProps[] = [
  {
    name: "password",
    label: "原密码",
    componentType: "password",
    required: true,
    placeholder: "请输入原密码",
    grid: 12,
    validation: yup
      .string()
      .min(6, "原密码长度不可小于6位")
      .max(20, "原密码长度不可大于20位")
      .required("请输入原密码"),
  },
  {
    name: "newPassword",
    label: "新密码",
    componentType: "password",
    required: true,
    placeholder: "请输入新密码",
    grid: 12,
    validation: yup
      .string()
      .min(6, "新密码长度不可小于6位")
      .max(20, "新密码长度不可大于20位")
      .required("请输入新密码"),
  },
  {
    name: "confirmNewPassword",
    label: "确认新密码",
    componentType: "password",
    required: true,
    placeholder: "请再次输入新密码",
    grid: 12,
    validation: yup
      .string()
      .oneOf([yup.ref("newPassword")], "两次输入的新密码不一致")
      .required("请输入新密码"),
  },
];
