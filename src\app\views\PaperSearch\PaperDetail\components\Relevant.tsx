import { anyValueProps } from "@/types/common";
import { styled } from "@mui/material";
import React from "react";
import PaperPopover from "../../components/PaperPopover";
import { useAppSelector } from "@/hooks";
import AdminPaperPopover from "../../components/PaperPopover/AdminPaperPopover";
import { checkPermission } from "@/utils/auth";
import MuiAccordion, { AccordionProps } from "@mui/material/Accordion";
import MuiAccordionSummary, {
  AccordionSummaryProps,
} from "@mui/material/AccordionSummary";
import MuiAccordionDetails from "@mui/material/AccordionDetails";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import { PERMISSION_MENU } from "@/utils/permission";
import { withPermission } from "@/components/HocButton";
import { splitByTriplePipe } from "../common";

const Root = styled("div")(({ theme }) => ({
  width: 358,
  height: "calc(65% - 20px)",
  borderRadius: 20,
  background: "rgba(252, 252, 252, 1)",
  marginTop: 20,
  padding: theme.spacing(0, 2, 2, 2),
  boxSizing: "border-box",
  position: "relative",
}));

const Header = styled("div")(() => ({
  height: 56,
  fontSize: 18,
  fontWeight: 700,
  lineHeight: "18px",
  color: "rgba(31, 31, 31, 1)",
  display: "flex",
  alignItems: "center",
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
  boxSizing: "border-box",
}));
const ItemStyle = styled("div")(() => ({
  width: "100%",
  // height: 112,
  borderRadius: 14,
  background: "rgba(255, 255, 255, 1)",
  boxShadow: "0px 0px 10px  rgba(0, 0, 0, 0.1)",
  // padding: "5px 0",
  // borderBottom: "1px dashed #E5E5E5",
  marginTop: 20,
  padding: "17px 10px 5px 27px",
  boxSizing: "border-box",
}));
const PaperBox = styled("div")(() => ({
  // position: "relative",
  height: "calc(100% - 72px)",
  overflow: "auto",
}));
const NodataDiv = styled("div")(() => ({
  color: "#67676A",
  position: "absolute",
  left: "50%",
  top: "50%",
  transform: "translate(-50%,-50%)",
}));
const Title = styled("div")(() => ({
  height: 20,
  fontSize: 18,
  fontWeight: 700,
  lineHeight: "18px",
  color: "rgba(64, 64, 64, 1)",
  cursor: "pointer",
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  maxWidth: "100%",
}));
const AuthorMsg = styled("div")(() => ({
  height: 16,
  fontSize: 14,
  fontWeight: 400,
  lineHeight: "14px",
  color: "rgba(64, 64, 64, 1)",
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  maxWidth: "100%",
  marginTop: 13,
  marginBottom: 13,
}));

const ButtonDiv = styled("div")(() => ({
  fontSize: 14,
  fontWeight: 400,
  lineHeight: "14px",
  color: "rgba(0, 48, 122, 1)",
  marginTop: 17,
  marginBottom: 17,
  cursor: "pointer",
}));

const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(() => ({
  width: "100%",
  background: "none",
  "&:not(:last-child)": {
    borderBottom: 0,
  },
  "&::before": {
    display: "none",
  },
}));

const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary
    expandIcon={<PlayArrowIcon sx={{ fontSize: "0.9rem" }} />}
    {...props}
  />
))(({ theme }) => ({
  borderRadius: "5px",
  height: "40px",
  minHeight: "25px",
  flexDirection: "row-reverse",
  background: "none",
  fontFamily: "思源宋体 Medium",
  "& .MuiAccordionSummary-expandIconWrapper.Mui-expanded": {
    transform: "rotate(90deg)",
  },
  "& .MuiAccordionSummary-content": {
    marginLeft: theme.spacing(1),
  },
}));

const AddPaperBaseButton = ({ pdfItem }: anyValueProps) => {
  const StyleAiChatButton: React.FC<any> = ({ pdfItem }) => {
    const { roleOption } = useAppSelector((state) => state.user);
    const [pdfId, setPdfId] = useState<string>("");
    const [anchorEl, setAnchorEl] = useState<null | HTMLButtonElement>(null);
    const [adminanchorEl, setAdminAnchorEl] =
      useState<null | HTMLButtonElement>(null);
    const onClickPaperBase = (event: any) => {
      window.console.log(123, pdfItem);
      if (!pdfItem) return;
      const { id } = pdfItem;
      setPdfId(id);
      if (checkPermission(roleOption)) {
        setAdminAnchorEl(event.currentTarget);
      } else {
        setAnchorEl(event.currentTarget);
      }
    };

    return (
      <>
        <ButtonDiv onClick={(e: any) => onClickPaperBase(e)}>
          加入资料库
        </ButtonDiv>
        {Boolean(anchorEl) && (
          <PaperPopover
            id={anchorEl ? "simple-popover" : undefined}
            open={Boolean(anchorEl)}
            onClose={() => setAnchorEl(null)}
            anchorEl={anchorEl}
            pdfId={pdfId}
            anchorOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
          />
        )}
        {Boolean(adminanchorEl) && (
          <AdminPaperPopover
            id={adminanchorEl ? "simple-popover" : undefined}
            open={Boolean(adminanchorEl)}
            onClose={() => setAdminAnchorEl(null)}
            anchorEl={adminanchorEl}
            pdfId={pdfId}
            anchorOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
          />
        )}
      </>
    );
  };
  const PermissionButton = withPermission(
    StyleAiChatButton,
    PERMISSION_MENU["edit"],
  );
  return <PermissionButton pdfItem={pdfItem} />;
};

const AccordionDetails = styled(MuiAccordionDetails)(() => ({
  padding: "0 8px",
  marginTop: "3px",
  borderRadius: "8px",
  boxSizing: "border-box",
}));

const Relevant: React.FC<{ data: anyValueProps }> = ({ data }) => {
  const [expanded, setExpanded] = useState<string | false>("");

  const init = () => {
    const params = Object.entries(data).filter(([, value]) => value.length > 0);
    if (params.length) {
      setExpanded(params[0][0]);
    }
  };

  const checkEmpty = (data: anyValueProps) =>
    data.every((item: any) => item[1].length === 0);

  useEffect(() => {
    init();
  }, [data]);

  const convertStringToArrayAndBack = (str: string) => {
    try {
      if (str === "" || !str) return "";
      const arr = JSON.parse(str);
      const authorsSplit = splitByTriplePipe(arr);
      if (!Array.isArray(authorsSplit)) {
        return authorsSplit;
      }
      if (authorsSplit.length !== 2) {
        return Array.isArray(authorsSplit[0])
          ? authorsSplit[0].join(", ")
          : authorsSplit.join(", ");
      } else {
        const author1 = Array.isArray(authorsSplit[0])
          ? authorsSplit[0].join(",")
          : authorsSplit[0];
        const author2 = Array.isArray(authorsSplit[1])
          ? authorsSplit[1].join(",")
          : authorsSplit[1];
        return `${author1}   ${author2}`;
      }
    } catch (error) {
      console.error("Error parsing JSON string:", error);
      return str;
    }
  };

  const handleClick = (id: string) => {
    window.open(
      window.APP_CONFIG.BASE_PATH +
        "/#/paper-search/paper-details/paperbase?id=" +
        id,
    );
  };

  const accordionHandleChange =
    (key: string) => (_: React.SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? key : false);
    };
  return (
    <Root>
      <Header>作者相关资料</Header>
      <PaperBox>
        {Object.keys(data).length && !checkEmpty(Object.entries(data)) ? (
          <>
            {Object.entries(data).map(
              ([key, value]) =>
                value.length !== 0 && (
                  <Accordion
                    key={key}
                    expanded={expanded === key}
                    onChange={accordionHandleChange(key)}
                  >
                    <AccordionSummary>
                      {key} <span>({value.length}篇)</span>
                    </AccordionSummary>
                    <AccordionDetails>
                      {value.map((item: any) => (
                        <ItemStyle key={item.id}>
                          <Title
                            title={
                              item.title
                                ? item.title
                                    .replace(/\|\|\|/g, "    ")
                                    .replace(/#/g, "")
                                : ""
                            }
                            onClick={() => handleClick(item.id)}
                          >
                            {item.title
                              ? item.title
                                  .replace(/\|\|\|/g, "    ")
                                  .replace(/#/g, "")
                              : "暂无标题"}
                          </Title>
                          <AuthorMsg
                            title={convertStringToArrayAndBack(item.authors)}
                          >
                            作者：
                            {item.authors
                              ? convertStringToArrayAndBack(item.authors)
                              : "暂无"}
                          </AuthorMsg>
                          {/* <ButtonDiv onClick={(e) => handleAdd(item.id, e)}>
                            加入资料库
                          </ButtonDiv> */}
                          <AddPaperBaseButton pdfItem={item} />
                        </ItemStyle>
                      ))}
                    </AccordionDetails>
                  </Accordion>
                ),
            )}
          </>
        ) : (
          <NodataDiv>暂无数据</NodataDiv>
        )}
      </PaperBox>
    </Root>
  );
};

export default Relevant;
