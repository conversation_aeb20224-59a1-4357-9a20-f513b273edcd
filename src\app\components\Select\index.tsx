import React, { useEffect, useState } from "react";
import {
  MenuItem,
  Select,
  SelectProps,
  CircularProgress,
  InputAdornment,
} from "@mui/material";
import lodash from "lodash";
import { anyValueProps } from "@/types/common";

const ITEM_HEIGHT = 40;
const ITEM_PADDING_TOP = 8;

export type SelectItemProps = {
  label: string;
  value: string | number;
};

type MySelectProps = SelectProps & {
  value: string | number | string[];
  options: SelectItemProps[];
  loadRequest?: (queryParams: any) => Promise<any>;
  initSearch?: anyValueProps;
  // searchKey?: string;
  labelKey?: string;
  valueKey?: string;
  multiple?: boolean;
  anchorOrigin?: "center" | "bottom" | "top" | number;
  transformOrigin?: "center" | "bottom" | "top" | number;
};

const MySelect: React.FC<MySelectProps> = (props) => {
  const {
    options,
    loadRequest,
    initSearch, // 初始搜索条件
    // searchKey = "name",
    labelKey = "label",
    valueKey = "value",
    multiple = false,
    transformOrigin = "top",
    anchorOrigin = "bottom",
    onChange,
    ...otherProps
  } = props;
  let value = props.value;
  if (multiple && !Array.isArray(value)) {
    // 后端返回null时，默认为空数组
    value = [];
  }
  const initPage = () => ({ page: 1, size: 50 });

  const [hasMoreItems, setHasMoreItems] = useState(false);
  const [optionList, setOptionList] = useState<SelectItemProps[]>(
    options || [],
  );
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState(initPage);
  const preSearch = useRef(initSearch); // 追踪上一个 initSearch 的值

  const fetchData = async () => {
    if (!loadRequest) return;
    try {
      setLoading(true);
      const response = await loadRequest({
        ...pagination,
        ...initSearch,
      });
      const { result, total } = response.data;
      setHasMoreItems(Math.ceil(total / pagination.size) > pagination.page);
      const newOptions =
        result?.map((el: any) => ({
          label: el[labelKey],
          value: el[valueKey],
          item: el,
        })) || [];
      // 去重
      setOptionList((prev) => {
        const existingValues = new Set(prev.map((item) => item.value));
        const filteredNewOptions = newOptions.filter(
          (newOption: anyValueProps) => !existingValues.has(newOption.value),
        );
        return [...prev, ...filteredNewOptions];
      });
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [loadRequest, pagination]);

  // 监听 initSearch 的变化，防止重复请求
  useEffect(() => {
    if (!lodash.isEqual(preSearch.current, initSearch)) {
      setOptionList(options); // 恢复默认options
      setPagination(initPage); // 重置分页
      preSearch.current = initSearch; // 更新引用
    }
  }, [initSearch]); // 监听 initSearch 变化

  const handleScroll = (event: any) => {
    const bottom =
      event.target.scrollHeight ===
      event.target.scrollTop + event.target.clientHeight;
    if (bottom && !loading && hasMoreItems) {
      // 滚动到底部并且不是正在加载
      setPagination({ page: pagination.page + 1, size: pagination.size });
    }
  };

  const MenuProps = {
    PaperProps: {
      onScroll: handleScroll, // 监听菜单滚动
      style: {
        maxHeight: ITEM_HEIGHT * 6 + ITEM_PADDING_TOP,
      },
    },
    anchorOrigin: {
      vertical: anchorOrigin, // 相对于触发元素的垂直位置
      horizontal: "center" as const, // 相对于触发元素的水平位置
    },
    transformOrigin: {
      vertical: transformOrigin, // 下拉菜单自身的垂直起点
      horizontal: "center" as const, // 下拉菜单自身的水平起点
    },
  };

  const handleSelect = (e: any) => {
    onChange && onChange(e, e.target.value);
  };

  return (
    <Select
      {...otherProps}
      value={value}
      multiple={multiple}
      onChange={handleSelect}
      MenuProps={MenuProps}
      displayEmpty
      sx={{
        borderRadius: "20px",
        border: "1px solid rgba(235, 235, 235, 1)",
      }}
      endAdornment={
        loading && (
          <InputAdornment position="end">
            <CircularProgress size={24} />
          </InputAdornment>
        )
      }
    >
      {optionList.map((item) => (
        <MenuItem key={item.value} value={item.value}>
          {item.label}
        </MenuItem>
      ))}
    </Select>
  );
};

export default MySelect;
