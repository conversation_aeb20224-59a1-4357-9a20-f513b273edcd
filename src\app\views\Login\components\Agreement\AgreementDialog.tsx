import CustomDialog from "@/components/Dialog";
import mammoth from "mammoth";
interface Props {
  open: boolean;
  setOpen: (value: boolean) => void;
}

const DialogHeader = styled("div")(() => ({
  height: 60,
  textAlign: "center",
  lineHeight: "60px",
  boxSizing: "border-box",
}));

const AgreementDialog: React.FC<Props> = ({ open, setOpen }) => {
  const [htmlContent, setHtmlContent] = useState("");
  const handleLocalFile = () => {
    // 假设文件路径是相对路径或绝对路径
    const filePath = window.APP_CONFIG.BASE_PATH + "/agreement.docx"; // 这里填上本地文件路径

    fetch(filePath)
      .then((response) => response.arrayBuffer()) // 读取文件为 ArrayBuffer
      .then((arrayBuffer) => {
        mammoth
          .convertToHtml({ arrayBuffer })
          .then((result) => {
            window.console.log(result);
            setHtmlContent(result.value); // 设置转换后的 HTML 内容
          })
          .catch((err) => console.error(err));
      })
      .catch((err) => console.error("Error reading file:", err));
  };

  useEffect(() => {
    handleLocalFile();
  }, []);

  return (
    <CustomDialog
      open={open}
      setDialogOpen={setOpen}
      width={900}
      maskClosable={false}
    >
      <DialogHeader slot="header">用户隐私和服务条款</DialogHeader>
      <div slot="content" style={{ padding: "0 20px" }}>
        <span dangerouslySetInnerHTML={{ __html: htmlContent }} />
      </div>
    </CustomDialog>
  );
};
export default AgreementDialog;
