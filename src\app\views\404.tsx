import { Divider, Typography } from "@mui/material";
import React from "react";
import { styled } from "@mui/material/styles";

const Root = styled("div")(() => ({
  width: "100%",
  height: "calc(100vh - 64px - 40px)",
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  color: "rgba(0, 0, 0, 0.8)",
}));

const Main = styled("div")(() => ({
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
}));

const MyDivider = styled(Divider)(({ theme }) => ({
  marginLeft: theme.spacing(2),
  marginRight: theme.spacing(2),
}));

const NotFound: React.FC = () => (
  <Root>
    <Main>
      <Typography style={{ marginTop: "4px" }}>404</Typography>
      <MyDivider orientation="vertical" flexItem />
      <Typography>page not found</Typography>
    </Main>
  </Root>
);

export default NotFound;
