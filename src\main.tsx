import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import "./app/styles/icon/iconfont.css";
import "virtual:svg-icons-register";
import App from "./app/views";
import { Provider } from "react-redux";
import { store } from "./app/store";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient();

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  // <React.StrictMode>
  <QueryClientProvider client={queryClient}>
    <Provider store={store}>
      <App />
    </Provider>
  </QueryClientProvider>,
  // </React.StrictMode>
);
