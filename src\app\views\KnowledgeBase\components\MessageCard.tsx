import { FC } from "react";
import DeleteOutlineRoundedIcon from "@mui/icons-material/DeleteOutlineRounded";
import EditIcon from "@mui/icons-material/Edit";
// import ClearIcon from "@mui/icons-material/Clear";
import {
  Divider,
  Card,
  CardContent,
  SvgIcon,
  Tooltip,
  Checkbox,
} from "@mui/material";
import { CheckboxProps } from "@mui/material/Checkbox";
import { TooltipProps, tooltipClasses } from "@mui/material/Tooltip";
import Knowledge from "@/assets/knowledge.png";
import dayjs from "dayjs";
import { useNavigate } from "react-router-dom";
import { knowledgeDataProps, knowledgeOptionProps } from "./common";
import { useAppDispatch } from "@/hooks";
import { addChat, setKnowledge } from "@/store/counterSlice";
const Root = styled("div")(() => ({
  display: "grid",
  gridTemplateColumns: "repeat(auto-fill, minmax(320px, 1fr))",
  gap: "20px 30px",
  boxSizing: "border-box",
  height: "100%",
}));
const CardActionsStyle = styled("div")(() => ({
  display: "flex",
  justifyContent: "space-around",
  alignItems: "center",
  padding: "10px 0",
}));

const HeaderStyle = styled("div")(() => ({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
}));

const CardContentStyle = styled("div")(() => ({
  flex: 1,
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
}));

const ItemDiv = styled("div")(() => ({
  display: "flex",
  justifyContent: "space-between",
  margin: "5px 0",
}));

const RightContent = styled("div")(() => ({
  flex: 1,
  padding: "0 10px",
}));

const H4 = styled("h4")(() => ({
  margin: 0,
}));

const BootstrapTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.common.black,
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.black,
  },
}));

const BpIcon = styled("span")(({ theme }) => ({
  borderRadius: 3,
  width: 16,
  height: 16,
  boxShadow:
    "inset 0 0 0 1px rgba(16,22,26,.2), inset 0 -1px 0 rgba(16,22,26,.1)",
  backgroundColor: "#f5f8fa",
  backgroundImage:
    "linear-gradient(180deg,hsla(0,0%,100%,.8),hsla(0,0%,100%,0))",
  ".Mui-focusVisible &": {
    outline: "2px auto rgba(19,124,189,.6)",
    outlineOffset: 2,
  },
  "input:hover ~ &": {
    backgroundColor: "#ebf1f5",
    ...theme.applyStyles("dark", {
      backgroundColor: `${theme.palette.primary.main}`,
    }),
  },
  "input:disabled ~ &": {
    boxShadow: "none",
    background: "rgba(206,217,224,.5)",
    ...theme.applyStyles("dark", {
      background: "rgba(57,75,89,.5)",
    }),
  },
  ...theme.applyStyles("dark", {
    boxShadow: "0 0 0 1px rgb(16 22 26 / 40%)",
    backgroundColor: "#394b59",
    backgroundImage:
      "linear-gradient(180deg,hsla(0,0%,100%,.05),hsla(0,0%,100%,0))",
  }),
}));

const BpCheckedIcon = styled(BpIcon)(({ theme }) => ({
  backgroundColor: `${theme.palette.primary.main}`,
  backgroundImage:
    "linear-gradient(180deg,hsla(0,0%,100%,.1),hsla(0,0%,100%,0))",
  "&::before": {
    display: "block",
    width: 16,
    height: 16,
    backgroundImage:
      "url(\"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath" +
      " fill-rule='evenodd' clip-rule='evenodd' d='M12 5c-.28 0-.53.11-.71.29L7 9.59l-2.29-2.3a1.003 " +
      "1.003 0 00-1.42 1.42l3 3c.***********.71.29s.53-.11.71-.29l5-5A1.003 1.003 0 0012 5z' fill='%23fff'/%3E%3C/svg%3E\")",
    content: '""',
  },
  "input:hover ~ &": {
    backgroundColor: `${theme.palette.primary.main}`,
  },
}));

const BpCheckbox = (props: CheckboxProps) => (
  <Checkbox
    sx={{ "&:hover": { bgcolor: "transparent" } }}
    disableRipple
    color="default"
    checkedIcon={<BpCheckedIcon />}
    icon={<BpIcon />}
    inputProps={{ "aria-label": "Checkbox demo" }}
    {...props}
  />
);

interface Props {
  data: any[];
  onChange: (e: React.ChangeEvent<HTMLInputElement>, id: number) => void;
  setTypes: (value: string) => void;
  setItemValue: (value: knowledgeDataProps) => void;
  setKnowledgeOption: (value: knowledgeOptionProps[]) => void;
  setKnowledgeOpen: (value: boolean) => void;
  setAnchorEl: (value: HTMLButtonElement | null) => void;
  setDelIndex: (value: number) => void;
  handleTitle: (value: knowledgeDataProps) => void;
}

const ImgMediaCard: FC<Props> = ({
  data,
  onChange,
  setTypes,
  setItemValue,
  setKnowledgeOption,
  setKnowledgeOpen,
  setAnchorEl,
  setDelIndex,
  handleTitle,
}) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  // 删除知识库标识
  const deleteKnowledge = (id: number, event: any) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setDelIndex(id);
  };
  // 修改知识库
  const editKnowledge = (
    event: { stopPropagation: () => void },
    item: knowledgeDataProps,
  ) => {
    event.stopPropagation();
    const option = data.map((item: { id: number; kb_name: string }) => ({
      label: item.kb_name,
      value: item.id,
    }));
    window.console.log(option);
    setKnowledgeOption(option);
    setKnowledgeOpen(true);
    setItemValue(item);
    setTypes("edit");
  };
  // 发起会话
  const launchChat = (item: knowledgeDataProps) => {
    const uuid = Date.now();
    dispatch(addChat({ uuid, item }));
    dispatch(setKnowledge(item.id));
    navigate(`/ai-chat`);
  };
  return (
    <Root>
      {data.length
        ? data.map((item, index) => (
            <Card
              sx={{
                width: 320,
                height: 190,
                background: "#f7f7fb",
                borderRadius: "16px",
              }}
              key={index}
            >
              <CardContent>
                <HeaderStyle>
                  <BpCheckbox
                    onChange={(e) => onChange(e, item.id)}
                    checked={item.checked ?? false}
                  />
                  {/* <ClearIcon sx={{ fontSize: "18px", cursor: "pointer" }} /> */}
                </HeaderStyle>
                <CardContentStyle>
                  <img
                    src={Knowledge}
                    style={{ width: "60px", height: "60px" }}
                  ></img>
                  <RightContent>
                    <ItemDiv>
                      <H4
                        onClick={() => handleTitle(item)}
                        sx={{ cursor: "pointer" }}
                      >
                        {item.kb_name}
                      </H4>
                      <H4>{item.kb_version}</H4>
                    </ItemDiv>
                    <ItemDiv sx={{ fontSize: "14px" }}>
                      <span>{item.paper_count}篇资料</span>
                    </ItemDiv>
                    <ItemDiv sx={{ color: "#ccc", fontSize: "14px" }}>
                      <span>
                        更新时间:
                        {dayjs(item.update_dt).format("YYYY/MM/DD HH:mm")}
                      </span>
                    </ItemDiv>
                  </RightContent>
                </CardContentStyle>
              </CardContent>
              <Divider />
              <CardActionsStyle>
                <BootstrapTooltip title="编辑" placement="top">
                  <EditIcon
                    fontSize="small"
                    onClick={(e) => editKnowledge(e, item)}
                    sx={{ cursor: "pointer", ":hover": { color: "#4248B5" } }}
                  />
                </BootstrapTooltip>
                <Divider orientation="vertical" flexItem />
                <BootstrapTooltip title="删除" placement="top">
                  <DeleteOutlineRoundedIcon
                    fontSize="small"
                    onClick={(e) => deleteKnowledge(item.id, e)}
                    sx={{ cursor: "pointer", ":hover": { color: "#4248B5" } }}
                  />
                </BootstrapTooltip>
                <Divider orientation="vertical" flexItem />
                <BootstrapTooltip title="发起会话" placement="top">
                  <SvgIcon
                    fontSize={"inherit"}
                    sx={{
                      width: "20px",
                      height: "20px",
                      cursor: "pointer",
                      ":hover": { color: "#4248B5" },
                    }}
                    onClick={() => launchChat(item)}
                  >
                    <path
                      fill="currentColor"
                      d="M2 8.994A5.99 5.99 0 0 1 8 3h8c3.313 0 6 2.695 6 5.994V21H8c-3.313 0-6-2.695-6-5.994zM20 19V8.994A4.004 4.004 0 0 0 16 5H8a3.99 3.99 0 0 0-4 3.994v6.012A4.004 4.004 0 0 0 8 19zm-6-8h2v2h-2zm-6 0h2v2H8z"
                    ></path>
                  </SvgIcon>
                </BootstrapTooltip>
              </CardActionsStyle>
            </Card>
          ))
        : ""}
    </Root>
  );
};

export default ImgMediaCard;
