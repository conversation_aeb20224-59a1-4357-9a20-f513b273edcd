{"name": "ai-system", "version": "1.0.0", "type": "module", "private": true, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@microsoft/fetch-event-source": "^2.0.1", "@mui/icons-material": "^6.1.1", "@mui/lab": "6.0.0-beta.10", "@mui/material": "^6.1.3", "@mui/utils": "^6.1.3", "@mui/x-date-pickers": "^7.20.0", "@reduxjs/toolkit": "^2.2.7", "@tanstack/react-query": "^5.59.20", "@types/js-cookie": "^3.0.6", "@uppy/core": "^4.2.1", "@uppy/dashboard": "^4.1.0", "@uppy/drag-drop": "^4.0.2", "@uppy/file-input": "^4.0.1", "@uppy/locales": "^4.1.0", "@uppy/progress-bar": "^4.0.0", "@uppy/react": "^4.0.2", "axios": "^1.7.3", "base-64": "^1.0.0", "copy-to-clipboard": "^3.3.3", "date-fns": "^4.1.0", "dayjs": "^1.11.12", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "echarts-wordcloud": "^2.1.0", "formik": "^2.4.6", "highlight.js": "11.7.0", "js-cookie": "^3.0.5", "katex": "0.16.4", "lodash": "^4.17.21", "mammoth": "^1.9.0", "markdown-it": "13.0.1", "markdown-it-texmath": "1.0.0", "mui-daterange-picker-plus": "^1.0.8", "notistack": "^2.0.8", "pdfjs": "^2.5.3", "pdfjs-dist": "3.11.174", "react": "^18.3.1", "react-dom": "^18.3.1", "react-pdf": "^8.0.2", "react-redux": "7.2.8", "react-router-dom": "^6.26.0", "react-scripts": "5.0.1", "react-slick": "^0.30.3", "react-slider-verify": "^0.2.1", "react-viewer": "^3.2.2", "react-virtuoso": "^4.7.6", "recorder-core": "^1.3.24040900", "redux-persist": "6.0.0", "slick-carousel": "^1.8.1", "typescript": "^4.9.5", "uuid": "^10.0.0", "viewerjs": "^1.9.0", "viewerjs-react": "1.0.2", "vite-plugin-static-copy": "^3.0.0", "vite-plugin-svg-icons": "^2.0.1", "yup": "^1.4.0"}, "scripts": {"dev": "vite", "build": "vite build", "build:other": "vite build --mode other", "serve": "vite preview", "lint": "eslint --no-cache --fix \"./src/**/*.{js,jsx,ts,tsx}\"", "format": "prettier --write \"src/**/*.+(js|ts|jsx|tsx)\"", "prepare": "husky install", "commitlint": "commitlint --config commitlint.config.js -e -V"}, "devDependencies": {"@commitlint/cli": "^19.4.1", "@commitlint/config-conventional": "^19.4.1", "@eslint/js": "^9.9.1", "@types/base-64": "^1.0.2", "@types/katex": "0.16.7", "@types/lodash": "^4.17.7", "@types/markdown-it": "^14.1.2", "@types/node": "^18.19.44", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-redux": "^7.1.33", "@types/react-slick": "^0.23.13", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "^8.5.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.10.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.2", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "husky": "^8.0.0", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "typescript-eslint": "^8.4.0", "unplugin-auto-import": "^0.18.2", "vite": "^5.4.0", "vite-plugin-html": "^3.2.2"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint", "**/*.{js,jsx,tsx,ts,less,md,json}": ["eslint --fix", "prettier --write"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "prettier": {"endOfLine": "lf"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "main": "index.js", "repository": "http://************/qzsun/ai-system", "author": "ai-system", "license": "MIT"}