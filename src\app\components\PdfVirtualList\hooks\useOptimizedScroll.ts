import { useRef, useCallback, useEffect } from "react";
import { PageLayout, VisibleRange, ScrollState, PerformanceMetrics } from "../types/types";

interface UseOptimizedScrollProps {
  containerRef: React.RefObject<HTMLDivElement>;
  pageLayouts: PageLayout[];
  numPages: number;
  initialVisiblePages: number;
  onVisibleRangeChange: (range: VisibleRange) => void;
  onCurrentPageChange: (page: number) => void;
}

/**
 * 优化的滚动处理Hook
 * 使用requestAnimationFrame替代防抖，提供更流畅的滚动体验
 */
export const useOptimizedScroll = ({
  containerRef,
  pageLayouts,
  numPages,
  initialVisiblePages,
  onVisibleRangeChange,
  onCurrentPageChange,
}: UseOptimizedScrollProps) => {
  const rafId = useRef<number>();
  const scrollState = useRef<ScrollState>({
    scrollTop: 0,
    containerHeight: 0,
    isScrolling: false,
    lastScrollTime: 0,
  });
  
  const performanceMetrics = useRef<PerformanceMetrics>({
    scrollEventCount: 0,
    renderCount: 0,
    cacheHitRate: 0,
    averageRenderTime: 0,
  });

  const lastVisibleRange = useRef<VisibleRange>({ start: 0, end: 0 });
  const lastCurrentPage = useRef<number>(1);

  /**
   * 使用二分查找优化可见范围计算
   */
  const calculateVisibleRange = useCallback((
    scrollTop: number,
    containerHeight: number
  ): VisibleRange => {
    if (!pageLayouts.length) {
      return { start: 0, end: Math.min(initialVisiblePages - 1, numPages - 1) };
    }

    // 二分查找第一个可见页面
    let start = 0;
    let low = 0;
    let high = numPages - 1;

    while (low <= high) {
      const mid = Math.floor((low + high) / 2);
      const layout = pageLayouts[mid];
      
      if (!layout) break;
      
      if (layout.y + layout.height < scrollTop) {
        low = mid + 1;
      } else {
        high = mid - 1;
      }
    }
    
    start = Math.max(0, low - 1);

    // 计算结束页面
    const viewportBottom = scrollTop + containerHeight;
    let end = start;
    
    for (let i = start; i < numPages; i++) {
      const layout = pageLayouts[i];
      if (!layout || layout.y > viewportBottom) break;
      end = i;
    }

    // 确保至少显示指定数量的页面
    end = Math.max(end, Math.min(start + initialVisiblePages - 1, numPages - 1));

    return { start, end };
  }, [pageLayouts, numPages, initialVisiblePages]);

  /**
   * 计算当前页码
   */
  const calculateCurrentPage = useCallback((
    scrollTop: number,
    containerHeight: number
  ): number => {
    if (!pageLayouts.length) return 1;

    const middleScrollPosition = scrollTop + containerHeight / 2;
    
    // 使用二分查找找到中间位置对应的页面
    let low = 0;
    let high = numPages - 1;
    
    while (low <= high) {
      const mid = Math.floor((low + high) / 2);
      const layout = pageLayouts[mid];
      
      if (!layout) break;
      
      if (layout.y > middleScrollPosition) {
        high = mid - 1;
      } else if (layout.y + layout.height < middleScrollPosition) {
        low = mid + 1;
      } else {
        return mid + 1; // 页码从1开始
      }
    }
    
    return Math.max(1, Math.min(low, numPages));
  }, [pageLayouts, numPages]);

  /**
   * 优化的滚动处理函数
   */
  const handleScroll = useCallback(() => {
    const container = containerRef.current;
    if (!container || scrollState.current.isScrolling) return;

    const now = performance.now();
    
    // 限制处理频率到60fps
    if (now - scrollState.current.lastScrollTime < 16) return;
    
    scrollState.current.lastScrollTime = now;
    performanceMetrics.current.scrollEventCount++;

    if (rafId.current) {
      cancelAnimationFrame(rafId.current);
    }

    rafId.current = requestAnimationFrame(() => {
      try {
        const startTime = performance.now();
        
        const scrollTop = container.scrollTop;
        const containerHeight = container.clientHeight;

        // 更新滚动状态
        scrollState.current.scrollTop = scrollTop;
        scrollState.current.containerHeight = containerHeight;

        // 计算可见范围
        const newVisibleRange = calculateVisibleRange(scrollTop, containerHeight);
        
        // 只有当可见范围真正改变时才更新
        if (
          newVisibleRange.start !== lastVisibleRange.current.start ||
          newVisibleRange.end !== lastVisibleRange.current.end
        ) {
          lastVisibleRange.current = newVisibleRange;
          onVisibleRangeChange(newVisibleRange);
        }

        // 计算当前页码
        const newCurrentPage = calculateCurrentPage(scrollTop, containerHeight);
        
        // 只有当页码真正改变时才更新
        if (newCurrentPage !== lastCurrentPage.current) {
          lastCurrentPage.current = newCurrentPage;
          onCurrentPageChange(newCurrentPage);
        }

        // 更新性能指标
        const endTime = performance.now();
        const renderTime = endTime - startTime;
        performanceMetrics.current.renderCount++;
        performanceMetrics.current.averageRenderTime = 
          (performanceMetrics.current.averageRenderTime * (performanceMetrics.current.renderCount - 1) + renderTime) / 
          performanceMetrics.current.renderCount;

      } catch (error) {
        console.error("滚动处理错误:", error);
      }
    });
  }, [
    containerRef,
    calculateVisibleRange,
    calculateCurrentPage,
    onVisibleRangeChange,
    onCurrentPageChange,
  ]);

  /**
   * 平滑滚动到指定页面
   */
  const scrollToPage = useCallback((
    pageNumber: number,
    behavior: 'smooth' | 'instant' = 'smooth'
  ): void => {
    const container = containerRef.current;
    if (!container || pageNumber < 1 || pageNumber > numPages) return;

    const targetLayout = pageLayouts[pageNumber - 1];
    if (!targetLayout) return;

    scrollState.current.isScrolling = true;

    if (behavior === 'smooth') {
      // 自定义平滑滚动动画
      const startY = container.scrollTop;
      const targetY = targetLayout.y;
      const distance = targetY - startY;
      const duration = Math.min(Math.abs(distance) / 2, 500); // 最大500ms
      
      const startTime = performance.now();
      
      const animateScroll = (currentTime: number) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 使用easeInOutCubic缓动函数
        const easeProgress = progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;
        
        const currentY = startY + distance * easeProgress;
        container.scrollTop = currentY;
        
        if (progress < 1) {
          requestAnimationFrame(animateScroll);
        } else {
          scrollState.current.isScrolling = false;
        }
      };
      
      requestAnimationFrame(animateScroll);
    } else {
      container.scrollTop = targetLayout.y;
      scrollState.current.isScrolling = false;
    }
  }, [containerRef, pageLayouts, numPages]);

  /**
   * 获取性能指标
   */
  const getPerformanceMetrics = useCallback((): PerformanceMetrics => {
    return { ...performanceMetrics.current };
  }, []);

  /**
   * 重置性能指标
   */
  const resetPerformanceMetrics = useCallback((): void => {
    performanceMetrics.current = {
      scrollEventCount: 0,
      renderCount: 0,
      cacheHitRate: 0,
      averageRenderTime: 0,
    };
  }, []);

  // 设置滚动事件监听器
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 使用passive事件监听器提高性能
    container.addEventListener("scroll", handleScroll, { passive: true });
    
    // 初始调用一次
    handleScroll();

    return () => {
      container.removeEventListener("scroll", handleScroll);
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, [handleScroll]);

  // 清理RAF
  useEffect(() => {
    return () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, []);

  return {
    scrollToPage,
    getPerformanceMetrics,
    resetPerformanceMetrics,
    scrollState: scrollState.current,
  };
};
