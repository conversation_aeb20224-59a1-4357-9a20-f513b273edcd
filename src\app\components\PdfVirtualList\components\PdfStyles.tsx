import React, { useEffect } from "react";

// 自定义样式组件
const PdfStyles: React.FC = () => {
  useEffect(() => {
    const styles = `
      .react-pdf__Page {
        margin: 10px auto;
        position: relative;
        overflow: hidden;
        background-color: white;
        box-shadow: 0 0px 8px rgba(0,0,0,0.3);
        border-radius: 4px;
        padding-bottom: 0; /* 确保底部没有额外的内边距 */
      }
      
      .react-pdf__Page__textContent {
        user-select: text;
        opacity: 0.2;
        cursor: text;
        padding-bottom: 0; /* 确保文本层底部没有额外的内边距 */
      }
      
      .react-pdf__Page__textContent span {
        color: transparent;
        cursor: text;
      }
      
      .react-pdf__Page__textContent span::selection {
        background: rgb(24, 112, 199) !important;
        color: transparent !important;
      }
      
      .react-pdf__Page__annotations {
        pointer-events: none;
      }

      /* 滚动条样式 */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      ::-webkit-scrollbar-track {
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    `;

    const styleSheet = document.createElement("style");
    styleSheet.innerText = styles;
    document.head.appendChild(styleSheet);

    return () => {
      document.head.removeChild(styleSheet);
    };
  }, []);

  return null;
};

export default PdfStyles;
