import {
  Dialog,
  DialogContent,
  Box,
  Button,
  ButtonProps,
  Divider,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import LoadingButton from "@mui/lab/LoadingButton";
import React from "react";

const TitleBox = styled(`span`, {
  shouldForwardProp: (prop) => prop !== "category",
})<Pick<PaperDialogFrameworkProps, "category">>(
  ({ theme, category = "primary" }) => ({
    padding: "4px 0",
    margin: "4px 24px",
    fontSize: 20,
    lineHeight: 1,
    fontWeight: 700,
    borderBottom: `4px solid ${theme.palette[category].main}`,
  }),
);

const WidthMap = {
  small: 468,
  medium: 640,
  large: 960,
};

const MainContainer = styled("div")<Pick<PaperDialogFrameworkProps, "size">>(
  ({ size }) => ({
    width: WidthMap[size || "small"],
    paddingTop: 20,
    paddingLeft: 6,
    paddingRight: 6,
  }),
);

const StyledDivide = styled(Divider)(() => ({
  margin: "5px 24px",
}));

const MainDialogContent = styled(DialogContent)(() => ({
  display: "flex",
  flexDirection: "column",
}));

const CancelBtn = styled(Button)<ButtonProps>(({ theme }) => ({
  marginLeft: theme.spacing(3),
}));

interface PaperDialogFrameworkProps {
  title: string;
  category?: "primary" | "error";
  size?: "small" | "medium" | "large";
  loading: boolean;
  onClose: () => void;
  onSubmit: () => void;
  hiddenCancelBtn?: boolean;
  confirmBtnText?: string;
  cancelBtnText?: string;
  children: React.ReactNode;
}
const PaperDialogFramework: React.FC<PaperDialogFrameworkProps> = ({
  title,
  onClose,
  category,
  size = "small",
  onSubmit,
  loading,
  children,
  hiddenCancelBtn = false,
  confirmBtnText = "确定",
  cancelBtnText = "取消",
}) => {
  const preventDefault = (event: React.MouseEvent) => {
    event.preventDefault();
  };

  return (
    <Dialog maxWidth={false} open={true} onClose={() => onClose()}>
      <MainContainer size={size} onClick={preventDefault}>
        <TitleBox category={category}>{title}</TitleBox>
        <StyledDivide />
        <MainDialogContent>{children}</MainDialogContent>
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          justifyContent="center"
          pb={4}
        >
          <LoadingButton
            loading={loading}
            variant="contained"
            color="primary"
            onClick={() => onSubmit()}
          >
            {confirmBtnText}
          </LoadingButton>
          {!hiddenCancelBtn && (
            <CancelBtn
              variant="outlined"
              color="primary"
              onClick={() => onClose()}
              className="large-button"
            >
              {cancelBtnText}
            </CancelBtn>
          )}
        </Box>
      </MainContainer>
    </Dialog>
  );
};

export default PaperDialogFramework;
