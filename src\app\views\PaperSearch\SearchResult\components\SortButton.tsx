import { IconButton } from "@mui/material";
import ArrowDropUp from "@mui/icons-material/ArrowDropUpOutlined";
import ArrowDropDown from "@mui/icons-material/ArrowDropDownOutlined";

const Root = styled(Box)(() => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  height: 32,
  borderRadius: 28,
  background: "rgba(242, 242, 242, 1)",
  cursor: "pointer",
  flexShrink: 0,
  padding: "9px 24px",
  boxSizing: "border-box",
  marginLeft: 20,
}));

const StyledIconButton = styled(IconButton)(() => ({
  padding: "0px",
  "& .MuiSvgIcon-root": {
    fontSize: 10,
    "& path": {
      transform: "scale(2.5)",
      transformOrigin: "center",
    },
  },
  "& .active": {
    color: `rgba(24, 112, 199, 1)`,
  },
}));

const SortMap = {
  0: "default",
  1: "asc",
  2: "desc",
};
type SortType = keyof typeof SortMap;
export type SortStatus = (typeof SortMap)[keyof typeof SortMap];

interface SortPartProps {
  label: string;
  onSort: (status: SortStatus) => void;
}

const SortButton: React.FC<SortPartProps> = ({ label, onSort }) => {
  const [sortType, setSortType] = useState<SortType>(0);
  const handleClick = () => {
    let newStatus: SortType;
    switch (sortType) {
      case 0:
        newStatus = 1;
        break;
      case 1:
        newStatus = 2;
        break;
      case 2:
        newStatus = 0;
        break;
    }
    setSortType(newStatus);
    onSort(SortMap[newStatus]);
  };
  return (
    <Root onClick={handleClick}>
      <Typography variant="body2">{label}</Typography>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          ml: 1,
        }}
      >
        <StyledIconButton>
          <ArrowDropUp
            color="inherit"
            className={sortType === 1 ? "active" : ""}
          />
        </StyledIconButton>
        <StyledIconButton>
          <ArrowDropDown
            color="inherit"
            className={sortType === 2 ? "active" : ""}
          />
        </StyledIconButton>
      </Box>
    </Root>
  );
};

export default SortButton;
