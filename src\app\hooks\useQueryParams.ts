import { useState, useEffect } from "react";

export function useQueryParams() {
  const getSearchParams = () => {
    const hash = window.location.hash;
    const searchStr = hash.includes("?")
      ? hash.substring(hash.indexOf("?"))
      : window.location.search;
    return new URLSearchParams(searchStr);
  };

  const [params, setParams] = useState(getSearchParams);

  useEffect(() => {
    const handleUrlChange = () => {
      setParams(getSearchParams());
    };

    window.addEventListener("hashchange", handleUrlChange);
    window.addEventListener("popstate", handleUrlChange);

    return () => {
      window.removeEventListener("hashchange", handleUrlChange);
      window.removeEventListener("popstate", handleUrlChange);
    };
  }, []);

  return params;
}
