import React, {
  useEffect,
  useRef,
  useImperativeHandle,
  forwardRef,
} from "react";
import * as echarts from "echarts";
// 定义子组件的 props 类型
interface EChartComponentProps {
  option: any;
  setKey?: (key: string) => void;
}

export interface EChartComponentRef {
  downloadChart: () => void;
  handleFullScreen: () => void;
}

const ChartsRoot = styled("div")(({}) => ({
  width: "100%",
  height: "100%",
}));
const EChartComponent = forwardRef<EChartComponentRef, EChartComponentProps>(
  ({ option, setKey }, ref) => {
    const chartRef = useRef<HTMLDivElement | null>(null);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const chartInstanceRef = useRef<echarts.ECharts | null>(null);
    // const [tooltip, setTooltip] = useState({
    //   visible: false,
    //   x: 0,
    //   y: 0,
    //   content: "",
    // });
    // const originalData = useRef<string[]>([]);
    useEffect(() => {
      // 监听容器大小变化
      const resizeObserver = new ResizeObserver(() => {
        chartInstanceRef.current?.resize(); // 调整图表大小
      });
      if (chartRef.current) {
        const chart = echarts.init(chartRef.current);
        resizeObserver.observe(chartRef.current);
        chartInstanceRef.current = chart;
        chartInstanceRef.current.setOption(option);
        // const elementDiv = document.getElementById("extension");
        // chartInstanceRef.current.on("mouseover", function (params) {
        //   if (params.componentType == "yAxis" && elementDiv) {
        //     // 设置悬浮文本的位置以及样式
        //     const elementStyle =
        //       "position: absolute;z-index: 99999;color: #000;font-size: 12px;padding:5px 10px;display: inline;border-radius: 5px;background-color: #fff;box-shadow: rgba(0, 0, 0, 0.3) 2px 2px 8px";
        //     elementDiv.style.cssText = elementStyle;
        //     elementDiv.innerHTML = params.value as string;
        //     const htmlElement = document.querySelector("html");
        //     if (htmlElement)
        //       htmlElement.onmousemove = function (event) {
        //         const xx = event.pageX + 20;
        //         const yy = event.pageY;
        //         elementDiv.style.top = yy + "px";
        //         elementDiv.style.left = xx + "px";
        //       };
        //   }
        // });
        // chart.on("mouseout", function (params) {
        //   if (params.componentType === "yAxis" && elementDiv) {
        //     elementDiv.style.cssText = "display:none";
        //   }
        // });
        chart.on("legendselectchanged", function (params: any) {
          const selected = params.selected; // 当前选中状态
          const newSelected: any = {}; // 新的选中状态对象
          // 遍历 selected，找到当前选中的图例项
          for (const name in selected) {
            if (name === params.name) {
              newSelected[name] = true; // 当前选中的项设置为 true
              setKey && setKey(name); // 更新选中的 key
            } else {
              newSelected[name] = false; // 其他项设置为 false
            }
          }

          // 更新图表的选中状态
          chart.setOption({
            legend: {
              selected: newSelected,
            },
          });
        });
      }

      const chartInstance = chartInstanceRef.current;
      if (!chartInstance) return;

      return () => {
        // 清理时销毁图表实例
        resizeObserver.disconnect();
        chartInstanceRef.current?.dispose();
        // chart.getZr().off('mousemove', handleMouseMove);
      };
    }, []);
    useEffect(() => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.setOption(option);
      }
    }, [option]);
    useEffect(() => {
      const handleFullscreenChange = () => {
        if (!document.fullscreenElement) {
          setIsFullscreen(false);
        }
        if (isFullscreen) {
          chartInstanceRef.current?.resize();
        }
      };

      document.addEventListener("fullscreenchange", handleFullscreenChange);

      return () => {
        document.removeEventListener(
          "fullscreenchange",
          handleFullscreenChange,
        );
      };
    }, [isFullscreen]);

    useImperativeHandle(ref, () => ({
      downloadChart: () => downloadCharts(),
      handleFullScreen: () => toggleFullScreen(),
    }));
    // 下载图表
    const downloadCharts = () => {
      if (chartInstanceRef.current) {
        const imgData = chartInstanceRef.current.getDataURL({
          type: "png",
          pixelRatio: 2,
          backgroundColor: "#fff",
        });
        const link = document.createElement("a");
        link.href = imgData;
        link.download = "echarts图表.png";
        link.click();
      }
    };
    // 切换全屏
    const toggleFullScreen = () => {
      if (chartRef.current) {
        if (!document.fullscreenElement) {
          // 进入全屏
          chartRef.current.style.backgroundColor = "#fff";
          chartRef.current.requestFullscreen();
          setIsFullscreen(true);
        }
      }
    };

    return (
      <>
        <ChartsRoot ref={chartRef}></ChartsRoot>
        <div id="extension"></div>
      </>
    );
  },
);

export default EChartComponent;
