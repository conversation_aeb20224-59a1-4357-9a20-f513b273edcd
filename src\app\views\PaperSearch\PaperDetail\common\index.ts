import { anyValueProps } from "@/types/common";

export interface HeaderParamsPorps {
  id: string;
  title: string;
  authors: string[];
  journal: string;
  pdfUrl: string;
  doi: string;
  favorite: boolean;
  publishedYear: number | null;
}

export interface ContentParamsPorps {
  id: string;
  abstract: string;
  keywords: string[];
  images: anyValueProps[];
  tables: anyValueProps[];
  bibls: anyValueProps[];
  figureCount: number;
  tableCount: number;
  zhAbstract: string;
  catalogue: any;
  docType: string;
  zhKeywords: string[];
  language: string;
}

export interface PaperChartPorps {
  type: number;
  imgCount: number;
  pdfId: string;
}

export function splitByTriplePipe(
  input: string | string[],
): string[] | string[][] {
  if (typeof input !== "string" && !Array.isArray(input)) {
    return [];
  }

  // 处理字符串输入（按 ||| 分割，并过滤空字符串）
  if (typeof input === "string") {
    return input.split(/\|\|\|/).filter((part) => part.trim() !== "");
  }

  // 处理数组输入（找到 "|||" 并分割成前后两部分，同时过滤空字符串）
  const delimiterIndex = input.indexOf("|||");

  if (delimiterIndex === -1) {
    // 如果没有找到分隔符，返回原数组（过滤空字符串）和一个空数组
    const filtered = input.filter(
      (item) => typeof item === "string" && item.trim() !== "",
    );
    return [filtered];
  }

  // 分割成前后两部分（不包含分隔符），并分别过滤空字符串
  const before = input
    .slice(0, delimiterIndex)
    .filter((item) => typeof item === "string" && item.trim() !== "");

  const after = input
    .slice(delimiterIndex + 1)
    .filter((item) => typeof item === "string" && item.trim() !== "");

  return [before, after];
}

export const CHART_TYPE_CONTRAST = ["图片", "表格"];
