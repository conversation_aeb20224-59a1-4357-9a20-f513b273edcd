import { RouteProps } from "@/router";
import { anyValueProps } from "@/types/common";

/**
 * 根据权限过滤路由配置
 * @param routes 原始路由配置数组
 * @param menuList 用户拥有的菜单权限列表
 * @returns 过滤后的路由配置数组
 */
export function filterPermRoutes(
  routes: RouteProps[],
  menuList: anyValueProps[],
) {
  return routes.filter((route) => {
    // 无需权限校验的菜单直接保留
    if (!route.menuCode) return true;

    // 查找当前路由对应的权限配置项
    const matchedItem = menuList.find((el) => el.menuCode === route.menuCode);
    if (!matchedItem) return false; // 无权限访问该路由

    const matchedChildren = matchedItem.children;

    // 处理子菜单权限配置
    if (!matchedChildren) return true; // 无子菜单配置时保留父级

    // 当配置了子菜单但未勾选任何项时，禁止访问
    if (!matchedChildren.length) return false;

    // 递归过滤子路由
    if (route.children?.length) {
      const filteredChildren = filterPermRoutes(
        route.children,
        matchedChildren,
      );
      // 保留包含有效子路由的父级
      if (filteredChildren.length > 0) {
        route.children = filteredChildren;
        return true;
      }
    }
    return true;
  });
}

export const filterMenuData = (
  menuList: anyValueProps[],
  allowedIds: anyValueProps[],
) =>
  menuList.filter((menu) => {
    if (allowedIds.includes(menu.id)) {
      return true;
    } // 如果有子菜单，则递归过滤子菜单
    if (menu.children) {
      menu.children = filterMenuData(menu.children, allowedIds);
      return menu.children.length > 0; // 如果子菜单有剩余项，保留当前菜单
    }
    return false;
  });

export const PERMISSION_MENU = {
  chat: "CHAT",
  edit: "EDIT",
  share: "SHARE",
  query: "QUERY",
  normal: "NORMAL",
};
