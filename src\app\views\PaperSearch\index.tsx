import React from "react";
import { Box, Typography, styled } from "@mui/material";
import SearchInput, {
  SearchInputRefProps,
} from "./components/SearchInput/index";
import RelatedRecommend from "./components/RelatedRecommend";
import useSearchRecord from "@/hooks/useSearchRecord";
import { getRecommended } from "@/api/paperSearch";
import { getChatHistory } from "@/api/chat";
import { useAppDispatch } from "@/hooks";
import { setListTotal } from "@/store/counterSlice";
// import { v4 as uuidv4 } from "uuid";

const Root = styled("div")(() => ({
  position: "relative",
  height: "100%",
  overflow: "auto",
}));

const Main = styled("div")(() => ({
  color: "#000",
  width: "85%",
  margin: "0 auto",
  paddingTop: 70,
  position: "relative",
  zIndex: 2,
}));

const RecommendBox = styled("div")(() => ({
  display: "grid",
  gridTemplateColumns: "repeat(2,1fr)",
  gap: 40,
}));

const HistoryWord = styled(Typography)(({ theme }) => ({
  maxWidth: 100,
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  cursor: "pointer",
  marginLeft: 28,
  textDecoration: "underline",
  "&:hover": {
    color: theme.palette.primary.main,
  },
}));

const EmptyMsg = styled("div")(() => ({
  height: "300px",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  color: "rgba(0,0,0,0.4)",
  fontSize: "14px",
}));
interface SearchHistoryProps {
  searchRef: React.RefObject<SearchInputRefProps>;
}
const SearchHistory: React.FC<SearchHistoryProps> = ({ searchRef }) => {
  const dispatch = useAppDispatch();
  const { onSearchClick, getHistoryKeys } = useSearchRecord();
  const historyWords = getHistoryKeys().slice(-5);
  // 历史记录检索，只是简单检索--检索searches为一条
  const handleHistoryClick = (content: string) => {
    if (searchRef.current) {
      onSearchClick({
        query: content,
        searches: [],
      });
    }
  };
  const getScrollList = async () => {
    const {
      data: { total, code },
    } = await getChatHistory({
      page: 1,
      size: 15,
    });
    if (code === 200) {
      dispatch(setListTotal(total));
    } else {
      return;
    }
  };

  useEffect(() => {
    getScrollList();
  }, []);
  return (
    <>
      {historyWords.length > 0 && (
        <Box
          sx={{
            width: "100%",
            mt: 2,
            zIndex: 1,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Typography variant="body1" sx={{ mr: "2px" }}>
            搜索历史：
          </Typography>
          {historyWords?.map((content: string, index: number) => (
            <HistoryWord
              key={index}
              variant="body1"
              onClick={() => handleHistoryClick(content)}
              title={content}
            >
              {content}
            </HistoryWord>
          ))}
        </Box>
      )}
    </>
  );
};

const PaperSearch: React.FC = () => {
  const searchRef = useRef<SearchInputRefProps>(null);
  const [data, setData] = useState<any[]>([]);

  const processData = (data: any[]) =>
    data.map((item) => ({
      ...item,
      authors: item.authors
        ? JSON.parse(item.authors).map((item: string) =>
            typeof item === "string" ? item.trim() : item,
          )
        : [], // 将authors从字符串转换为数组
      keywords: item.keywords ? JSON.parse(item.keywords) : [], // 将keywords从字符串转换为数组

      paragraph: item.firstPara ? item.firstPara : "", // 将firstPara字段名改为paragraph
      secondPara: item.secondPara,
      pdfId: item.id,
      ext2: item.ext2 ? item.ext2 : "",
    }));
  const init = async () => {
    const {
      data: { data, code },
    } = await getRecommended();
    if (code === 200) {
      setData(processData(data));
    } else {
      setData([]);
      message.error(data?.msg);
    }
  };

  useEffect(() => {
    init();
  }, []);
  return (
    <Root>
      <Main>
        <SearchInput ref={searchRef} />
        <SearchHistory searchRef={searchRef} />
        <Box sx={{ mt: "38px", mb: "20px" }}>
          <Typography variant="subtitle1" sx={{ lineHeight: 1, mb: 3 }}>
            相关推荐 :
          </Typography>
          {data.length ? (
            <RecommendBox>
              {data.map((item, index) => (
                <RelatedRecommend item={item} key={index} />
              ))}
            </RecommendBox>
          ) : (
            <EmptyMsg>
              <div>暂无相关推荐</div>
            </EmptyMsg>
          )}
        </Box>
      </Main>
    </Root>
  );
};

export default PaperSearch;
