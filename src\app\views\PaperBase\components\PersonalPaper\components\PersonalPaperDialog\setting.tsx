import { FormColumnProps } from "@/components/DynamicForm";
import dayjs from "dayjs";
import * as yup from "yup";

export const sleep = (ms: number | undefined) =>
  new Promise((resolve) => setTimeout(resolve, ms));

export const EditColumns = (
  language: string | undefined,
): FormColumnProps[] => [
  ...(language && language === "zh"
    ? [
        {
          name: "zhTitle",
          label: "中文标题",
          componentType: "input" as const,
          grid: 6,
          styleProps: {
            height: 38,
          },
        },
      ]
    : []),
  {
    name: "title",
    label: language && language === "zh" ? "英文标题" : "标题",
    componentType: "input",
    grid: 6,
    styleProps: {
      height: 38,
    },
  },

  // {
  //   name: "doi",
  //   label: "DOI",
  //   componentType: "input",
  //   grid: 6,
  //   styleProps: {
  //     height: 38,
  //   },
  // },
  ...(language && language === "zh"
    ? [
        {
          name: "zhAuthors",
          label: "中文作者",
          componentType: "input" as const,
          grid: 6,
          styleProps: {
            height: 38,
          },
        },
      ]
    : []),
  {
    name: "authors",
    label: language && language === "zh" ? "英文作者" : "作者",
    componentType: "input",
    grid: 6,
    styleProps: {
      height: 38,
    },
  },
  // {
  //   name: "journal",
  //   label: "期刊",
  //   componentType: "input",
  //   grid: 6,
  //   styleProps: {
  //     height: 38,
  //   },
  // },
  {
    name: "publishedYear",
    label: "出版年",
    componentType: "input",
    grid: 6,
    styleProps: {
      height: 38,
    },
    validation: yup.number().typeError("请输入有效的出版年"),
  },
];

const taskTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
const taskName = `任务${taskTime}`;

export const UploadColumns: FormColumnProps[] = [
  {
    name: "name",
    label: "任务名称",
    componentType: "input",
    grid: 12,
    required: true,
    defaultValue: taskName,
    styleProps: {
      height: 38,
    },
  },
  {
    name: "file",
    label: "",
    componentType: "upload",
    grid: 12,
    required: true,
    styleProps: {
      height: 250,
    },
    componentProps: {
      limit: {
        allowedFileTypes: [".pdf"],
        maxTotalFileSize: 200 * 1024 * 1024,
      },
    },
    validation: yup
      .array()
      .min(1, "上传文件不能为空")
      .required("上传文件不能为空"),
  },
];
// interface SelectItemProps {
//   label: string;
//   value: string;
//   roleName?: string;
// }
export const AddPaperBaseColumns: FormColumnProps[] = [
  {
    name: "name",
    label: "名称",
    componentType: "input",
    required: true,
    validation: yup
      .string()
      .min(1, "名称不能为空")
      // .max(15, "名称不能超过15个字符")
      .required("名称不能为空"),
    grid: 12,
    placeholder: "请输入名称",
    maxLength: 15,
  },
  {
    name: "version",
    label: "版本",
    componentType: "input",
    required: true,
    validation: yup
      .string()
      .required("版本不能为空")
      .min(1, "版本不能为空")
      .max(15, "版本不能超过15个字符")
      .matches(
        /^[vV]\d{1,2}\.\d{1,2}(\.\d{1,2})?$/,
        "版本格式不正确,如v1.0,v1.0.0,数字位最多两位",
      ),
    grid: 12,
    placeholder: "例:v1.0.0",
    maxLength: 9,
  },
  {
    name: "note",
    label: "描述",
    componentType: "textarea",
    grid: 12,
    componentProps: {
      minRows: 3,
      maxRows: 3,
    },
    placeholder: "请输入描述",
    validation: yup.string().max(100, "描述不能超过100个字符"),
    maxLength: 100,
  },
];
