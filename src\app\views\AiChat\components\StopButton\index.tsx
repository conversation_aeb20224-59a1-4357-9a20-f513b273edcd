import { SvgIcon } from "@mui/material";

interface Props {
  handleStop: () => void;
}

const StopBtn = styled("div")(() => ({
  position: "relative",
  // top:"-100%",
  // left:"50%",
  // transform: "translate(-50%, -50%)",
  display: "flex",
  justifyContent: "center",
}));
const Index: React.FC<Props> = ({ handleStop }) => (
  <StopBtn>
    <Button
      sx={{
        backgroundColor: "#f0a020",
        display: "flex",
        alignItems: "center",
        color: "#fff",
        padding: "3px 5px",
      }}
      onClick={handleStop}
    >
      <SvgIcon
        sx={{
          width: "18px",
          height: "18px",
          margin: "0 3px",
        }}
      >
        <path
          fill="currentColor"
          d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10m0-2a8 8 0 1 0 0-16a8 8 0 0 0 0 16M9 9h6v6H9z"
        ></path>
      </SvgIcon>
      停止响应
    </Button>
  </StopBtn>
);
export default Index;
