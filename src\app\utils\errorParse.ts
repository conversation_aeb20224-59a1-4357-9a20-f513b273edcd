const getMsg = (code: any, msg: any) => {
  if (code === 403) {
    return "没有权限";
  }
  if (code === 401) {
    return "请重新登录";
  }
  if (code === -100) {
    return "查询超出限制";
  }
  if (code === "ECONNABORTED") {
    return "请求超时";
  }
  return msg ?? "请稍后再试";
};

export const getErrorMessage = (error: any) => {
  let message = "请稍后再试";
  if (error && error.code) {
    const { msg, code } = error;
    message = getMsg(code, msg);
  } else if (error.response) {
    const { status } = error.response;
    if (status === 401) {
      message = "请重新登录";
    } else if (error.response.data) {
      const { msg, code } = error.response.data;
      message = getMsg(code, msg);
    }
  } else if ("toJSON" in error) {
    const jsonError = error.toJSON();
    message = jsonError.message;
  } else if ("message" in error) {
    message = error.message;
  }

  return message;
};
