// 资料搜索API
import axios from "axios";
import { apiBaseUrl, responseProps, ExcelApiBaseUrl } from "./config";
import { anyValueProps } from "@/types/common";
export const getPaperList = (params: anyValueProps) =>
  axios.post<responseProps>(`/engine/api/pdf/full-text`, params);

export const getWordCloud = (id: string) =>
  axios.get(`/engine/api/pdf/word-cloud/${id}`);

export const getImageList = (params: anyValueProps) =>
  axios.post<responseProps>(`${apiBaseUrl}/common/download-fileList`, params, {
    responseType: "arraybuffer",
  });

export const getPaperTitle = (id: string) =>
  axios.get(`${apiBaseUrl}/query/paper-info/${id}`);
interface formDataProps {
  doi: string;
  pageIndex: number;
  pageSize: number;
}

export const getCited = (formData: formDataProps) =>
  axios.post(`${apiBaseUrl}/query/cited`, formData);
export const getReferences = (formData: formDataProps) =>
  axios.post(`${apiBaseUrl}/query/references`, formData);

export const ConvertExcel = (formData: anyValueProps) =>
  axios({
    url: `${ExcelApiBaseUrl}/common/download-excel`,
    method: "post",
    data: formData,
  });

// 资料详情数据数据查询
export const getPaperDetail = (params: anyValueProps) =>
  axios({
    url: `/engine/api/pdf/detail`,
    method: "get",
    params,
  });

//  获取文件
export const getFile = (url: string) =>
  axios({
    url: `/engine/api/file?${url}`,
    method: "get",
    responseType: "blob",
  });

// 下载文件
export const downloadFile = (url: string) =>
  axios({
    url: `/engine/api/file/download?${url}`,
    method: "get",
    responseType: "blob",
  });

// 高级检索数据查询
export const getSearchResultList = (params: anyValueProps) =>
  axios({
    url: `/engine/api/pdf/full-text`,
    method: "post",
    data: params,
  });

//  分析获取数据
export const getPdfCountStats = (params: anyValueProps) =>
  axios({
    url: `/engine/api/pdf/countStats?limit=${100}`,
    method: "post",
    data: params,
  });
//milvus进行语义搜索和标量过滤
export const getHybridSearch = (params: anyValueProps) =>
  axios({
    url: `/engine/api/pdf/milvus-hybrid-search`,
    method: "post",
    data: params,
  });

// 获取pdf段落,携带坐标信息
export const getPdfParagraph = (params: anyValueProps) =>
  axios({
    url: `/engine/api/paragraph/get`,
    method: "get",
    params,
  });

export const cancelFavoriteByMd5orAuthor = (content: string) =>
  axios.get<responseProps>(
    `${apiBaseUrl}/favorite/delete-detail-by-content?content=${content}`,
  );

interface addFavoriteProps {
  name: string; // 文件夹名称
  type: string; // 文件夹类型， 1 资料收藏夹， 2 作者收藏夹
}

/**
 * 创建PDF关联关系
 */
export const addFavorite = (params: addFavoriteProps) =>
  axios.post<responseProps>(`/engine/api/group/create`, params);

export const getFavorites = () =>
  axios.get<responseProps>(`/engine/api/group/bookMarks`);

export const addPdfAssociation = (params: anyValueProps) =>
  axios.post<responseProps>(`/engine/api/pdf/relation/document`, params);

export const queryLinkUrl = () =>
  axios.get<responseProps>(`/engine/system/config?key=aichem_chat_url`);

export const pdfScore = (params: anyValueProps) =>
  axios.post<responseProps>(`/engine/api/pdf/relation/create`, params);

export const getPaperBaseList = (params?: anyValueProps) =>
  axios.get("/engine/api/group/documents", { params });

export const getRelatedLiterature = (id: string) =>
  axios.get(`/engine/api/pdf/related-doc/${id}`);

export const getRelatedLiteratureAuthor = (id: string) =>
  axios.get(`/engine/api/pdf/related-doc-author/${id}`);
export const getRecommended = () => axios.get(`/engine/api/pdf/recommend`);

export const getAdminDocumentList = (params: anyValueProps) =>
  axios.get(`/engine/api/group/resource/document-list`, { params });

export const adminDocumentAdd = (params: anyValueProps) =>
  axios({
    url: "/engine/api/pdf/relation/admin-document",
    method: "post",
    data: params,
  });
// axios(url: `/engine/api/pdf/relation/admin-document`, params);

export const queryImgList = (params: anyValueProps) =>
  axios({
    url: "/engine/api/pdf/pdf-picture",
    method: "post",
    data: params,
  });
