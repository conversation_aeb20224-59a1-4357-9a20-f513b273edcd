import { forwardRef, useImperativeHandle } from "react";
import {
  styled,
  TextField,
  Grid2 as Grid,
  FormLabel,
  FormControl,
  FormHelperText,
  ToggleButton,
  ToggleButtonGroup,
  OutlinedInput,
  InputAdornment,
  IconButton,
  RadioGroup,
  FormControlLabel,
  TextareaAutosize,
  Typography,
} from "@mui/material";
import { useFormik } from "formik";
import * as yup from "yup";
import { anyValueProps } from "@/types/common";
import UppyUpload from "../UppyUpload";
import DatePicker from "@/components/DatePicker";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { BpRadio } from "./setting";
import Select, { SelectItemProps } from "../Select";
/**
 * 1、表单使用列子：column = [
            {
              name: "email",
              label: "Email",
              valueType: "string",// 值的类型
              componentType: "input",
              required: true,
              validation: yup
                .string()
                .email("Invalid email format") // email规则
                .min(6, "Password should be at least 6 characters") // 限制长度
                .required("Email is required"),
              defaultValue:'aa'
            }
      ]
    2、外部触发表单校检并接受数据 ： const values = await Ref.current?.submit()
    3、formik.values 可在任意时刻抛出表单当前数据
 */

const StyledToggleButtonGroup = styled(ToggleButtonGroup)(({ theme }) => ({
  height: "100%",
  ".Mui-selected": {
    color: "#fff !important",
    background: theme.palette.primary.main + " !important",
  },
}));
const Textarea = styled(TextareaAutosize)(
  ({ theme }) => `
  box-sizing: border-box;
  width: 100%;
  font-family: 'IBM Plex Sans', sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  padding: 8px 12px;
  border-radius: 8px;
  background: ${theme.palette.mode === "dark" ? "#ccc" : "#fff"};
  border: 1px solid #ccc;
  padding-bottom: 32px;
  &:focus {
    border-color: ${theme.palette.primary.main};
  }
  // firefox
  &:focus-visible {
    outline: 0;
  }
`,
);

const StyledTextField = styled(TextField)(() => ({
  "& .MuiInputBase-root": {
    borderRadius: 28,
  },
}));

const PasswordOutlinedInput = styled(OutlinedInput)(() => ({
  "&.MuiOutlinedInput-root": {
    borderRadius: 28,
  },
}));

const StyleFormControlLabel = styled(FormControlLabel, {
  shouldForwardProp: (props) => props !== "isSelect",
})<{ isSelect: boolean }>(({ isSelect }) => ({
  width: 78,
  height: 38,
  background: "#fff",
  border: `1px solid ${!isSelect ? "rgba(235, 235, 235, 1)" : "rgba(24, 112, 199, 1)"}`,
  borderRadius: 24,
  "& .MuiTypography-root": {
    color: `${!isSelect ? "#000" : "rgba(24, 112, 199, 1)"}`,
  },
}));

type componentType =
  | "input"
  | "select"
  | "toggle-button"
  | "date"
  | "time"
  | "upload"
  | "password"
  | "radio-group"
  | "textarea";

export interface FormColumnProps {
  name: string; // 绑定字段
  label: string; // 名称
  valueType?: "string" | "number"; // 输入框值类型
  componentType: componentType; // 组件类型
  validation?: yup.AnySchema;
  required?: boolean;
  placeholder?: string;
  options?: SelectItemProps[];
  grid?: number;
  defaultValue?: any;
  styleProps?: React.CSSProperties;
  labelStyleProps?: React.CSSProperties;
  componentProps?: anyValueProps;
  maxLength?: number;
  multiple?: boolean;
}

interface DynamicFormProps {
  columns: FormColumnProps[];
  size?: "medium" | "small";
  labelWidth?: number | string;
  rowSpacing?: number;
  columnSpacing?: number;
  formData?: anyValueProps | null; // 表单回显数据
  onChange?: (key: string, value: any) => void;
  direction?: "row" | "column";
}
export type RefProps = {
  submit: () => Promise<unknown>;
  values: () => void;
};

const DynamicForm = forwardRef((props: DynamicFormProps, ref) => {
  const {
    columns,
    size = "medium",
    formData = null,
    onChange,
    labelWidth = "25%",
    rowSpacing = 0,
    columnSpacing = 2,
    direction = "row",
  } = props;
  const uppyRef = useRef<any>(null);
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>(
    {},
  );
  const handleClickShowPassword = (fieldName: string) => {
    setShowPasswords((prev) => ({
      ...prev,
      [fieldName]: !prev[fieldName],
    }));
  };
  const handleMouseDownPassword = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    event.preventDefault();
  };
  const handleMouseUpPassword = (
    event: React.MouseEvent<HTMLButtonElement>,
  ) => {
    event.preventDefault();
  };

  const validationSchema = yup.object(
    columns.reduce(
      (acc, column) => {
        if (column.required) {
          acc[column.name] = yup.string().required(`${column.label}为必填项`);
        }
        if (column.validation) {
          acc[column.name] = column.validation;
        }
        return acc;
      },
      {} as { [key: string]: any },
    ),
  );
  const initialValues = columns.reduce(
    (acc, column) => {
      // 处理mui组件默认值 +  undefined时组件会警告，导致表单异常
      acc[column.name] =
        column.defaultValue === undefined ? "" : column.defaultValue;
      return acc;
    },
    {} as { [key: string]: any },
  );

  // 外部更新表单值--注意传入表单数据的完整性，存在属性未传|undefined报组件警告
  useEffect(() => {
    formData && formik.setValues(formData);
  }, [formData]);

  const formik = useFormik({
    // 表单初始赋值
    initialValues,
    validationSchema,
    enableReinitialize: true, // 仅在 initialValues 改变时更新
    onSubmit: () => {}, // 不处理onSubmit
  });

  const submitForm = useMemo(
    () => () =>
      new Promise((resolve, reject) => {
        formik.validateForm().then((errors) => {
          formik.setTouched(
            Object.keys(formik.values).reduce(
              (acc, key) => ({ ...acc, [key]: true }),
              {},
            ),
            true,
          );
          Object.keys(errors).length === 0
            ? resolve(formik.values)
            : reject(errors);
        });
      }),
    [formik.values],
  );

  useImperativeHandle(ref, () => ({
    values: formik.values,
    submit: submitForm,
  }));

  const handleChange = (key: string, value: any) => {
    // // 如果传入了 maxLength，且值是字符串
    // if (maxLength && typeof value === 'string') {
    //   // 使用 slice 截取字符串，确保其不超过 maxLength
    //   const truncatedValue = value.slice(0, maxLength);
    //   formik.setFieldValue(key, truncatedValue);
    //   onChange && onChange(key, truncatedValue);
    // } else {
    //   // 如果没有传入 maxLength 或者 value 不是字符串，直接更新
    formik.setFieldValue(key, value);
    onChange && onChange(key, value);
    // }
  };

  const getUploadFiles = (key: string, value: any) => {
    const filesArray = value.map((item: any) => item.data);
    handleChange(key, filesArray);
  };

  return (
    <Box component={"form"} sx={{ width: "100%", height: "100%" }}>
      <Grid
        container
        rowSpacing={rowSpacing}
        columnSpacing={columnSpacing}
        sx={{ height: "100%" }}
      >
        {columns.map((column: FormColumnProps) => {
          const value = formik.values[column.name] ?? "";

          const error =
            formik.touched[column.name] && Boolean(formik.errors[column.name]);

          const helperText =
            formik.touched[column.name] &&
            String(formik.errors[column.name] || "");

          return (
            <Grid
              size={column.grid || 3}
              key={column.name}
              // sx={{ height: "100%" }}
            >
              <FormControl
                fullWidth
                error={error}
                sx={{
                  flexDirection: direction,
                  height: "100%",
                }}
              >
                {column.label && (
                  <FormLabel
                    component={"div"}
                    required={column.required}
                    title={column.label}
                    sx={{
                      textWrap: "nowrap",
                      mr: 1.25,
                      mt: 1,
                      width: labelWidth,
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                      whiteSpace: "nowrap",
                      textAlign: "start",
                      color: "rgba(31, 31, 31, 1)",
                      fontWeight: 400,
                      fontSize: direction === "row" ? 16 : 14,
                      justifyContent:
                        direction === "row" ? "flex-end" : "flex-start",
                      ...(direction === "column" ? { mb: "11px" } : {}),
                      ...column.labelStyleProps,
                    }}
                  >
                    {column.label}
                  </FormLabel>
                )}
                <Box sx={{ flex: "1" }}>
                  {column.componentType === "input" && (
                    <StyledTextField
                      fullWidth
                      size={size}
                      name={column.name}
                      type={column.valueType || "string"}
                      error={error}
                      value={value}
                      placeholder={column.placeholder || "请输入"}
                      onChange={(e) =>
                        handleChange(column.name, e.target.value)
                      }
                      onBlur={formik.handleBlur}
                      sx={{ ...column.styleProps }}
                      autoComplete="off"
                      slotProps={{
                        ...(column.maxLength
                          ? {
                              htmlInput: {
                                maxLength: column.maxLength,
                              },
                            }
                          : {}),
                        input: {
                          endAdornment: column.maxLength && (
                            <InputAdornment
                              position="end"
                              style={{ color: "rgba(0, 0, 0, 0.45)" }}
                            >
                              {typeof value === "string" && value.length}/
                              {column.maxLength}
                            </InputAdornment>
                          ),
                        },
                      }}
                    />
                  )}
                  {column.componentType === "radio-group" && (
                    <RadioGroup
                      row
                      name={column.name}
                      value={value}
                      onChange={(e) =>
                        handleChange(column.name, e.target.value)
                      }
                      sx={{ pl: 1.5, justifyContent: "space-between" }}
                    >
                      {column.options?.map((option, index) => (
                        <StyleFormControlLabel
                          key={index}
                          value={
                            typeof option === "object" ? option.value : option
                          }
                          control={<BpRadio />}
                          label={
                            typeof option === "object" ? option.label : option
                          }
                          isSelect={
                            typeof option === "object"
                              ? option.value === value
                              : option === value
                          }
                        />
                      ))}
                    </RadioGroup>
                  )}
                  {column.componentType === "password" && (
                    <PasswordOutlinedInput
                      fullWidth
                      size={size}
                      id="outlined-adornment-password"
                      value={value}
                      onChange={(e) =>
                        handleChange(column.name, e.target.value)
                      }
                      sx={{ ...column.styleProps }}
                      name={column.name}
                      type={showPasswords[column.name] ? "text" : "password"}
                      placeholder={column.placeholder || "请输入"}
                      onBlur={formik.handleBlur}
                      autoComplete="new-password"
                      endAdornment={
                        <InputAdornment position="end" sx={{ mr: 1 }}>
                          <IconButton
                            aria-label={
                              showPasswords[column.name]
                                ? "hide the password"
                                : "display the password"
                            }
                            onClick={() => handleClickShowPassword(column.name)}
                            onMouseDown={handleMouseDownPassword}
                            onMouseUp={handleMouseUpPassword}
                            edge="end"
                            sx={{ fontSize: 14 }}
                          >
                            {showPasswords[column.name] ? (
                              <Visibility sx={{ fontSize: 18 }} />
                            ) : (
                              <VisibilityOff sx={{ fontSize: 18 }} />
                            )}
                          </IconButton>
                        </InputAdornment>
                      }
                    />
                  )}
                  {column.componentType === "select" && (
                    <Select
                      fullWidth
                      size={size}
                      multiple={column.multiple}
                      name={column.name}
                      value={value}
                      onChange={(e) =>
                        handleChange(column.name, e.target.value)
                      }
                      onBlur={formik.handleBlur}
                      defaultValue={column.defaultValue}
                      sx={{ ...column.styleProps }}
                      options={column.options || []}
                      {...column.componentProps}
                    />
                  )}
                  {column.componentType === "toggle-button" && (
                    <StyledToggleButtonGroup
                      value={value}
                      size={size}
                      exclusive
                      onChange={(_, value) =>
                        value !== null && handleChange(column.name, value)
                      }
                    >
                      {column.options?.map((option, index) => (
                        <ToggleButton
                          key={index}
                          value={
                            typeof option === "object" ? option.value : option
                          }
                        >
                          {typeof option === "object" ? option.label : option}
                        </ToggleButton>
                      ))}
                    </StyledToggleButtonGroup>
                  )}
                  {column.componentType === "textarea" && (
                    <div style={{ width: "100%", position: "relative" }}>
                      <Textarea
                        name={column.name}
                        onChange={(e) =>
                          handleChange(column.name, e.target.value)
                        }
                        {...column.componentProps}
                        onBlur={formik.handleBlur}
                        value={value}
                        placeholder={column.placeholder || "请输入"}
                        maxLength={column.maxLength}
                        sx={{
                          resize: "none", // 禁止调整大小
                        }}
                      />
                      {column.maxLength && (
                        <Typography
                          variant="caption"
                          sx={{
                            position: "absolute",
                            bottom: 8,
                            right: 12,
                            color: "rgba(0, 0, 0, 0.45)",
                            fontSize: "14px",
                            userSelect: "none",
                            backgroundColor: "transparent",
                          }}
                        >
                          {typeof value === "string" ? value.length : 0}/
                          {column.maxLength}
                        </Typography>
                      )}
                    </div>
                  )}

                  {column.componentType === "date" && (
                    <DatePicker
                      onChange={handleChange}
                      name={column.name}
                      value={value}
                    />
                  )}
                  {column.componentType === "upload" && (
                    <Box sx={{ ...column.styleProps }}>
                      <UppyUpload
                        ref={uppyRef}
                        {...column.componentProps}
                        changeFile={(files) =>
                          getUploadFiles(column.name, files)
                        }
                      />
                    </Box>
                  )}
                  {/* 非校检表单不展示helperText */}
                  {columns.some(
                    (column) => column.required || column.validation,
                  ) && (
                    <FormHelperText sx={{ mt: 0 }}>
                      {helperText || " "}
                    </FormHelperText>
                  )}
                </Box>
              </FormControl>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
});

export default DynamicForm;
