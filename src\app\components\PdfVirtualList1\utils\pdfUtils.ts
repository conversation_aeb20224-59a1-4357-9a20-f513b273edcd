import { pdfjs } from "react-pdf";

// 设置PDF worker路径
pdfjs.GlobalWorkerOptions.workerSrc = window.APP_CONFIG.BASE_PATH
  ? `${window.APP_CONFIG.BASE_PATH}/pdf.worker.min.js`
  : "/pdf.worker.min.js";

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => ReturnType<T> | void {
  let timeout: ReturnType<typeof setTimeout>;
  return function executedFunction(...args: Parameters<T>): void {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 页面布局接口
export interface PageLayout {
  y: number;
  height: number;
}

// PDF虚拟列表属性接口
export interface PDFVirtualListProps {
  url: string;
  initialVisiblePages?: number;
  coordsData?: any[];
}
