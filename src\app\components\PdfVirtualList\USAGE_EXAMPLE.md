# PdfVirtualList 使用示例

## 基础使用

```tsx
import React from 'react';
import PDFVirtualList from './components/PdfVirtualList';

const MyPdfViewer: React.FC = () => {
  const handleGetParagraph = (paragraph: string, type: string) => {
    console.log('选中段落:', paragraph, '类型:', type);
  };

  return (
    <div style={{ width: '100%', height: '100vh' }}>
      <PDFVirtualList
        url="/path/to/your/document.pdf"
        initialVisiblePages={3}
        coordsData={annotationData}
        operation={operationButtons}
        getParagraph={handleGetParagraph}
      />
    </div>
  );
};
```

## 高级使用 - 性能监控

```tsx
import React, { useRef, useEffect } from 'react';
import PDFVirtualList from './components/PdfVirtualList';

const AdvancedPdfViewer: React.FC = () => {
  const pdfRef = useRef<any>(null);

  useEffect(() => {
    // 定期检查性能统计
    const interval = setInterval(() => {
      if (pdfRef.current?.getStats) {
        const stats = pdfRef.current.getStats();
        console.log('PDF性能统计:', stats);
        
        // 如果缓存命中率过低，可以考虑调整策略
        if (stats.pageCache.hitRate < 0.5) {
          console.warn('缓存命中率较低，考虑优化');
        }
      }
    }, 30000); // 每30秒检查一次

    return () => clearInterval(interval);
  }, []);

  const handleMemoryPressure = () => {
    // 在内存压力大时手动清理缓存
    if (pdfRef.current?.clearCaches) {
      pdfRef.current.clearCaches();
      console.log('已清理PDF缓存');
    }
  };

  return (
    <div style={{ width: '100%', height: '100vh' }}>
      <button onClick={handleMemoryPressure}>
        清理缓存
      </button>
      <PDFVirtualList
        ref={pdfRef}
        url="/path/to/large-document.pdf"
        initialVisiblePages={5} // 大文档可以增加可见页面数
        coordsData={annotationData}
      />
    </div>
  );
};
```

## 自定义滚动控制

```tsx
import React, { useRef } from 'react';
import PDFVirtualList from './components/PdfVirtualList';

const CustomScrollPdfViewer: React.FC = () => {
  const pdfRef = useRef<any>(null);

  const jumpToPage = (pageNumber: number) => {
    if (pdfRef.current?.scrollToPage) {
      pdfRef.current.scrollToPage(pageNumber, 'smooth');
    }
  };

  const jumpToPageInstant = (pageNumber: number) => {
    if (pdfRef.current?.scrollToPage) {
      pdfRef.current.scrollToPage(pageNumber, 'instant');
    }
  };

  return (
    <div style={{ width: '100%', height: '100vh' }}>
      <div style={{ padding: '10px', borderBottom: '1px solid #ccc' }}>
        <button onClick={() => jumpToPage(1)}>
          平滑跳转到第1页
        </button>
        <button onClick={() => jumpToPageInstant(10)}>
          立即跳转到第10页
        </button>
        <button onClick={() => jumpToPage(50)}>
          平滑跳转到第50页
        </button>
      </div>
      <PDFVirtualList
        ref={pdfRef}
        url="/path/to/document.pdf"
        initialVisiblePages={3}
      />
    </div>
  );
};
```

## 注释数据格式示例

```tsx
const annotationData = [
  {
    id: 1,
    head: "重要段落",
    paragraph: "这是一个重要的段落内容...",
    coords: ["100,200", "300,250"],
    frameCoords: [
      {
        page: 1,
        pageSize: {
          widthPt: 595,
          heightPt: 842
        },
        upLeft: [100, 200],
        lowRight: [300, 250],
        upLeftScale: [0.168, 0.237],
        lowRightScale: [0.504, 0.297]
      }
    ],
    paragraphId: 1001,
    type: "highlight"
  }
];

const operationButtons = [
  {
    type: "copy",
    label: "复制",
    disable: false
  },
  {
    type: "highlight",
    label: "高亮",
    disable: false
  }
];
```

## 性能优化建议

### 1. 合理设置可见页面数
```tsx
// 小文档 (< 20页)
<PDFVirtualList initialVisiblePages={3} />

// 中等文档 (20-100页)
<PDFVirtualList initialVisiblePages={4} />

// 大文档 (> 100页)
<PDFVirtualList initialVisiblePages={5} />
```

### 2. 监控内存使用
```tsx
useEffect(() => {
  const checkMemory = () => {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      if (memInfo.usedJSHeapSize > 100 * 1024 * 1024) { // 100MB
        // 清理缓存
        pdfRef.current?.clearCaches();
      }
    }
  };

  const interval = setInterval(checkMemory, 60000); // 每分钟检查
  return () => clearInterval(interval);
}, []);
```

### 3. 处理大量注释
```tsx
// 对于包含大量注释的文档，可以考虑分批处理
const processAnnotationsInBatches = (annotations: RawAnnotation[]) => {
  const batchSize = 100;
  const batches = [];
  
  for (let i = 0; i < annotations.length; i += batchSize) {
    batches.push(annotations.slice(i, i + batchSize));
  }
  
  return batches;
};
```

## 故障排除

### 常见问题及解决方案

1. **滚动性能问题**
   ```tsx
   // 检查是否设置了过多的可见页面
   <PDFVirtualList initialVisiblePages={3} /> // 推荐值
   ```

2. **内存使用过高**
   ```tsx
   // 定期清理缓存
   useEffect(() => {
     const cleanup = setInterval(() => {
       pdfRef.current?.clearCaches();
     }, 5 * 60 * 1000); // 每5分钟清理一次
     
     return () => clearInterval(cleanup);
   }, []);
   ```

3. **注释显示异常**
   ```tsx
   // 确保注释数据格式正确
   const validateAnnotation = (annotation: RawAnnotation) => {
     return annotation.frameCoords && 
            annotation.frameCoords.length > 0 &&
            annotation.frameCoords[0].upLeftScale &&
            annotation.frameCoords[0].lowRightScale;
   };
   ```

## 开发模式调试

在开发环境中，组件会自动输出性能统计信息：

```
PDF Virtual List Performance Stats: {
  pageCache: {
    size: 15,
    maxSize: 20,
    hitRate: 0.75,
    sizeByScale: { "1": 10, "1.5": 5 }
  },
  scroll: {
    scrollEventCount: 150,
    renderCount: 45,
    averageRenderTime: 12.5
  }
}
```

这些信息可以帮助你：
- 监控缓存效率
- 识别性能瓶颈
- 优化配置参数
