import { anyValueProps } from "@/types/common";
import { Tooltip } from "@mui/material";
import React from "react";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { FormColumnProps } from "@/components/DynamicForm";
// import { buttonGroupProps } from "../components/common";
export interface ColumnProps {
  dataKey: string;
  label: string;
  sortable?: boolean;
  width?: number;
  ellipsis?: boolean;
  disablePadding?: boolean;
  align?: "left" | "right" | "center";
  render?: (row: anyValueProps) => React.ReactNode;
}
const StatusDot = styled("span", {
  shouldForwardProp: (prop) => prop !== "color",
})<{ color?: string }>(({ color }) => ({
  display: "inline-block",
  width: 6,
  height: 6,
  marginRight: 5,
  borderRadius: "50%",
  marginLeft: 8,
  background: color || "none",
}));

const TooltipTitleMain = styled("div")(() => ({
  maxWidth: 500,
  maxHeight: 100,
  overflow: "auto",
  paddingRight: 6,
}));

const TitleTypography = styled(Typography)(() => ({
  width: "100%",
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
}));

const ErrorIcon = styled(ErrorOutlineIcon, {
  shouldForwardProp: (props) => props !== "statusColor",
})<{ statusColor: string | undefined }>(({ statusColor }) => ({
  fontSize: "20px",
  color: statusColor || "red",
  marginLeft: "5px",
}));
const StatusBox: React.FC<{ status: number }> = ({ status }) => {
  const statusInfo = parseStatusList.find((el) => el.value === status);
  return (
    <Box
      sx={{ display: "flex", alignItems: "center", justifyContent: "center" }}
    >
      <StatusDot color={statusInfo?.color} />
      <Typography>{statusInfo?.label || status}</Typography>
    </Box>
  );
};

const TooltipTitle = (props: { title: string }) => {
  const { title } = props;
  return (
    <React.Fragment>
      <TooltipTitleMain>
        <TitleTypography color="inherit" title={title}>
          {title}
        </TitleTypography>
      </TooltipTitleMain>
    </React.Fragment>
  );
};

const StatusLabel = styled("div")(() => ({
  display: "flex",
}));
const parseStatusList = [
  {
    value: 0,
    label: "未解析",
    color: "grey",
  },
  {
    value: 1000,
    label: "排队中",
    color: "orange",
  },
  {
    value: 2000,
    label: "解析中",
    color: "#409eff",
  },
  {
    value: 3000,
    label: "全部成功",
    color: "green",
  },
  {
    value: 4000,
    label: "部分成功",
    color: "skyblue",
  },
  {
    value: 5000,
    label: "全部失败",
    color: "red",
  },
];
export const columns: ColumnProps[] = [
  {
    dataKey: "title",
    label: "任务名称",
    width: 150,
    ellipsis: true,
    disablePadding: true,
  },
  {
    dataKey: "authors",
    label: "物理编号",
    width: 150,
    ellipsis: true,
    render: (row: anyValueProps) => {
      const authors = row.authors ? JSON.parse(row.authors)?.join(",") : "";
      return <span title={authors}>{authors}</span>;
    },
  },
  {
    dataKey: "journal",
    label: "实验编号",
    align: "center",
    width: 100,
    ellipsis: true,
  },
  { dataKey: "publishedYear", label: "创建时间", align: "center", width: 80 },
  { dataKey: "category", label: "实验时间", width: 100 },
  {
    dataKey: "createTime",
    label: "实验人员",
    align: "center",
    width: 200,
    // sortable: true,
  },

  {
    dataKey: "parseStatusCode",
    label: "状态",
    align: "center",
    width: 120,
    render: (row: anyValueProps) => {
      const status = row.parseStatusCode;
      const statusInfo = parseStatusList.find((el: any) => el.value === status);

      return (
        <StatusLabel>
          <StatusBox status={status} />
          {(status === 4000 || status === 5000) && (
            <Tooltip
              placement="top"
              arrow
              title={<TooltipTitle title={row.message} />}
            >
              <ErrorIcon statusColor={statusInfo?.color} />
            </Tooltip>
          )}
        </StatusLabel>
      );
    },
  },
  {
    dataKey: "doi",
    label: "解析耗时",
    align: "center",
    width: 100,
    ellipsis: true,
  },
  {
    dataKey: "sources",
    label: "来源",
    align: "center",
    width: 100,
  },
];

export const searchColumns: FormColumnProps[] = [
  {
    name: "range_value",
    label: "实验时间",
    componentType: "date",
    grid: 3.5,
    required: false,
  },
  {
    name: "kb_name",
    label: "模糊查询",
    componentType: "input",
    grid: 2.5,
    required: false,
    placeholder: "请输入关键词",
  },
  {
    name: "source",
    label: "来源",
    componentType: "select",
    grid: 2,
    defaultValue: 0,
    options: [
      { label: "全部", value: 0 },
      { label: "我的收藏", value: 1 },
      { label: "个人上传", value: 2 },
      { label: "资料抽取", value: 3 },
    ],
  },
  {
    name: "status",
    label: "状态",
    componentType: "select",
    grid: 2,
    defaultValue: 0,
    options: [
      { label: "全部", value: 0 },
      { label: "已完成", value: 1 },
      { label: "进行中", value: 2 },
      { label: "取消", value: 3 },
      { label: "失败", value: 4 },
    ],
  },
];
