import Breadcrumb from "@/components/Breadcrumb";
import { useLocation } from "react-router-dom";
import MyPaperPagination from "@/components/MyPaperPagination";
const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
}));

const OperationBar = styled("div")(() => ({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  minHeight: 0,
  padding: "0 10%",
}));
const HeadTitle = styled("div")(() => ({
  lineHeight: "54px",
  height: "54px",
  opacity: 1,
  borderRadius: "16px",
  background: " rgba(255, 255, 255, 1)",
  margin: "18px 0",
  paddingLeft: "20px",
}));

const ContentBox = styled("div")(() => ({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  minHeight: 0,
  borderRadius: "20px",
  background:
    "radial-gradient(46.19% 59.91% at 17.11229946524064% -40.09433962264151%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  padding: "20px 20px",
}));

const InfoDetail = styled("div")(() => ({
  flex: 1,
  overflowY: "scroll",
}));
const Index: React.FC = () => {
  const location = useLocation();
  const { id, kb_name, kb_id } = location.state;
  const breadcrumbInfo = useMemo(
    () => ({
      current: `${kb_name}实验数据详情`,
      parent: id
        ? [
            { path: `/knowledge-base`, name: "实验数据库" },
            {
              path: "/knowledge-base/knowledge-detail",
              name: `${kb_name}实验数据库`,
              state: { kb_name, kb_id },
            },
          ]
        : [],
    }),
    [id],
  );
  const onChange = () => {};
  return (
    <Root>
      <Breadcrumb
        parent={breadcrumbInfo.parent}
        current={breadcrumbInfo.current}
      />

      <OperationBar>
        <HeadTitle>任务名称: {id}</HeadTitle>
        <ContentBox>
          <InfoDetail></InfoDetail>
          <MyPaperPagination
            total={50}
            page={1}
            onChangePage={onChange}
            pageSizeOptions={[10, 20, 50]}
          />
        </ContentBox>
      </OperationBar>
    </Root>
  );
};
export default Index;
