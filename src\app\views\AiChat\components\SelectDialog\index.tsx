import CustomDialog from "@/components/Dialog";
import KnowledgeBase from "@/views/KnowledgeBase/index";
import PaperBase from "@/views/PaperBase/index";
import DialogPaper from "../DialogPaper";
import { leftHistoryListProps, tabList } from "../common";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import { setIsFirst, setSelectedBank } from "@/store/counterSlice";
import { chatUpdate } from "@/api/chat";
interface Props {
  open: boolean;
  setOpen: (open: boolean) => void;
  isAddPaper?: boolean;
}
const ContentBox = styled("div")(() => ({
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
  height: "100%",
  width: "100%",
  padding: "0 20px",
}));
const TabsButton = styled("div")(() => ({
  display: "flex",
  marginTop: "10px",
  borderRadius: "10px",
  background: "rgba(255, 255, 255, 1)",
  height: 48,
  alignItems: "center",
  paddingLeft: "10px",
}));
const TabsItem = styled("div", {
  shouldForwardProp: (p) => p !== "active",
})<{
  active: boolean;
}>(({ active }) => ({
  lineHeight: "48px",
  textAlign: "center",
  cursor: "pointer",
  margin: "0 25px",
  color: active ? "rgba(24, 112, 199, 1)" : "rgba(0, 0, 0, 1)",
  position: "relative",
  "::before": active
    ? {
        content: "''",
        borderWidth: "3px",
        borderStyle: "solid",
        position: "absolute",
        top: "50%",
        transform: "translateY(-50%)",
        left: "-9px",
        borderColor:
          "transparent  transparent transparent  rgba(24, 112, 199, 1)",
      }
    : {},
  "::after": active
    ? {
        content: "''",
        borderWidth: "3px",
        borderStyle: "solid",
        position: "absolute",
        top: "50%",
        transform: "translateY(-50%)",
        right: "-9px",
        borderColor:
          "transparent  rgba(24, 112, 199, 1) transparent transparent ",
      }
    : {},
}));
const TabsContent = styled("div")(() => ({
  flex: 1,
}));
const Index: React.FC<Props> = ({ open, setOpen, isAddPaper }) => {
  const [activeType, setActiveType] = useState(1);
  const [checkedData, setCheckedData] = useState<
    leftHistoryListProps[] | number[]
  >();
  const { active, selectedBank, isFirst } = useAppSelector(
    (state: RootState) => state.counter,
  );
  const dispatch = useAppDispatch();
  // tabs切换
  const handleChange = (newValue: number) => {
    setActiveType(newValue);
  };
  // 确定
  const handleOk = async () => {
    if (!checkedData || (Array.isArray(checkedData) && !checkedData.length)) {
      message.warning("请选择");
      dispatch(setSelectedBank({}));
      return;
    }
    if (activeType === 2 && Array.isArray(checkedData) && !isAddPaper) {
      if (checkedData.length > 5) {
        message.warning("最多选择5篇资料");
      } else {
        dispatch(
          setSelectedBank({ externalIds: checkedData, bankType: activeType }),
        );
        handleCancel();
      }
    } else if (isAddPaper && Array.isArray(checkedData)) {
      const externalIds = [...selectedBank.externalIds, ...checkedData];
      const uniqueArray = externalIds.filter(
        (value, index, self) => self.indexOf(value) === index,
      );
      if (uniqueArray.length > 5) {
        const len = selectedBank.externalIds.length;
        message.warning(`当前存在${len}篇资料，最多选择${5 - len}篇资料`);
      } else {
        dispatch(setSelectedBank({ externalIds: uniqueArray, bankType: 2 }));
        await chatUpdate({
          chatId: active,
          externalIds: uniqueArray,
        });
        dispatch(setIsFirst(!isFirst));
        handleCancel();
      }
    } else {
      const nameJoin = checkedData.map((item: any) => item.name).join("+");
      const externalIds = checkedData.map((item: any) => item.id);
      dispatch(
        setSelectedBank({
          data: checkedData,
          bankType: activeType,
          name: nameJoin,
          ids: externalIds,
        }),
      );
      if (externalIds.length && active) {
        await chatUpdate({
          chatId: active,
          externalIds,
          type: activeType,
        });
      }
      handleCancel();
    }
  };
  // 取消
  const handleCancel = () => {
    setOpen(false);
    setActiveType(1);
  };

  return (
    <CustomDialog
      open={open}
      setDialogOpen={setOpen}
      title={isAddPaper ? "选择资料" : "选择对话类型"}
      okButtonProps={{ onOk: handleOk }}
      cancelButtonProps={{ onCancel: handleCancel }}
      btnIsCenter={true}
      width={"100%"}
      bgColor="rgba(247, 247, 247, 1)"
    >
      {isAddPaper ? (
        <ContentBox slot="content">
          <DialogPaper
            isDialog={true}
            setCheckedData={setCheckedData}
            isAddPaper={true}
          />
        </ContentBox>
      ) : (
        <ContentBox slot="content">
          <TabsButton>
            {tabList.map((item) => (
              <TabsItem
                key={item.key}
                onClick={() => handleChange(item.key)}
                active={item.key === activeType}
              >
                {item.name}
              </TabsItem>
            ))}
          </TabsButton>
          <TabsContent>
            {activeType === 1 ? (
              <PaperBase isDialog={true} setCheckedData={setCheckedData} />
            ) : activeType === 3 ? (
              <KnowledgeBase isDialog={true} setCheckedData={setCheckedData} />
            ) : (
              <DialogPaper
                isDialog={true}
                setCheckedData={setCheckedData}
                isAddPaper={false}
              />
            )}
          </TabsContent>
        </ContentBox>
      )}
    </CustomDialog>
  );
};
export default Index;
