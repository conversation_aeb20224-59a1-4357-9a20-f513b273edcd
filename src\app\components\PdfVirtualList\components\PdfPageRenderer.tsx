import React, { useState, useRef, useEffect } from "react";
import { Page } from "react-pdf";
import { PageLayout } from "../utils/pdfUtils";
import { FormattedAnnotation, Operation } from "../types/types";
import { Button, styled } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

interface PdfPageRendererProps {
  visibleRange: { start: number; end: number };
  numPages: number;
  pageLayouts: PageLayout[];
  containerWidth: number;
  scale: number;
  updatePageHeight: (index: number, height: number) => void;
  updatePageDimensions: (
    pageIndex: number,
    width: number,
    height: number,
  ) => void;
  handlePageLoadSuccess?: (index: number, page: any) => void;
  annotations: FormattedAnnotation[][];
  operation?: Operation[];
  getParagraph?: (paragraph: string, type: string) => void;
}

const ButtonBox = styled("div")<{ position: { x: number; y: number } }>(
  ({ position }) => ({
    position: "absolute",
    left: position.x,
    top: position.y,
    padding: "4px",
    display: "flex",
    alignItems: "center",
    gap: "8px",
    zIndex: 1100,
    pointerEvents: "auto",
    transform: "translate(0, 0)",
    background: "#fff",
    boxShadow: "2px 4px 9px 0 rgba(25, 45, 79, .5)",
    borderRadius: "20px",
  }),
);

const ButtonItem = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const OperationButton = styled(Button)(() => ({
  borderRadius: "20px",
  outline: "none",
  border: "none",
  boxShadow: "none",
  lineHeight: "36px",
  padding: "0 14px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: "14px",
  fontWeight: "400",
  color: "#192d4f",
  ":disabled": {
    cursor: " not-allowed",
    pointerEvents: "auto",
  },
}));

const ButtonLine = styled("span")(() => ({
  height: "12px",
  display: "block",
  borderLeft: "1px solid #e8e8e8",
}));

const CloseButton = styled("div")(({ theme }) => ({
  marginLeft: theme.spacing(0.7),
  cursor: "pointer",
  fontSize: "16px",
  lineHeight: "16px",
}));

const AnnotationToolbar: React.FC<{
  position: { x: number; y: number } | undefined;
  onAdd: (text: string, type: string) => void;
  onClose: () => void;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  operation?: Operation[];
  annotationText: string;
}> = ({
  position,
  onAdd,
  onClose,
  onMouseEnter,
  onMouseLeave,
  operation,
  annotationText,
}) => {
  // 如果没有有效的位置，不显示工具栏
  if (!position) return null;
  const submit = (type: string) => {
    onAdd(annotationText, type);
  };
  return (
    <ButtonBox
      position={position}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {operation &&
        operation.length > 0 &&
        operation.map((item) => (
          <ButtonItem key={item.type}>
            <OperationButton
              onClick={item.disable ? () => {} : () => submit(item.type)}
              disabled={item.disable}
            >
              {item.label}
            </OperationButton>
            <ButtonLine />
          </ButtonItem>
        ))}
      <CloseButton sx={{ marginRight: "10px" }} onClick={onClose}>
        <CloseIcon style={{ fontSize: "16px" }} />
      </CloseButton>
    </ButtonBox>
  );
};

// 添加防抖函数
// ... 添加泛型类型定义
const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number) => {
  let timer: ReturnType<typeof setTimeout> | null = null;
  return function (...args: Parameters<T>): ReturnType<T> | void {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      // 使用展开运算符替代.apply()
      fn(...args);
      timer = null;
    }, delay);
  };
};

const PdfPageRenderer: React.FC<PdfPageRendererProps> = ({
  visibleRange,
  numPages,
  pageLayouts,
  containerWidth,
  scale,
  updatePageHeight,
  updatePageDimensions,
  handlePageLoadSuccess,
  annotations,
  operation,
  getParagraph,
}) => {
  // window.console.log("annotations", annotations);
  const [hoveredAnnotation, setHoveredAnnotation] = useState<{
    pageIndex: number;
    annotation: FormattedAnnotation;
  } | null>(null);
  const [isToolbarHovered, setIsToolbarHovered] = useState(false);
  const pageDimensionsRef = useRef<
    Record<number, { width: number; height: number }>
  >({});

  // 添加文本选择相关状态
  const [selectedText, setSelectedText] = useState<string>("");
  const [selectionPosition, setSelectionPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const [currentSelectionPage, setCurrentSelectionPage] = useState<
    number | null
  >(null);
  const [isTextSelected, setIsTextSelected] = useState(false);
  const selectionRangeRef = useRef<Range | null>(null);

  // 保存添加过监听器的元素引用
  const elementsWithListenersRef = useRef<
    { element: Element; pageIndex: number; handler: (e: Event) => void }[]
  >([]);

  // 添加全局selectionchange事件监听器
  useEffect(() => {
    const handleGlobalSelectionChange = debounce(() => {
      const selection = window.getSelection();
      if (!annotations.length) return;

      if (
        selection &&
        selection.rangeCount > 0 &&
        selection.toString().trim()
      ) {
        // 找到选择所在的页面
        const range = selection.getRangeAt(0);
        const container = range.commonAncestorContainer;

        // 向上查找包含页面信息的元素
        let pageElement =
          container.nodeType === Node.TEXT_NODE
            ? container.parentElement
            : (container as Element);

        while (pageElement && !pageElement.hasAttribute("data-page-number")) {
          pageElement = pageElement.parentElement;
        }

        if (pageElement) {
          const pageNumber = pageElement.getAttribute("data-page-number");
          if (pageNumber) {
            const pageIndex = parseInt(pageNumber) - 1;
            // 调用文本选择处理函数
            handleTextSelection(new Event("selectionchange"), pageIndex);
          }
        }
      } else {
        // 没有选择文本时，清除选择状态（但不在hover工具栏时）
        if (!isToolbarHovered && isTextSelected) {
          setSelectedText("");
          setSelectionPosition(null);
          setIsTextSelected(false);
          setCurrentSelectionPage(null);
          selectionRangeRef.current = null;
        }
      }
    }, 100); // 100ms防抖

    // 绑定全局selectionchange事件
    document.addEventListener("selectionchange", handleGlobalSelectionChange);

    return () => {
      document.removeEventListener(
        "selectionchange",
        handleGlobalSelectionChange,
      );
    };
  }, [annotations.length, isToolbarHovered, isTextSelected]); // 添加更多依赖项

  // 在组件卸载时清理所有文本选择事件监听器
  useEffect(
    () => () => {
      // 清理所有已添加的mouseup事件监听器
      elementsWithListenersRef.current.forEach(({ element, handler }) => {
        element.removeEventListener("mouseup", handler);
      });
    },
    [],
  );

  const pageLoadSuccess = (pageIndex: number, page: any) => {
    if (handlePageLoadSuccess) {
      handlePageLoadSuccess(pageIndex, page);
    } else {
      // 使用 setTimeout 确保在页面实际渲染后获取尺寸
      setTimeout(() => {
        const pageElement = document.querySelector(
          `[data-page-number="${pageIndex + 1}"]`,
        );

        // 添加文本选择事件监听
        if (pageElement) {
          const textLayer = pageElement.querySelector(
            ".react-pdf__Page__textContent",
          );
          if (textLayer) {
            // 使用命名函数而不是匿名函数，便于后续移除
            const handleTextSelectionForPage = (e: Event) =>
              handleTextSelection(e, pageIndex);
            textLayer.addEventListener("mouseup", handleTextSelectionForPage);

            // 将元素添加到引用数组中，以便后续清理
            elementsWithListenersRef.current.push({
              element: textLayer,
              pageIndex,
              handler: handleTextSelectionForPage,
            });
          }
        }

        if (!pageElement) {
          console.warn(`找不到页面 ${pageIndex + 1} 元素`);
          return;
        }

        const rect = pageElement.getBoundingClientRect();
        const actualWidth = rect.width;
        const actualHeight = rect.height;

        if (actualWidth > 0 && actualHeight > 0) {
          updatePageDimensions(pageIndex, actualWidth, actualHeight);
          // 同步更新pageDimensionsRef
          pageDimensionsRef.current = {
            ...pageDimensionsRef.current,
            [pageIndex]: { width: actualWidth, height: actualHeight },
          };
          updatePageHeight(pageIndex, actualHeight);

          // 记录页面容器的尺寸，用于调试
          // const containerElement = document.querySelector(
          //   `[data-page-container="page-container-${pageIndex + 1}"]`,
          // );
          // if (containerElement) {
          //   const containerRect = containerElement.getBoundingClientRect();
          // }
        } else {
          // 如果无法获取实际尺寸，则使用原始尺寸
          if (
            page &&
            typeof page.height === "number" &&
            typeof page.width === "number"
          ) {
            const scaledWidth = page.width * scale;
            const scaledHeight = page.height * scale;

            updatePageDimensions(pageIndex, scaledWidth, scaledHeight);
            // 同步更新pageDimensionsRef
            pageDimensionsRef.current = {
              ...pageDimensionsRef.current,
              [pageIndex]: { width: scaledWidth, height: scaledHeight },
            };
            updatePageHeight(pageIndex, scaledHeight);
          }
        }
      }, 0);
    }
  };

  // 修改 resize 监听
  useEffect(() => {
    const updatePageSizes = () => {
      // 重新获取所有可见页面的尺寸
      for (
        let i = visibleRange.start;
        i <= visibleRange.end && i < numPages;
        i++
      ) {
        const pageElement = document.querySelector(
          `[data-page-number="${i + 1}"]`,
        );
        if (pageElement) {
          const rect = pageElement.getBoundingClientRect();
          updatePageDimensions(i, rect.width, rect.height);
          // 同步更新pageDimensionsRef
          pageDimensionsRef.current = {
            ...pageDimensionsRef.current,
            [i]: { width: rect.width, height: rect.height },
          };
          updatePageHeight(i, rect.height);
        }
      }
    };

    // 使用防抖函数包装更新函数，设置300ms的延迟
    const debouncedUpdate = debounce(updatePageSizes, 300);

    window.addEventListener("resize", debouncedUpdate);
    return () => {
      window.removeEventListener("resize", debouncedUpdate);
    };
  }, [visibleRange, numPages, updatePageDimensions, updatePageHeight]);

  // 添加滚动事件监听
  useEffect(() => {
    // 防抖处理滚动事件
    const handleScroll = debounce(() => {
      // 如果有文本选中且有保存的选区，更新操作栏位置
      if (
        isTextSelected &&
        selectionRangeRef.current &&
        currentSelectionPage !== null
      ) {
        const range = selectionRangeRef.current;
        const rect = range.getBoundingClientRect();

        // 检查选中的文本是否还在可视区域内
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        // 判断选中文本是否在可视区域内
        const isInViewport =
          rect.top >= 0 &&
          rect.left >= 0 &&
          rect.bottom <= viewportHeight &&
          rect.right <= viewportWidth &&
          rect.width > 0 &&
          rect.height > 0;

        if (isInViewport) {
          // 如果在可视区域内，更新操作栏位置
          const x = rect.left + rect.width / 2 - 100;
          const y = rect.bottom + 10;
          setSelectionPosition({ x, y });
        } else {
          // 如果离开可视区域，关闭操作栏（但不在hover工具栏时）
          if (!isToolbarHovered) {
            setSelectedText("");
            setSelectionPosition(null);
            setIsTextSelected(false);
            setCurrentSelectionPage(null);
            selectionRangeRef.current = null;
          }
        }
      }
    }, 10); // 使用10ms的防抖延迟

    // 获取container元素并添加滚动监听
    // 使用更可靠的方式找到滚动容器
    const findScrollContainer = () => {
      // 尝试使用containerRef
      const pdfContainer = document.querySelector(
        ".react-pdf__Document",
      )?.parentElement;
      if (
        pdfContainer &&
        pdfContainer.scrollHeight > pdfContainer.clientHeight
      ) {
        return pdfContainer;
      }

      // 备选方案：通过递归向上查找可滚动的父元素
      const isScrollable = (element: HTMLElement) => {
        const style = window.getComputedStyle(element);
        const overflowY = style.getPropertyValue("overflow-y");
        return (
          (overflowY === "auto" || overflowY === "scroll") &&
          element.scrollHeight > element.clientHeight
        );
      };

      let container = document.querySelector("[data-page-container]");
      while (container && container.parentElement) {
        container = container.parentElement;
        if (container instanceof HTMLElement && isScrollable(container)) {
          return container;
        }
      }

      // 最后的备选方案：使用document作为滚动容器
      return document;
    };

    const scrollContainer = findScrollContainer();
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", handleScroll);

      // 也监听窗口大小变化，因为这也会影响位置
      window.addEventListener("resize", handleScroll);
    }

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener("scroll", handleScroll);
      }
      window.removeEventListener("resize", handleScroll);
    };
  }, [isTextSelected, currentSelectionPage]);

  const handleTextSelection = (e: Event, pageIndex: number) => {
    const selection = window.getSelection();

    if (!annotations.length) return;
    if (
      !selection ||
      selection.rangeCount === 0 ||
      selection.toString().trim() === ""
    ) {
      // 如果没有选中文本，不显示操作栏
      if (!isToolbarHovered) {
        setSelectedText("");
        setSelectionPosition(null);
        setIsTextSelected(false);
        setCurrentSelectionPage(null);
        selectionRangeRef.current = null;
      }
      return;
    }

    const text = selection.toString().trim();

    if (text) {
      setSelectedText(text);
      setCurrentSelectionPage(pageIndex);
      setIsTextSelected(true);

      const range = selection.getRangeAt(0);
      // 保存选区引用，以便在滚动时更新位置
      selectionRangeRef.current = range;
      const rect = range.getBoundingClientRect();

      // 计算操作栏位置，位于选中文本的下方
      const pageElement = document.querySelector(
        `[data-page-number="${pageIndex + 1}"]`,
      );
      if (pageElement) {
        // const pageRect = pageElement.getBoundingClientRect();

        // 计算相对于视口的位置，水平居中，垂直在文本下方
        const x = rect.left + rect.width / 2 - 100; // 水平居中的偏移
        const y = rect.bottom + 10; // 文本下方10px的位置

        setSelectionPosition({ x, y });
      }
    }
  };

  // 处理页面点击事件，清除选中状态
  const handlePageClick = (e: React.MouseEvent) => {
    // 如果点击的不是文本层，则清除选中状态
    const target = e.target as HTMLElement;
    if (!target.closest(".react-pdf__Page__textContent") && !isToolbarHovered) {
      setSelectedText("");
      setSelectionPosition(null);
      setIsTextSelected(false);
      setCurrentSelectionPage(null);
      selectionRangeRef.current = null;
    }
  };

  const pageMouseMove = (
    e: React.MouseEvent<HTMLDivElement>,
    pageIndex: number,
  ) => {
    // 如果操作栏正在被hover，不处理鼠标移动事件
    if (isToolbarHovered) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 检查鼠标是否在某个标注区域内
    const annotation = annotations[pageIndex]?.find(
      (ann) => x >= ann.x[0] && x <= ann.x[1] && y >= ann.y[0] && y <= ann.y[1],
    );

    if (annotation) {
      // 只有当鼠标移动到新的标注区域时才更新
      if (
        hoveredAnnotation?.pageIndex !== pageIndex ||
        hoveredAnnotation?.annotation.id !== annotation.id
      ) {
        setHoveredAnnotation({ pageIndex, annotation });
      }
    } else if (!isToolbarHovered) {
      // 只有在不是hover在操作栏上时，才清除hoveredAnnotation
      setHoveredAnnotation(null);
    }
  };

  const handleAddOperation = (text: string, type: string) => {
    getParagraph && getParagraph(text, type);
  };

  const handleToolbarMouseEnter = () => {
    setIsToolbarHovered(true);
  };

  const handleToolbarMouseLeave = () => {
    setIsToolbarHovered(false);
  };

  const handleClose = () => {
    setHoveredAnnotation(null);
    setSelectedText("");
    setSelectionPosition(null);
    setIsTextSelected(false);
    setCurrentSelectionPage(null);
    selectionRangeRef.current = null;
    setIsToolbarHovered(false);
  };

  const calculateToolbarPosition = (annotation: FormattedAnnotation) => {
    if (!hoveredAnnotation) return;

    // 获取当前页面的尺寸
    const currentPageDimensions =
      pageDimensionsRef.current[hoveredAnnotation.pageIndex];

    if (!currentPageDimensions) return;

    // 检查原始尺寸是否存在
    if (!annotation.originalWidth || !annotation.originalHeight) {
      console.warn("标注缺少原始尺寸信息");
      return;
    }

    // 计算缩放比例
    const scaleX = currentPageDimensions.width / annotation.originalWidth;
    const scaleY = currentPageDimensions.height / annotation.originalHeight;

    // 获取页面元素
    const pageElement = document.querySelector(
      `[data-page-number="${hoveredAnnotation.pageIndex + 1}"]`,
    );
    if (!pageElement) return;

    // 获取页面元素的位置信息
    const pageRect = pageElement.getBoundingClientRect();

    // 计算相对于视口的位置
    const x = pageRect.left + annotation.x[1] * scaleX - 200; // 距离右边界padding距离
    const y = pageRect.top + annotation.y[1] * scaleY - 10; // 距离下边界padding距离

    return { x, y };
  };

  // 添加监听全局selection change事件
  useEffect(() => {
    const handleSelectionChange = () => {
      // 只有在已有选择的情况下才检查
      if (isTextSelected && !isToolbarHovered) {
        const selection = window.getSelection();
        // 如果没有选中内容或选区为空，关闭工具栏
        if (
          !selection ||
          selection.rangeCount === 0 ||
          selection.toString().trim() === ""
        ) {
          setSelectedText("");
          setSelectionPosition(null);
          setIsTextSelected(false);
          setCurrentSelectionPage(null);
          selectionRangeRef.current = null;
        }
      }
    };

    document.addEventListener("selectionchange", handleSelectionChange);
    return () => {
      document.removeEventListener("selectionchange", handleSelectionChange);
    };
  }, [isTextSelected, isToolbarHovered]);

  const visiblePages = [];
  for (let i = visibleRange.start; i <= visibleRange.end && i < numPages; i++) {
    const pageIndex = i;

    visiblePages.push(
      <div
        key={`page-${pageIndex + 1}`}
        style={{
          position: "absolute",
          top: pageLayouts[pageIndex]?.y || 0,
          left: 0,
          width: "100%",
          display: "flex",
          justifyContent: "center",
          padding: "0 5px",
          boxSizing: "border-box",
          overflowX: "visible",
          overflowY: "visible",
          borderRadius: "10px",
          pointerEvents: "auto",
          zIndex: 1,
          WebkitOverflowScrolling: "touch",
        }}
        data-page-container={`page-container-${pageIndex + 1}`}
        onClick={handlePageClick}
      >
        <div
          style={{
            position: "relative",
            width: scale > 1 ? `${containerWidth * scale}px` : "100%",
            maxWidth:
              scale > 1 ? `${containerWidth * scale}px` : `${containerWidth}px`,
            overflow: "visible",
            backgroundColor: "#ffffff",
            pointerEvents: "auto",
          }}
          onMouseMove={(e) => pageMouseMove(e, pageIndex)}
          data-page-wrapper={`page-wrapper-${pageIndex + 1}`}
        >
          <div
            style={{
              overflow: "visible",
              width: "100%",
              maxWidth: scale > 1 ? `${containerWidth * scale}px` : "100%",
              display: "flex",
              justifyContent: "center",
              // marginBottom: '5px',
            }}
          >
            <Page
              pageNumber={pageIndex + 1}
              width={containerWidth > 0 ? containerWidth : undefined}
              scale={scale}
              renderTextLayer={true}
              renderAnnotationLayer={false}
              onLoadSuccess={(page) => pageLoadSuccess(pageIndex, page)}
              onRenderError={(error) => {
                console.error(`Page ${pageIndex + 1} render error:`, error);
              }}
              data-page-index={pageIndex}
              loading=""
            />
          </div>
          {/* 渲染标注 */}
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              pointerEvents: "none",
              overflow: scale > 1 ? "visible" : "hidden",
            }}
          >
            {annotations[pageIndex]?.map((annotation, index) => {
              const isHovered =
                // hoveredAnnotation?.pageIndex === pageIndex &&
                hoveredAnnotation?.annotation.id === annotation.id;
              return (
                <React.Fragment key={`${annotation.id}-${index}`}>
                  <div
                    style={{
                      position: "absolute",
                      left: annotation.x[0],
                      top: annotation.y[0],
                      width: annotation.x[1] - annotation.x[0],
                      height: annotation.y[1] - annotation.y[0],
                      backgroundColor: isHovered
                        ? "rgba(187, 214, 251, 0.3)"
                        : "transparent",
                      border: isHovered
                        ? "1px solid rgba(187, 214, 251, 0.5)"
                        : "none",
                      transition: "all 0.2s ease-in-out",
                      pointerEvents: "none",
                      zIndex: 1000,
                    }}
                  />
                </React.Fragment>
              );
            })}
          </div>
        </div>
      </div>,
    );
  }

  return (
    <>
      {visiblePages}
      {/* 将操作栏移到外层，避免被页面内的事件影响 */}
      {!isTextSelected && hoveredAnnotation && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
            zIndex: 1100,
            overflow: "hidden",
          }}
        >
          <AnnotationToolbar
            position={
              calculateToolbarPosition(hoveredAnnotation.annotation) ||
              undefined
            }
            onAdd={handleAddOperation}
            onClose={handleClose}
            onMouseEnter={handleToolbarMouseEnter}
            onMouseLeave={handleToolbarMouseLeave}
            operation={operation}
            annotationText={hoveredAnnotation.annotation.text}
          />
        </div>
      )}

      {/* 文本选择的操作栏 */}
      {isTextSelected && selectionPosition && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            pointerEvents: "none",
            zIndex: 1100,
            overflow: "hidden",
          }}
        >
          <AnnotationToolbar
            position={selectionPosition}
            onAdd={handleAddOperation}
            onClose={handleClose}
            onMouseEnter={handleToolbarMouseEnter}
            onMouseLeave={handleToolbarMouseLeave}
            operation={operation}
            annotationText={selectedText}
          />
        </div>
      )}
    </>
  );
};

export default PdfPageRenderer;
