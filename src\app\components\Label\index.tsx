import React from "react";
import { styled } from "@mui/material/styles";

const StyledLabel = styled("div")<{
  labelwidth?: number;
  align?: "right" | "left" | "center";
  required?: 1 | 0;
}>(({ labelwidth, align, theme, required }) => ({
  whiteSpace: "nowrap",
  width: labelwidth,
  textAlign: align ?? "right",
  paddingRight: theme.spacing(),
  quotes: required ? "'*' '*' '*' '*'" : "",
  "&::before": required
    ? {
        content: `open-quote`,
        color: "red",
      }
    : {},
}));

interface FormLabelProps {
  labelwidth?: number;
  label: any;
  labelStuff?: string;
  labelAlign?: "right" | "left" | "center";
  required?: boolean;
}

const FormLabel: React.FC<FormLabelProps> = ({
  labelwidth,
  label,
  labelAlign = "right",
  required,
}) => (
  <StyledLabel
    labelwidth={labelwidth}
    align={labelAlign}
    required={required ? 1 : 0}
  >
    {label}
  </StyledLabel>
);
export default FormLabel;
