import { pdfjs } from "react-pdf";

// 设置PDF worker路径 - 支持多种环境配置
const getWorkerSrc = (): string => {
  // 1. 优先使用全局配置的BASE_PATH
  if (typeof window !== "undefined" && (window as any).APP_CONFIG?.BASE_PATH) {
    return `${(window as any).APP_CONFIG.BASE_PATH}/pdf.worker.min.js`;
  }

  // 2. 检查是否在开发环境
  if (process.env.NODE_ENV === "development") {
    return "/pdf.worker.min.js";
  }

  // 3. 尝试从当前路径推断base path
  if (typeof window !== "undefined") {
    const currentPath = window.location.pathname;
    if (currentPath.includes("/aihub-chat")) {
      return "/aihub-chat/pdf.worker.min.js";
    }
  }

  // 4. 默认路径
  return "/pdf.worker.min.js";
};

// 初始化PDF worker
const initializePdfWorker = (): void => {
  try {
    const workerSrc = getWorkerSrc();
    pdfjs.GlobalWorkerOptions.workerSrc = workerSrc;

    // 开发环境下输出调试信息
    if (process.env.NODE_ENV === "development") {
      console.log(
        `[PdfVirtualList] PDF Worker initialized with path: ${workerSrc}`,
      );
    }
  } catch (error) {
    console.error("[PdfVirtualList] Failed to initialize PDF worker:", error);

    // 降级方案：使用CDN worker
    try {
      pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;
      console.warn("[PdfVirtualList] Fallback to CDN worker");
    } catch (fallbackError) {
      console.error(
        "[PdfVirtualList] CDN fallback also failed:",
        fallbackError,
      );
    }
  }
};

// 验证worker是否可用
export const validateWorker = async (): Promise<boolean> => {
  try {
    const workerSrc = pdfjs.GlobalWorkerOptions.workerSrc;
    if (!workerSrc) {
      throw new Error("Worker source not set");
    }

    // 尝试加载worker文件
    const response = await fetch(workerSrc);
    if (!response.ok) {
      throw new Error(`Worker file not found: ${response.status}`);
    }

    return true;
  } catch (error) {
    console.error("[PdfVirtualList] Worker validation failed:", error);
    return false;
  }
};

// 初始化worker
initializePdfWorker();

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): (...args: Parameters<T>) => ReturnType<T> | void {
  let timeout: ReturnType<typeof setTimeout>;
  return function executedFunction(...args: Parameters<T>): void {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 页面布局接口
export interface PageLayout {
  y: number;
  height: number;
}

// PDF虚拟列表属性接口
export interface PDFVirtualListProps {
  url: string;
  initialVisiblePages?: number;
  coordsData?: any[];
}
