import { createSlice } from "@reduxjs/toolkit";

// Redux state 结构
interface BreadcrumbState {
  minScore: number;
  topK: number;
}

// 初始状态
const initialState: BreadcrumbState = {
  minScore: 0.5,
  topK: 20,
};

// 创建 slice
const searchSlice = createSlice({
  name: "search",
  initialState,
  reducers: {
    setRelevances: (state, action) => {
      state.minScore = action.payload;
    },
    setPaperNumber: (state, action) => {
      state.topK = action.payload;
    },
  },
});

// 导出 actions
export const { setRelevances, setPaperNumber } = searchSlice.actions;

// 导出 reducer
export default searchSlice.reducer;
