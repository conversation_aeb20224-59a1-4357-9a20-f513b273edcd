import { RouteProps } from "..";
import knowledge from "@/assets/database.svg";
import KnowledgeBase from "@/views/KnowledgeBase";
import KnowledgeTable from "@/views/KnowledgeBase/KnowledgeTable";
import DataBaseDetails from "@/views/KnowledgeBase/DataDetails";
export const knowledgeRoute: Array<RouteProps> = [
  {
    path: "/knowledge-base",
    name: "knowledge-base",
    description: `实验数据库`,
    lazyComponent: () => import("../../views/KnowledgeBase"),
    components: KnowledgeBase,
    hidden: false,
    icon: knowledge,
    menuCode: "ExpDB",
    children: [
      {
        path: "/knowledge-base/knowledge-detail",
        name: "knowledge-detail",
        description: "数据库详情",
        hidden: true,
        lazyComponent: () => import("@/views/KnowledgeBase/KnowledgeTable"),
        components: KnowledgeTable,
      },
      {
        path: "/knowledge-base/data-details",
        name: "knowledge-detail",
        description: "数据详情",
        hidden: true,
        lazyComponent: () => import("@/views/KnowledgeBase/DataDetails"),
        components: DataBaseDetails,
      },
    ],
  },
];
