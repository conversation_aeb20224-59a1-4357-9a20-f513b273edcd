import CustomDialog from "@/components/Dialog";
import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { UploadColumns, sleep } from "./setting";
import {
  FormControl,
  FormHelperText,
  FormLabel,
  InputBase,
  MenuItem,
  Select,
} from "@mui/material";
import { getPaperBase, uploadPdfFile } from "@/api/personalpaper";
import { useAppSelector } from "@/hooks";
import { checkPermission } from "@/utils/auth";
import { anyValueProps } from "@/types/common";
import { queryGroupList } from "@/api/knowledgeBase";
import { LoadingButton } from "@mui/lab";
import { debounce } from "lodash";
interface Props {
  open: boolean;
  setOpen: (value: boolean) => void;
  reload?: () => void;
  rowOption?: any;
  sliceSize?: number;
  documentId?: string;
  documentName?: string;
  uploadGroupCode?: string;
  uploadGroupName?: string;
}
const ContentBox = styled("div")(() => ({
  width: "550px",
  padding: "10px 10px 0 10px",
  boxSizing: "border-box",
  position: "relative",
}));

const FormBox = styled("div")(() => ({
  width: "100%",
}));

const ProgressLabel = styled("div")(() => ({
  width: "100%",
  marginTop: "10px",
  fontSize: "14px",
}));

const BootstrapInput = styled(InputBase, {
  shouldForwardProp: (props) => props !== "errorFlag",
})<{ errorFlag: string }>(({ theme, errorFlag }) => ({
  "label + &": {
    marginTop: theme.spacing(3),
  },
  "& .MuiInputBase-input": {
    // height: "30px",
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.background.paper,
    border: "1px solid #ced4da",
    borderColor: errorFlag ? "#d32f2f" : "#ced4da",
    fontSize: 16,
    // padding: "10px 26px 10px 12px",
    padding: "5px 0 5px 12px",
    transition: theme.transitions.create(["border-color", "box-shadow"]),
    boxSize: "border-box",
    // Use the system font instead of the default Roboto font.
    fontFamily: [
      "-apple-system",
      "BlinkMacSystemFont",
      '"Segoe UI"',
      "Roboto",
      '"Helvetica Neue"',
      "Arial",
      "sans-serif",
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(","),
    "&:focus": {
      borderRadius: 4,
      borderColor: "#4248B5",
      boxShadow: "0 0 0 0.2rem rgba(64,72,181,.25)",
    },
  },
}));

const BaseSelect = styled("div")(() => ({
  width: "calc(100% - 70px)",
}));

const StyledSelect = styled(Select)(() => ({
  "& .MuiSelect-select": {
    borderRadius: 28,
  },
  "&.MuiInputBase-root": {
    borderRadius: 28,
  },
  "& #demo-customized-select": {
    // height: 10,
    padding: "6.5px 10px",
    borderRadius: 28,
    boxSize: "border-box",
  },
}));

const FooterBox = styled("div")(() => ({
  width: "100%",
  height: 74,
  display: "flex",
  justifyContent: "flex-end",
  alignItems: "center",
  gap: 10,
}));

const StyleButton = styled(Button)(() => ({
  width: 64,
  height: 32,
  borderRadius: 28,
}));

const StyleLoadingButton = styled(LoadingButton)(() => ({
  width: 64,
  height: 32,
  borderRadius: 28,
}));

const UploadDialog: React.FC<Props> = ({
  open,
  setOpen,
  reload,
  sliceSize = 10,
  documentId,
  documentName,
  uploadGroupCode,
  uploadGroupName,
}) => {
  const formRef = useRef<RefProps>(null);
  const [list, setList] = useState<any[]>([]);
  const { roleOption } = useAppSelector((state) => state.user);
  const [groupList, setGroupList] = useState<any[]>([]);
  // const [checked, setChecked] = useState(false);
  const [loading, setLoading] = useState(false);
  const [progressNum, setProgressNum] = useState<number>(0);
  const [filesLength, setFilesLength] = useState<number>(0);
  const [errorText, setErrorText] = useState<string>("");
  const [groupErrorText, setGroupErrorText] = useState<string>("");
  const [baseSelect, setBaseSelect] = useState(documentId || "");
  const [groupCode, setGroupCode] = useState(uploadGroupCode || "");
  const [lastUploadedIndex, setLastUploadedIndex] = useState<number | null>(
    null,
  );
  const [pagination, setPagination] = useState({
    page: 1,
    size: 10,
  });
  const [groupPagination, setGroupPagination] = useState({
    page: 1,
    size: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [groupTotal, setGroupTotal] = useState<number>(0);
  const [countPage, setCountPage] = useState<number>(1);
  const [groupCountPage, setGroupCountPage] = useState<number>(1);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [selectOpen, setSelectOpen] = useState(false);

  const queryRequest = async (params: anyValueProps) => {
    const { data } = await getPaperBase({
      ...params,
      isShare: false,
      ...(checkPermission(roleOption) ? { resourceCode: groupCode } : {}),
    });
    if (data.code !== 200) {
      message.error("获取资料库失败");
      return;
    }
    setList(data.data);
    setTotal(data.total);
  };

  const queruGroupList = async (params: anyValueProps) => {
    const { data } = await queryGroupList({
      ...params,
    });
    if (data.code === 200) {
      setGroupList(data.result);
      setGroupTotal(data.total);
    }
  };

  useEffect(() => {
    if (roleOption.roleCode === "ADMIN") queruGroupList(groupPagination);
  }, [groupPagination]);

  useEffect(() => {
    queryRequest(pagination);
  }, [pagination, groupCode]);

  const sliceIntoChunks = (arr: any, chunkSize: number) => {
    const chunks = [];
    for (let i = 0; i < arr.length; i += chunkSize) {
      const chunk = arr.slice(i, i + chunkSize);
      chunks.push(chunk);
    }
    return chunks;
  };

  const createFormData = (
    files: any[],
    name: string | Blob,
    baseSelect: string,
  ) => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("files", file);
    });
    formData.append("name", name);
    formData.append("documents", baseSelect);
    return formData;
  };

  const handleOk = useCallback(() => {
    if (!baseSelect) {
      setErrorText("请选择上传的资料库");
    }
    if (!groupCode && checkPermission(roleOption)) {
      setGroupErrorText("请选择课题组");
    }
    formRef.current?.submit().then(async (res: any) => {
      try {
        if (!baseSelect) {
          setErrorText("请选择上传的资料库");
          return;
        }
        setLoading(true);
        const { file, name } = res;
        setFilesLength(file.length);
        const slicedFilesArray = sliceIntoChunks(file, sliceSize);

        const startIndex = lastUploadedIndex !== null ? lastUploadedIndex : 0;
        const firstBatch = slicedFilesArray[startIndex];
        const firstFormData = createFormData(firstBatch, name, baseSelect);
        let firstResponseId: any;
        if (!taskId) {
          const firstResponse = await uploadPdfFile(firstFormData);
          if (firstResponse.data.code !== 200) {
            handleUploadError();
            setLastUploadedIndex(startIndex);
            return;
          }
          firstResponseId = firstResponse.data.data.toString();
          setTaskId(firstResponse.data.data.toString());
          await handleUploadSuccess(
            firstBatch.length,
            slicedFilesArray.length === 1,
          );
        }

        if (slicedFilesArray.length === 1) return;

        for (let i = startIndex; i < slicedFilesArray.length; i++) {
          if (i === 0 && startIndex === 0) {
            continue;
          }

          const item = slicedFilesArray[i];
          const formData = createFormData(item, name, baseSelect);
          if (taskId) {
            formData.append("id", taskId);
          } else {
            formData.append("id", firstResponseId);
          }

          const res = await uploadPdfFile(formData);
          if (res.data.code !== 200) {
            handleUploadError();
            setLastUploadedIndex(i);
            return;
          }
          await handleUploadSuccess(
            item.length,
            i === slicedFilesArray.length - 1,
          );
        }
      } catch (error: any) {
        message.error("上传失败：" + error.message);
        setLoading(false);
        return;
      }
    });
  }, [baseSelect, groupCode]);

  // 处理上传成功的回调函数
  const handleUploadSuccess = async (
    batchLength: number,
    isFinalBatch: boolean,
  ) => {
    setProgressNum((prev) => prev + batchLength);
    if (isFinalBatch) {
      setOpen(false);
      setLoading(false);
      setTaskId(null);
      setProgressNum(0);
      setLastUploadedIndex(null);
      await sleep(1000);
      reload?.();
    }
  };

  const handleUploadError = async () => {
    message.error("上传失败");
    setLoading(false);
  };

  const handleClose = useCallback(() => {
    setOpen(false);
  }, [setOpen]);

  const handlePaperScroll = (event: any) => {
    const bottom =
      event.target.scrollHeight ===
      event.target.scrollTop + event.target.clientHeight;
    if (bottom) {
      // 滚动到底部并且不是正在加载
      const newPage = countPage + 1;
      const maxPage = Math.ceil(total / 10);
      setCountPage(newPage);
      if (newPage <= maxPage) {
        setPagination({ size: 10 * newPage, page: 1 });
      }
    }
  };

  const handleGroupList = (event: any) => {
    const bottom =
      event.target.scrollHeight ===
      event.target.scrollTop + event.target.clientHeight;
    if (bottom) {
      // 滚动到底部并且不是正在加载
      const newPage = groupCountPage + 1;
      const maxPage = Math.ceil(groupTotal / 10);
      window.console.log(123456);
      setGroupCountPage(newPage);
      if (newPage <= maxPage) {
        setGroupPagination({ size: 10 * newPage, page: 1 });
      }
    }
  };

  const handleSelectChange = (event: { target: { value: string } }) => {
    setBaseSelect(event.target.value);
    setErrorText("");
    setGroupErrorText("");
    setSelectOpen(false);
  };

  const handlePaperbaseClick = () => {
    if (!groupCode && checkPermission(roleOption)) {
      setErrorText("请选择课题组");
      setSelectOpen(false);
      return;
    } else {
      setSelectOpen(true);
      setErrorText("");
    }
  };

  const handleGroupChange = (event: { target: { value: string } }) => {
    setGroupCode(event.target.value);
    setErrorText("");
    setGroupErrorText("");
    setBaseSelect("");
  };

  return (
    <CustomDialog
      open={open}
      setDialogOpen={setOpen}
      maskClosable={false}
      title="资料上传"
      width={500}
    >
      <ContentBox slot="content">
        <FormBox>
          {checkPermission(roleOption) && (
            <FormControl
              fullWidth
              error={groupErrorText ? true : false}
              sx={{
                flexDirection: "row",
                height: "100%",
              }}
            >
              <FormLabel
                component={"div"}
                required={true}
                sx={{
                  textWrap: "nowrap",
                  mr: 1.25,
                  mt: 1,
                  textAlign: "right",
                  width: 97,
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  color: "rgba(31, 31, 31, 1)",
                  fontWeight: 400,
                  fontSize: 16,
                  boxSizing: "border-box",
                  justifyContent: "flex-end",
                }}
              >
                课题组
              </FormLabel>
              <BaseSelect>
                <StyledSelect
                  fullWidth
                  labelId="demo-customized-select-label"
                  id="demo-customized-select"
                  value={groupCode}
                  onChange={(e: any) => handleGroupChange(e)}
                  input={<BootstrapInput errorFlag={groupErrorText} />}
                  size="small"
                  MenuProps={{
                    PaperProps: {
                      onScroll: handleGroupList, // 监听菜单滚动
                    },
                  }}
                  sx={{ height: 38 }}
                  disabled={uploadGroupCode ? true : false}
                >
                  {uploadGroupName && (
                    <MenuItem value={groupCode}>{uploadGroupName}</MenuItem>
                  )}
                  {groupList.map((item) => (
                    <MenuItem key={item.resourceCode} value={item.resourceCode}>
                      {item.groupName}
                    </MenuItem>
                  ))}
                </StyledSelect>
                <FormHelperText sx={{ mt: 0, ml: 2, color: "#d32f2f" }}>
                  {groupErrorText || " "}
                </FormHelperText>
              </BaseSelect>
            </FormControl>
          )}
          <FormControl
            fullWidth
            error={errorText ? true : false}
            sx={{
              flexDirection: "row",
              height: "100%",
            }}
          >
            <FormLabel
              component={"div"}
              required={true}
              sx={{
                textWrap: "nowrap",
                mr: 1.25,
                mt: 1,
                textAlign: "right",
                width: 97,
                overflow: "hidden",
                textOverflow: "ellipsis",
                color: "rgba(31, 31, 31, 1)",
                fontWeight: 400,
                fontSize: 16,
                boxSizing: "border-box",
                justifyContent: "flex-end",
              }}
            >
              资料库
            </FormLabel>
            <BaseSelect>
              <StyledSelect
                fullWidth
                open={selectOpen}
                labelId="demo-customized-select-label"
                id="demo-customized-select"
                value={list.length === 0 ? "-1" : baseSelect}
                onChange={(e: any) => handleSelectChange(e)}
                onOpen={() => handlePaperbaseClick()}
                onClose={() => setSelectOpen(false)}
                input={<BootstrapInput errorFlag={errorText} />}
                MenuProps={{
                  PaperProps: {
                    onScroll: handlePaperScroll, // 监听菜单滚动
                  },
                }}
                size="small"
                sx={{ height: 38 }}
                disabled={documentId ? true : false}
              >
                {documentId && (
                  <MenuItem value={documentId}>{documentName}</MenuItem>
                )}
                {list.length === 0 && (
                  <MenuItem value="-1" disabled>
                    <span style={{ color: "#e0e0e0" }}>
                      暂无资料库{" "}
                      {checkPermission(roleOption) && "请先选择课题组"}
                    </span>
                  </MenuItem>
                )}
                {list.map((item) => (
                  <MenuItem key={item.id} value={item.id}>
                    {item.name + " " + item.version}
                  </MenuItem>
                ))}
              </StyledSelect>
              <FormHelperText sx={{ mt: 0, ml: 2, color: "#d32f2f" }}>
                {errorText || " "}
              </FormHelperText>
            </BaseSelect>
          </FormControl>
          <DynamicForm
            ref={formRef}
            columns={UploadColumns}
            size="small"
            // formData={rowOption}
            rowSpacing={0}
            labelWidth={90}
          />
        </FormBox>
        {(loading || progressNum !== 0) && (
          <ProgressLabel>
            上传进度: {progressNum + "/" + filesLength}
          </ProgressLabel>
        )}
      </ContentBox>
      <FooterBox slot="footer">
        <StyleButton
          variant="contained"
          sx={{ background: "#fff", color: "rgba(31, 31, 31, 1)" }}
          onClick={handleClose}
        >
          取消
        </StyleButton>
        <StyleLoadingButton
          loading={loading}
          variant="contained"
          onClick={debounce(handleOk, 500)}
        >
          上传
        </StyleLoadingButton>
      </FooterBox>
    </CustomDialog>
  );
};
export default UploadDialog;
