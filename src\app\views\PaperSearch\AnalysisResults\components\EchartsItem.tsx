import Echarts, { EChartComponentRef } from "./Echarts";
import Download from "@/assets/download.svg";
import FullScreen from "@/assets/full-screen.svg";
import ReactECharts from "echarts-for-react";
import "echarts-wordcloud";
const Root = styled("div")(() => ({
  borderRadius: "20px",
  background: "#fff",
  padding: "5px 20px",
  marginBottom: "15px",
  boxSizing: "border-box",
}));
const TitleMsg = styled("div")(() => ({
  display: "flex",
  justifyContent: "space-between",
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
  marginBottom: "17px",
  alignItems: "center",
}));
const TitleInfo = styled("span")(() => ({
  fontSize: "18px",
  borderRadius: "20px",
  height: "56px",
  lineHeight: "56px",
  fontWeight: 700,
}));
const ButtonDiv = styled("div")(() => ({
  display: "flex",
}));

const ImgStyle = styled("img")(() => ({
  marginRight: "6px",
}));
const ButtonStyle = styled(Button)(({}) => ({
  borderRadius: "16px",
  background: "rgba(242, 242, 242, 1)",
  color: "rgba(64, 64, 64, 1)",
  height: "32px",
  padding: "9px 13px",
  lineHeight: "32px",
  fontSize: "14px",
  marginLeft: "20px",
  ":hover": {
    background: "rgba(242, 242, 242, 1)",
    color: "rgba(29, 90, 246, 1)",
  },
  ":hover img": {
    filter:
      "brightness(0) saturate(100%) invert(27%) sepia(46%) saturate(7008%) hue-rotate(222deg) brightness(100%) contrast(94%)",
  },
  ":disabled": {
    cursor: "not-allowed",
    pointerEvents: "auto",
    color: "rgba(0, 0, 0, 0.26)",
    "& img": {
      filter: "opacity(26%)",
    },
  },
}));

const EmptyMsg = styled("div")(() => ({
  height: "100%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  color: "rgba(0,0,0,0.4)",
  fontSize: "14px",
}));
const ChartsDiv = styled("div")(() => ({
  height: "500px",
}));

interface dataProps {
  [key: string]: number | string;
}
interface Props {
  option: any;
  title: string;
  rootWidth?: string;
  data: dataProps;
  setKey?: (key: string) => void;
  keys?: string;
}
const Index: React.FC<Props> = ({ option, title, rootWidth, data, setKey }) => {
  const chartRef = useRef<EChartComponentRef | null>(null);
  const chartRefs = useRef<ReactECharts>(null);

  // 下载图表
  const handleDownload = () => {
    if (chartRef.current) {
      chartRef.current?.downloadChart();
    } else {
      downloadChart();
    }
  };
  // 全屏
  const handleFullScreen = () => {
    if (chartRef.current) {
      chartRef.current?.handleFullScreen();
    } else {
      fullScreenChart();
    }
  };
  //
  const downloadChart = () => {
    if (!chartRefs.current) return;
    const echartsInstance = chartRefs.current.getEchartsInstance();
    const dataURL = echartsInstance.getDataURL({
      type: "png",
      pixelRatio: 2, // 分辨率
      backgroundColor: "#fff", // 背景色
    });

    const link = document.createElement("a");
    link.href = dataURL;
    link.download = "chart.png";
    link.click();
  };
  const fullScreenChart = () => {
    if (!chartRefs.current) return;
    const chartElement = chartRefs.current.getEchartsInstance().getDom();
    chartElement.style.backgroundColor = "#fff";
    if (chartElement.requestFullscreen) {
      chartElement.requestFullscreen();
    } else if ((chartElement as any).webkitRequestFullscreen) {
      (chartElement as any).webkitRequestFullscreen();
    }
  };

  const downloadCSV = (csvContent: BlobPart, fileName: string) => {
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", fileName);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const convertToCSV = (
    data: any[] | { [s: string]: unknown } | ArrayLike<unknown> | null,
  ) => {
    if (Array.isArray(data)) {
      window.console.log(123);
      return convertArrayToCSV(data);
    } else if (
      typeof data === "object" &&
      data !== null &&
      !Array.isArray(data)
    ) {
      window.console.log("对象");
      return convertObjectToCSV(data);
    } else {
      throw new Error("Unsupported data format");
    }
  };
  // 对于对象数组的情况
  const convertArrayToCSV = (array: any[]) =>
    array.map((item) => `"${item.name}","${item.value}"`).join("\r\n");

  // 对于键值对对象的情况
  const convertObjectToCSV = (data: any) => {
    let csv = "研究趋势TOP10,年份,次数\n";

    for (const key in data) {
      const item = data[key];
      for (const year in item) {
        const count = item[year];
        // 对于第一个年份显示完整的期刊名称，之后的行只显示空格
        const displayKey = year === Object.keys(item)[0] ? `"${key}"` : "";
        csv += `${displayKey},${year},${count}\n`;
      }
    }
    return csv;
  };

  const handleDownloadData = () => {
    const csvContent = convertToCSV(data);
    downloadCSV(csvContent, `${title}.csv`);
  };

  return (
    <Root sx={{ width: rootWidth ? rootWidth : "100%" }}>
      <TitleMsg>
        <TitleInfo>{title}</TitleInfo>
        <ButtonDiv>
          <ButtonStyle
            onClick={handleDownload}
            disabled={!Object.keys(option).length}
          >
            <ImgStyle src={Download}></ImgStyle>
            下载图片
          </ButtonStyle>
          <ButtonStyle
            onClick={handleDownloadData}
            disabled={!Object.keys(option).length}
          >
            <ImgStyle src={Download}></ImgStyle>下载数据
          </ButtonStyle>
          <ButtonStyle
            onClick={handleFullScreen}
            disabled={!Object.keys(option).length}
          >
            <ImgStyle src={FullScreen}></ImgStyle>全屏
          </ButtonStyle>
        </ButtonDiv>
      </TitleMsg>
      <ChartsDiv sx={{ height: rootWidth ? 302 : 450 }} id="terminalServices">
        {Object.keys(option).length ? (
          <>
            <Echarts option={option} ref={chartRef} setKey={setKey}></Echarts>
          </>
        ) : (
          <EmptyMsg>暂无数据</EmptyMsg>
        )}
      </ChartsDiv>
    </Root>
  );
};
export default memo(Index);
