import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { useParams, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { styled } from "@mui/material/styles";
import { message } from "@/components/MessageBox/message";
import Breadcrumb from "../../../components/Breadcrumb";
import PaperHeader from "./components/DetailHeader";
import PaperAnchor from "./components/AnchorContent";
import {
  getPaperDetail,
  getRelatedLiteratureAuthor,
  getWordCloud,
} from "@/api/paperSearch";
import { anyValueProps } from "@/types/common";
import Relevant from "./components/Relevant";
import SearchCloud from "./components/SearchCloud";
import { ContentParamsPorps, splitByTriplePipe } from "./common";
import { batchRetry } from "@/api/personalpaper";
import AnalyzingInProgress from "./components/AnalyzingInProgress";

// 类型定义
interface ParentItemProps {
  name: string;
  path: string;
}

interface PaperData {
  title: string;
  journal: string;
  pdfUrl: string;
  doi: string;
  favorite: boolean;
  figureInfos: any[];
  keywords: string;
  tableInfos: any[];
  authors: any[];
  paragraphs: Array<{ head: string; paragraph: string }>;
  bibls: any[];
  bucket: string;
  pdfPath: string;
  textWithCoordsJsonPath: string;
  figureCount: number;
  tableCount: number;
  publishedYear: number;
  pdfName: string;
  docType: string;
  language: string;
  ext1?: string;
  ext2?: string;
  ext3?: string;
  ext4?: string;
  existDocumentRes: anyValueProps;
  parseStatus: anyValueProps;
}

interface HeaderParams {
  id: string;
  drawerTitle: string;
  title: string;
  titleZh: string;
  authors: any[];
  zhAuthors: <AUTHORS>
  journal: string;
  pdfUrl: string;
  doi: string;
  favorite: boolean;
  bucket: string;
  pdfPath: string;
  textWithCoordsJsonPath: string;
  publishedYear: number;
  pdfName: string;
  docType: string;
  language: string;
  ext1?: string;
  ext2?: string;
  ext3?: string;
  ext4?: string;
  existDocumentRes: anyValueProps;
}

// 样式组件
const Root = styled("div")(() => ({
  height: "100vh",
  display: "flex",
  flexDirection: "column",
  boxSizing: "border-box",
}));

const Main = styled("div")(() => ({
  width: "100%",
  flex: 1,
  display: "flex",
  padding: "0 0 41px 41px",
  boxSizing: "border-box",
  alignItems: "flex-start",
  position: "relative",
  overflow: "hidden",
}));

const MainLeft = styled("div")(() => ({
  height: "100%",
  width: "calc(100% - 409px)",
  display: "flex",
  flexDirection: "column",
  padding: "0 20px 0 16px",
  boxSizing: "border-box",
  borderRadius: "20px",
  background: "#fff",
  flexShrink: 0,
  flexGrow: 0,
  position: "relative",
  overflow: "hidden",
}));

const HeaderSection = styled("div")(() => ({
  flexShrink: 0,
  position: "sticky",
  top: 0,
  zIndex: 100,
  backgroundColor: "#fff",
}));

const ContentSection = styled("div")(() => ({
  flex: 1,
  overflowY: "auto",
  overflowX: "hidden",
  paddingTop: "10px",
  paddingBottom: "30px",
  "&::-webkit-scrollbar": {
    display: "none",
  },
  scrollbarWidth: "none", // Firefox
  msOverflowStyle: "none", // IE
}));

const MainRight = styled("div")(() => ({
  width: 358,
  height: "100%",
  position: "sticky",
  top: 0,
  right: 0,
  boxSizing: "border-box",
  marginLeft: 10,
  overflowY: "auto",
}));

// 自定义Hook：处理URL参数
const useUrlParams = () => {
  const location = useLocation();
  const { way = "" } = useParams<{ way: string }>();

  return useMemo(() => {
    const queryParams = new URLSearchParams(location.search);
    return {
      pdfId: queryParams.get("id"),
      share: queryParams.get("share"),
      searchId: queryParams.get("searchId"),
      way,
    };
  }, [location.search, way]);
};

// 工具函数：提取摘要
const extractAbstract = (
  paragraphs: Array<{ head: string; paragraph: string }>,
  type: string,
): string =>
  paragraphs
    .filter((paragraph) => paragraph.head === type)
    .map((paragraph) => paragraph.paragraph)
    .join(" ") || (type === "abstract" ? paragraphs[0]?.paragraph || "" : "");

// 工具函数：处理关键词
const processKeywords = (
  keywordsSplit: string[] | string[][],
  language: string,
) => {
  if (keywordsSplit.length === 1) {
    const firstItem = keywordsSplit[0];
    const isStringArray = Array.isArray(firstItem);
    return {
      keywords:
        language === "zh" ? [] : isStringArray ? firstItem : [firstItem],
      zhKeywords:
        language === "zh" ? (isStringArray ? firstItem : [firstItem]) : [],
    };
  }
  const secondItem = keywordsSplit[1];
  return {
    keywords: Array.isArray(secondItem) ? secondItem : [secondItem],
    zhKeywords: Array.isArray(keywordsSplit[0])
      ? keywordsSplit[0]
      : [keywordsSplit[0]],
  };
};

// 工具函数：处理作者
const processAuthors = (
  authorsSplit: string[] | string[][],
  language: string,
) => {
  if (authorsSplit.length === 1) {
    const firstItem = authorsSplit[0];
    const isStringArray = Array.isArray(firstItem);
    return {
      authors: language === "zh" ? [] : isStringArray ? firstItem : [firstItem],
      zhAuthors: <AUTHORS>
    };
  }
  const secondItem = authorsSplit[1];
  return {
    authors: Array.isArray(secondItem) ? secondItem : [secondItem],
    zhAuthors: <AUTHORS>
      ? authorsSplit[0]
      : [authorsSplit[0]],
  };
};

// 工具函数：处理目录
const processCatalogue = (ext1: string | undefined, docType: string) => {
  if (!ext1) return "";
  try {
    return docType === "common_doc" ? JSON.parse(ext1) : ext1;
  } catch (error) {
    console.error("解析目录数据失败:", error);
    return ext1;
  }
};

const PaperDetails: React.FC = () => {
  const { pdfId, share, searchId, way } = useUrlParams();
  const breadcrumb = useSelector((state: any) => state.breadcrumb.breadcrumb);

  // 状态管理
  const [headerParams, setHeaderParams] = useState<HeaderParams | undefined>();
  const [contentParams, setContentParams] = useState<
    ContentParamsPorps | undefined
  >();
  const [wordCloud, setWordCloud] = useState<anyValueProps>({});
  const [relevant, setRelevant] = useState<anyValueProps>({});
  const [wordLoading, setWordLoading] = useState(false);
  const [retryLoading, setRetryLoading] = useState(false);
  const [isPolling, setIsPolling] = useState(false);
  const [parseStatus, setParseStatus] = useState<number | undefined>(undefined);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 获取论文详情
  const getPaper = useCallback(async () => {
    if (!pdfId) return;

    try {
      const {
        data: { data: paperData },
      } = (await getPaperDetail({
        id: pdfId,
        onlyAbstract: true,
      })) as { data: { data: PaperData } };

      if (!paperData) {
        message.warning("未找到论文数据");
        return;
      }

      const {
        title,
        journal,
        existDocumentRes,
        pdfUrl,
        doi,
        favorite,
        figureInfos: images,
        keywords,
        tableInfos: tables,
        authors,
        paragraphs,
        bibls,
        bucket,
        pdfPath,
        textWithCoordsJsonPath,
        figureCount,
        tableCount,
        publishedYear,
        pdfName,
        docType,
        language,
        ext1,
        ext2,
        ext3,
        ext4,
      } = paperData;

      const titleSplit = splitByTriplePipe(title);
      const authorsSplit = splitByTriplePipe(authors);
      const keywordsSplit = splitByTriplePipe(keywords);

      const paperAbstract = extractAbstract(paragraphs, "abstract");
      const zhpaperAbstract = extractAbstract(paragraphs, "abstract_zh");
      const { keywords: processedKeywords, zhKeywords } = processKeywords(
        keywordsSplit,
        language,
      );
      const { authors: processedAuthors, zhAuthors } = processAuthors(
        authorsSplit,
        language,
      );

      console.log(processedAuthors, zhAuthors, "detail");

      // 处理标题逻辑 - 安全地提取字符串
      let processedTitle = "";
      let processedTitleZh = "";

      if (Array.isArray(titleSplit)) {
        if (Array.isArray(titleSplit[0])) {
          // 处理 string[][] 情况
          const firstArray = titleSplit as string[][];
          processedTitle =
            firstArray.length === 1
              ? firstArray[0][0] || ""
              : firstArray[1]?.[0] || "";
          processedTitleZh =
            firstArray.length === 2 ? firstArray[0]?.[0] || "" : "";
        } else {
          // 处理 string[] 情况
          const stringArray = titleSplit as string[];
          processedTitle =
            stringArray.length === 1 ? stringArray[0] : stringArray[1] || "";
          processedTitleZh =
            stringArray.length === 2 ? title.split("|||")[0] : "";
        }
      }

      const headerData: HeaderParams = {
        id: pdfId,
        drawerTitle: title,
        title: processedTitle,
        titleZh: processedTitleZh,
        authors: processedAuthors,
        zhAuthors,
        journal,
        pdfUrl,
        doi,
        favorite,
        bucket,
        pdfPath,
        textWithCoordsJsonPath,
        publishedYear,
        pdfName,
        docType,
        language,
        ext1,
        ext2,
        ext3,
        ext4,
        existDocumentRes,
      };

      const contentData: ContentParamsPorps = {
        id: pdfId,
        keywords: processedKeywords,
        zhKeywords,
        images,
        tables,
        abstract: paperAbstract,
        zhAbstract: zhpaperAbstract,
        bibls,
        figureCount,
        tableCount,
        catalogue: processCatalogue(ext1, docType),
        docType,
        language,
      };

      setHeaderParams(headerData);
      setContentParams(contentData);
      if ([0, 1000, 2000].includes(paperData.parseStatus.status)) {
        setParseStatus(paperData.parseStatus.status);
        startPolling();
        setRetryLoading(true);
      }
    } catch (error) {
      console.error("获取资料详情失败:", error);
      message.error("获取资料详情失败");
    }
  }, [pdfId]);

  // 获取词云数据
  const getWords = useCallback(
    async (value: string) => {
      if (!pdfId) return;

      setWordLoading(true);
      try {
        const {
          data: { data },
        } = await getWordCloud(value);
        setWordCloud(data || {});
      } catch (error: any) {
        console.error("获取词云失败:", error);
        message.error("获取词云失败");
      } finally {
        setWordLoading(false);
      }
    },
    [pdfId],
  );

  // 获取相关文献
  const getRelevant = useCallback(async () => {
    if (!pdfId) return;

    try {
      const {
        data: { data, code },
      } = await getRelatedLiteratureAuthor(pdfId);
      if (code === 200) {
        setRelevant(data || {});
      } else {
        message.error("获取相关文献失败");
      }
    } catch (error) {
      console.error("获取相关文献失败:", error);
      message.error("获取相关文献失败，请稍后重试");
    }
  }, [pdfId]);

  // 初始化数据
  const initializeData = useCallback(() => {
    if (!pdfId) return;

    Promise.all([getPaper(), getWords(pdfId), getRelevant()]).catch((error) => {
      console.error("初始化数据失败:", error);
    });
  }, [pdfId, getPaper, getWords, getRelevant]);

  // 构建面包屑导航
  const breadcrumbParent = useMemo(() => {
    if (way === "paperbase") {
      return breadcrumb;
    }

    const items = [];

    if (way === "searchResult") {
      items.push({
        name: "首页",
        path: "/paper-search",
      });
    }

    items.push({
      name: way === "home" ? "首页" : "检索结果",
      path:
        way === "home"
          ? "/paper-search"
          : `/paper-search/search-result/${searchId}`,
    });

    return items.filter((item): item is ParentItemProps => Boolean(item));
  }, [way, breadcrumb, searchId]);

  // 清理轮询
  const clearPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
    setIsPolling(false);
  }, []);

  // 轮询获取论文详情状态
  const pollPaperDetail = useCallback(async () => {
    if (!pdfId) return;

    try {
      const {
        data: { data: paperData },
      } = (await getPaperDetail({
        id: pdfId,
        onlyAbstract: true,
      })) as {
        data: { data: PaperData & { parseStatus?: { status: number } } };
      };

      if (paperData?.parseStatus?.status !== undefined) {
        const status = paperData.parseStatus.status;
        console.log("轮询状态:", status);

        // 更新解析状态
        setParseStatus(status);

        // 继续轮询的状态: 0, 1000, 2000
        if ([0, 1000, 2000].includes(status)) {
          console.log("继续轮询...");
          return;
        }

        // 停止轮询的状态: 3000, 4000, 5000
        if ([3000, 4000, 5000].includes(status)) {
          console.log("停止轮询, 状态:", status);
          clearPolling();

          if (status === 3000 || status === 4000) {
            // 状态为3000时，关闭loading并调用getPaper
            console.log("解析成功，刷新数据");
            setRetryLoading(false);
            await getPaper();
          } else {
            // 状态为4000或5000时，只关闭loading
            // setRetryLoading(false);
            if (status === 5000) {
              message.error("解析失败");
            }
            // await getPaper();
          }
        }
      }
    } catch (error) {
      console.error("轮询获取论文状态失败:", error);
      // 轮询出错时不停止轮询，继续尝试
    }
  }, [pdfId, clearPolling, getPaper]);

  // 开始轮询
  const startPolling = useCallback(() => {
    if (isPolling || !pdfId) return;

    console.log("开始轮询论文解析状态");
    setIsPolling(true);

    // 立即执行一次
    pollPaperDetail();

    // 设置定时轮询，每3秒执行一次
    pollingIntervalRef.current = setInterval(() => {
      pollPaperDetail();
    }, 3000);
  }, [isPolling, pdfId, pollPaperDetail]);

  const handleRetry = async () => {
    const ids = [pdfId];
    try {
      const { data } = await batchRetry(ids);
      if (data.code === 200) {
        message.success("重新解析成功");
        setRetryLoading(true);
        // 重新解析成功后开始轮询
        startPolling();
      } else {
        message.error("重新解析失败");
        setRetryLoading(false);
      }
    } catch {
      message.error("重新解析失败");
      setRetryLoading(false);
    }
  };

  const handleClose = async () => {
    setRetryLoading(false);
    clearPolling();
    await getPaper();
  };

  useEffect(() => {
    initializeData();
  }, [initializeData]);

  // 组件卸载时清理轮询
  useEffect(
    () => () => {
      clearPolling();
    },
    [clearPolling],
  );

  return (
    <Root>
      <Breadcrumb parent={breadcrumbParent} current="文章详情" />
      <Main>
        {retryLoading && (
          <AnalyzingInProgress
            status={parseStatus}
            polling={startPolling}
            pdfId={pdfId}
            onClose={handleClose}
          />
        )}
        <MainLeft>
          <HeaderSection>
            <PaperHeader
              headerParams={headerParams || {}}
              init={getPaper}
              share={share}
              retry={handleRetry}
            />
          </HeaderSection>
          <ContentSection>
            <PaperAnchor contentParams={contentParams} />
          </ContentSection>
        </MainLeft>
        <MainRight>
          <SearchCloud wordLoading={wordLoading} coAuthorShip={wordCloud} />
          <Relevant data={relevant} />
        </MainRight>
      </Main>
    </Root>
  );
};

export default PaperDetails;
