import { anyValueProps } from "@/types/common";
import { PageProps } from "../MyPaperPagination";
import {
  Grid2 as Grid,
  SvgIcon,
  Tooltip,
  TooltipProps,
  tooltipClasses,
} from "@mui/material";
import { CircleCheckbox } from "./setting";
import { resizeOptions } from "./setting";
import PopoverDelete from "@/components/Popover";
import Empty from "@/assets/empty.png";
import PaperIcon from "@/assets/paper-icon.svg";
import Pagination from "../Pagination";
import EditIcon from "@mui/icons-material/Edit";
import ShareIcon from "@mui/icons-material/Share";
import DeleteOutlineRoundedIcon from "@mui/icons-material/DeleteOutlineRounded";
import { withPermission } from "../HocButton";
import { PERMISSION_MENU } from "@/utils/permission";
import GroupPopover from "./groupPopover";
import { useAppSelector } from "@/hooks";
import { checkPermission } from "@/utils/auth";

type OperationType = {
  edit: any;
  delete: any;
  chat: any;
  transfer?: any;
};

type TableType = "paper" | "database";

interface columnsTypeProps {
  title: string;
  version: string;
  count: string;
  updateTime: string;
  groupName: string;
  documentSourceType: string;
}

interface CardTableProps {
  total: number;
  data: anyValueProps[];
  pageSizeOptions?: number[];
  onChangePage: (pageParams: PageProps) => void;
  onSelectChange: (e: React.ChangeEvent<HTMLInputElement>, id: number) => void;
  onClickCard: (value: any) => void;
  operations?: OperationType | undefined;
  type: TableType;
  buttonGroup?: React.ReactNode;
  pagination: PageProps;
  columnsTypeName: columnsTypeProps;
}

const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
  flexDirection: "column",
}));

const TableSide = styled("div")(() => ({
  flex: 1,
  boxSizing: "border-box",
  overflowY: "scroll",
  "::-webkit-scrollbar": {
    display: "none",
  },
}));

const CardItem = styled("div", {
  shouldForwardProp: (prop) => prop !== "isDialog",
})<{ isDialog: boolean }>(({ isDialog }) => ({
  width: "100%",
  background: "#fff",
  border: "1px solid #e8e8e8",
  borderRadius: "20px",
  paddingTop: "10px",
  paddingBottom: isDialog ? "10px" : 0,
  boxSizing: "border-box",
  "&:hover": {
    boxShadow:
      "0 1px 2px -2px rgba(0, 0, 0, 0.16),0 3px 6px 0 rgba(0, 0, 0, 0.12),0 5px 12px 4px rgba(0, 0, 0, 0.09)",
    border: "transparent",
  },
  transition: "box-shadow 0.4s,border-color 0.4s",
}));

const CardHeader = styled("div")(() => ({
  width: "100%",
  height: "20px",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  boxSizing: "border-box",
  padding: "0 15px",
}));

const CardContent = styled("div")(() => ({
  width: "calc(100% - 36px)",
  height: "120px",
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  cursor: "pointer",
  margin: "0 18px",
  padding: "10px 0",
}));

const ContentHeader = styled("div")(() => ({
  width: "100%",
  display: "flex",
  alignItems: "center",
  fontSize: "18px",
  fontWeight: 700,
  color: "rgba(64, 64, 64, 1)",
  lineHeight: "20px",
}));

const GroupName = styled("div")(() => ({
  fontSize: "14px",
  marginLeft: 10,
}));

const IconImg = styled("img")(() => ({
  width: 20,
  height: 20,
  marginRight: 5,
}));

const ContentTitle = styled("span")(() => ({
  height: "100%",
  margin: 0,
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  flex: 1,
}));

const ContentVersion = styled("span")(() => ({
  width: "100px",
  height: "100%",
  margin: "0 0 0 10px",
  display: "flex",
  justifyContent: "flex-end",
}));

const ContentShare = styled("div")(() => ({
  width: "100%",
  fontSize: "14px",
  fontWeight: 400,
  color: "rgba(64, 64, 64, 0.8)",
  paddingLeft: 50,
  marginTop: 8,
}));

const ContentCount = styled("div")(() => ({
  width: "100%",
  fontSize: "14px",
  fontWeight: 400,
  color: "rgba(64, 64, 64, 0.8)",
  paddingLeft: 50,
  marginTop: 8,
}));

const ContentUpdateTime = styled("div")(() => ({
  width: "100%",
  fontSize: 14,
  fontWeight: 400,
  color: "rgba(64, 64, 64, 0.8)",
  paddingLeft: 50,
  marginTop: 8,
}));

const CardFooter = styled("div")(() => ({
  width: "100%",
  height: "50px",
  borderRadius: "0 0 10px 10px",
  padding: "10px 0",
  display: "flex",
  alignItems: "center",
  boxSizing: "border-box",
  borderTop: "1px dashed rgba(207, 207, 207, 1)",
  // margin: "0 10px",
  justifyContent: "space-around",
}));

const BootstrapTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.common.black,
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.black,
  },
}));

// const ButtonStyle = styled(Button, {
//   shouldForwardProp: (prop) => prop !== "isColor",
// })<{ isColor: boolean }>(({ isColor }) => ({
//   color: isColor ? "rgba(255, 87, 51, 1)" : "rgba(24, 112, 199, 1)",
// }));

const EmptyDiv = styled("div")(() => ({
  height: "100%",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  color: "#cfcfcf",
  fontSize: "14px",
}));

const PaginationSide = styled("div")<{ flexFlag: any }>(({ flexFlag }) => ({
  width: "100%",
  height: 50,
  boxSizing: "border-box",
  display: "flex",
  alignItems: "center",
  justifyContent: flexFlag ? "space-between" : "flex-end",
}));

const TypeOptions: any = {
  edit: "编辑",
  delete: "删除",
  chat: "发起会话",
  share: "分享",
};

const EditIconButton = styled(EditIcon)({
  cursor: "pointer",
  ":hover": {
    color: "#4248B5",
  },
});

const DeleteIconButton = styled(DeleteOutlineRoundedIcon)({
  cursor: "pointer",
  ":hover": {
    color: "#4248B5",
  },
});

const ShareIconButton = styled(ShareIcon)({
  cursor: "pointer",
  ":hover": {
    color: "#4248B5",
  },
});

interface roleProps {
  role: string;
  type: string;
}

const StyleButton = ({
  item,
  action,
  roleList,
  hidden,
}: {
  item: any;
  action: any;
  roleList: roleProps;
  hidden: boolean;
}) => {
  const CardTableButton: React.FC<any> = ({
    item,
    action,
    roleList,
    hidden,
  }) => {
    const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
    const [popoverOpen, setPopoverOpen] = useState<HTMLButtonElement | null>(
      null,
    );
    const [documentId, setDocumentId] = useState<string>("");
    const [deleteItem, setDeleteItem] = useState<any>(null);
    const handleDeleteClose = () => {
      setAnchorEl(null);
    };
    const handleDeleteConfirm = () => {
      const res = action(deleteItem);
      if (res) {
        message.success("删除成功");
      } else {
        message.error("删除失败");
      }
      setAnchorEl(null);
    };

    const cardDelete = (event: any, item: any) => {
      event.stopPropagation();
      setAnchorEl(event.currentTarget);
      setDeleteItem(item);
    };

    const handleShare = (e: any, item: any) => {
      e.stopPropagation();
      setPopoverOpen(e.currentTarget);
      setDocumentId(item.id);
    };

    return (
      <>
        {roleList.type === "edit" && !hidden ? (
          <BootstrapTooltip title={TypeOptions["edit"]} placement="top">
            <EditIconButton fontSize="small" onClick={(e) => action(e, item)} />
          </BootstrapTooltip>
        ) : (
          <></>
        )}
        {roleList.type === "delete" && !hidden ? (
          <>
            <BootstrapTooltip title={TypeOptions["delete"]} placement="top">
              <DeleteIconButton
                fontSize="small"
                onClick={(e) => cardDelete(e, item)}
              />
            </BootstrapTooltip>
            {!!anchorEl && (
              <PopoverDelete
                title="确定删除该资料库吗?"
                anchorEl={anchorEl}
                handleClose={handleDeleteClose}
                handleConfirm={handleDeleteConfirm}
              />
            )}
          </>
        ) : (
          <></>
        )}
        {roleList.type === "chat" ? (
          <BootstrapTooltip title={TypeOptions["chat"]} placement="top">
            <SvgIcon
              fontSize={"inherit"}
              sx={{
                width: "20px",
                height: "20px",
                cursor: "pointer",
                ":hover": { color: "#4248B5" },
              }}
              onClick={() => action(item)}
            >
              <path
                fill="currentColor"
                d="M2 8.994A5.99 5.99 0 0 1 8 3h8c3.313 0 6 2.695 6 5.994V21H8c-3.313 0-6-2.695-6-5.994zM20 19V8.994A4.004 4.004 0 0 0 16 5H8a3.99 3.99 0 0 0-4 3.994v6.012A4.004 4.004 0 0 0 8 19zm-6-8h2v2h-2zm-6 0h2v2H8z"
              ></path>
            </SvgIcon>
          </BootstrapTooltip>
        ) : (
          <></>
        )}
        {roleList.type === "share" && !hidden ? (
          <>
            <BootstrapTooltip title={TypeOptions["share"]} placement="top">
              <ShareIconButton
                fontSize="small"
                onClick={(e) => handleShare(e, item)}
              />
            </BootstrapTooltip>
            {Boolean(popoverOpen) && (
              <GroupPopover
                anchorEl={popoverOpen}
                documentId={documentId}
                open={Boolean(popoverOpen)}
                onClose={() => setPopoverOpen(null)}
              />
            )}
          </>
        ) : (
          <></>
        )}
      </>
    );
  };
  const PermissionButton = withPermission(CardTableButton, roleList.role);
  return (
    <PermissionButton
      item={item}
      action={action}
      roleList={roleList}
      hidden={hidden}
    />
  );
};

const CardTable: React.FC<CardTableProps> = (props) => {
  const {
    total,
    pagination,
    pageSizeOptions = [12, 24, 48],
    onChangePage,
    onSelectChange,
    onClickCard,
    data,
    operations,
    type,
    buttonGroup,
    columnsTypeName,
    ...others
  } = props;
  const tableSideRef = useRef<HTMLDivElement>(null);
  const [columns, setColumns] = useState<any>({ xs: 4, sm: 8, md: 16 });
  const { roleOption } = useAppSelector((state) => state.user);
  const handleResize = () => {
    const tableSideCurrent = tableSideRef.current;
    const tableSideWidth = tableSideCurrent?.clientWidth;
    if (tableSideWidth) {
      if (tableSideWidth < 1200 && tableSideWidth > 900) {
        setColumns(resizeOptions["middle"]);
      }
      if (tableSideWidth > 1200) {
        setColumns(resizeOptions["normal"]);
      }
      if (tableSideWidth < 900) {
        setColumns(resizeOptions["small"]);
      }
    }
  };

  // 打开编辑弹窗
  const cardEdit = (event: { stopPropagation: () => void }, item: any) => {
    event.stopPropagation();
    window.console.log(item);
    operations?.edit(item);
  };

  // 打开会话
  const cardChat = (item: any) => {
    operations?.chat(item);
  };

  // 转存
  const cardTransfer = (item: any) => {
    operations?.transfer(item);
  };

  const buttonGroupList = [
    {
      action: cardEdit,
      roleList: {
        role: PERMISSION_MENU["edit"],
        type: "edit",
      },
    },
    {
      action: operations?.delete,
      roleList: {
        role: PERMISSION_MENU["edit"],
        type: "delete",
      },
    },
    {
      action: cardTransfer,
      roleList: {
        role: PERMISSION_MENU["share"],
        type: "share",
      },
    },
    {
      action: cardChat,
      roleList: {
        role: PERMISSION_MENU["chat"],
        type: "chat",
      },
    },
  ];

  useEffect(() => {
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);
  return (
    <Root>
      <TableSide ref={tableSideRef}>
        {total > 0 ? (
          <Grid container spacing={{ xs: 1, md: 3 }} columns={columns}>
            {data.map((item, index) => (
              <Grid size={{ xs: 2, sm: 4, md: 4 }} key={index}>
                <CardItem isDialog={operations ? false : true}>
                  <CardHeader>
                    {checkPermission(roleOption) && (
                      <GroupName>{item[columnsTypeName.groupName]}</GroupName>
                    )}
                    <CircleCheckbox
                      onChange={(e) => onSelectChange(e, item.id)}
                      checked={item.checked ?? false}
                    />
                  </CardHeader>
                  <CardContent onClick={() => onClickCard(item)}>
                    <ContentHeader>
                      <IconImg src={PaperIcon} />
                      <ContentTitle>{item[columnsTypeName.title]}</ContentTitle>
                      <ContentVersion>
                        {item[columnsTypeName.version]}
                      </ContentVersion>
                    </ContentHeader>
                    <ContentShare>
                      {item[columnsTypeName.documentSourceType] === "share" &&
                        "来自分享"}
                    </ContentShare>
                    <ContentCount>
                      {item[columnsTypeName.count]}
                      {type === "database" ? "条数据" : "篇资料"}
                    </ContentCount>
                    <ContentUpdateTime>
                      更新时间: {item[columnsTypeName.updateTime]}
                    </ContentUpdateTime>
                  </CardContent>
                  {operations && (
                    <CardFooter>
                      {buttonGroupList.map((ele, index) => (
                        <StyleButton
                          key={index}
                          item={item}
                          action={ele.action}
                          roleList={ele.roleList}
                          hidden={
                            item[columnsTypeName.documentSourceType] === "share"
                          }
                        />
                      ))}
                    </CardFooter>
                  )}
                </CardItem>
              </Grid>
            ))}
          </Grid>
        ) : (
          <EmptyDiv>
            <img src={Empty} width={62} height={40} alt="" />
            <div>{type === "database" ? "暂无实验数据库" : "暂无资料库"}</div>
          </EmptyDiv>
        )}
      </TableSide>
      <PaginationSide flexFlag={buttonGroup}>
        {total > 0 && buttonGroup}
        {total > 0 && (
          <Pagination
            total={total}
            page={pagination.page}
            pageSize={pagination.pageSize}
            onChangePage={onChangePage}
            pageSizeOptions={pageSizeOptions}
            {...others}
          />
        )}
      </PaginationSide>
      {/* {!!anchorEl && (
        <PopoverDelete
          title="确定删除该知识库吗?"
          anchorEl={anchorEl}
          handleClose={handleClose}
          handleConfirm={handleConfirm}
        />
      )} */}
    </Root>
  );
};
export default CardTable;
