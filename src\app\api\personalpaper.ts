// 资料库API
import axios from "axios";
import { anyValueProps } from "@/types/common";

export const pdfDrawKeywords = (params: anyValueProps) =>
  axios.post("/engine/api/pdf/draw-keywords-2", params);
export const getPersonalPaperList = (params: anyValueProps) =>
  axios.get("/engine/api/pdf/relation/user/page", { params });

export const uploadPdfFile = (params: anyValueProps) =>
  axios.post("/engine/api/task/create", params);

export const getPdfTask = (params: anyValueProps) =>
  axios.post("/engine/api/task/list", params);

export const queryPdfList = (params: anyValueProps) =>
  axios.post("/engine/api/task/detail-list", params);

export const editPdfInfo = (params: anyValueProps) =>
  axios.post("/engine/api/pdf/update", params);

export const deletePdf = (params: anyValueProps) =>
  axios.post("/engine/api/pdf/relation/delete", params);

export const getPaperBase = (params?: anyValueProps) =>
  axios.get("/engine/api/group/documents/page", { params });

export const createPaperBase = (params: anyValueProps) =>
  axios({
    url: "/engine/api/group/create",
    method: "post",
    data: params,
  });

export const editPaperBase = (params: anyValueProps) =>
  axios.post("/engine/api/group/update", params);

export const deletePaperBase = (params: anyValueProps) =>
  axios.post("/engine/api/group/delete", params);

export const batchPdfs = (params: anyValueProps) =>
  axios({
    url: "/engine/api/file/download/batch",
    method: "post",
    data: params,
    responseType: "blob",
  });

export const downloadPaperBase = (params: anyValueProps) =>
  axios({
    url: "/engine/api/file/download/document",
    method: "post",
    data: params,
    responseType: "blob",
  });

export const reparsePdf = (params: anyValueProps) =>
  axios({
    url: "/engine/api/pdf/re-parse",
    method: "post",
    params,
  });

export const getShareList = (params: anyValueProps) =>
  axios.get("/engine/api/document/share/selected-list", { params });

export const sharePaperBase = (params: anyValueProps) =>
  axios.get("/engine/api/document/share/share", { params });

export const batchRetry = (params: anyValueProps) =>
  axios.post("/engine/api/pdf/re-parse-more", params);
