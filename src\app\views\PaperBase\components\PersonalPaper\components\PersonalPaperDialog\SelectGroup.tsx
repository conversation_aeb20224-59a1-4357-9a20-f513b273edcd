import { createPaperBase } from "@/api/personalpaper";
import CustomDialog from "@/components/Dialog";
import { useAppSelector } from "@/hooks";
import {
  FormControl,
  FormHelperText,
  FormLabel,
  InputBase,
  MenuItem,
  Select,
} from "@mui/material";

interface Props {
  open: boolean;
  setOpen: (value: boolean) => void;
  setParentOpen: (value: boolean) => void;
  reload: () => void;
  rowOption: any;
}

const ContentBox = styled("div")(() => ({
  width: "100%",
  padding: "15px 20px 0 20px",
  boxSizing: "border-box",
}));

const FormBox = styled("div")(() => ({
  width: "100%",
}));

const BaseSelect = styled("div")(() => ({
  width: "calc(100% - 70px)",
}));

const BootstrapInput = styled(InputBase, {
  shouldForwardProp: (props) => props !== "errorFlag",
})<{ errorFlag: string }>(({ theme, errorFlag }) => ({
  "label + &": {
    marginTop: theme.spacing(3),
  },
  "& .MuiInputBase-input": {
    // height: "30px",
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.background.paper,
    border: "1px solid #ced4da",
    borderColor: errorFlag ? "#d32f2f" : "#ced4da",
    fontSize: 16,
    // padding: "10px 26px 10px 12px",
    padding: "5px 0 5px 12px",
    transition: theme.transitions.create(["border-color", "box-shadow"]),
    boxSize: "border-box",
    // Use the system font instead of the default Roboto font.
    fontFamily: [
      "-apple-system",
      "BlinkMacSystemFont",
      '"Segoe UI"',
      "Roboto",
      '"Helvetica Neue"',
      "Arial",
      "sans-serif",
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(","),
    "&:focus": {
      borderRadius: 4,
      borderColor: "#4248B5",
      boxShadow: "0 0 0 0.2rem rgba(64,72,181,.25)",
    },
  },
}));

const StyledSelect = styled(Select)(() => ({
  "& .MuiSelect-select": {
    borderRadius: 28,
  },
  "&.MuiInputBase-root": {
    borderRadius: 28,
  },
  "& #demo-customized-select": {
    // height: 10,
    padding: "6.5px 10px",
    borderRadius: 28,
    boxSize: "border-box",
  },
}));

const SelectGroup: React.FC<Props> = ({
  open,
  setOpen,
  setParentOpen,
  rowOption,
  reload,
}) => {
  const { groupOption } = useAppSelector((state) => state.user);
  const [errorText, setErrorText] = useState<string>("");
  const [baseSelect, setBaseSelect] = useState("");
  const [viewNum, setViewNum] = useState(10);
  const handleOk = async () => {
    if (!baseSelect) {
      setErrorText("请选择课题组");
      return;
    } else {
      try {
        const {
          data: { code, message },
        } = await createPaperBase({
          ...rowOption,
          resourceCode: baseSelect,
          type: "document",
        });
        if (code === 200) {
          setOpen(false);
          setParentOpen(false);
          reload();
        } else {
          message.error(message);
        }
      } catch (e: any) {
        message.error(e.message);
      }
    }
  };

  const handleScroll = (event: any) => {
    const bottom =
      event.target.scrollHeight ===
      event.target.scrollTop + event.target.clientHeight;
    if (bottom) {
      // 滚动到底部并且不是正在加载
      if (viewNum < groupOption.length) {
        setViewNum((viewNum) => viewNum + 10);
      }
    }
  };

  const handleSelectChange = (event: { target: { value: string } }) => {
    setBaseSelect(event.target.value);
    setErrorText("");
  };

  return (
    <CustomDialog
      open={open}
      setDialogOpen={setOpen}
      title="选择课题组"
      okButtonProps={{ onOk: handleOk }}
      cancelButtonProps={{ onCancel: () => setOpen(false) }}
      width={410}
    >
      <ContentBox slot="content">
        <FormBox>
          <FormControl
            fullWidth
            error={errorText ? true : false}
            sx={{
              flexDirection: "row",
              height: "100%",
            }}
          >
            <FormLabel
              component={"div"}
              required={true}
              sx={{
                textWrap: "nowrap",
                mr: 1.25,
                mt: 1,
                textAlign: "right",
                width: 97,
                overflow: "hidden",
                textOverflow: "ellipsis",
                color: "rgba(31, 31, 31, 1)",
                fontWeight: 400,
                fontSize: 16,
                boxSizing: "border-box",
                justifyContent: "flex-end",
              }}
            >
              课题组
            </FormLabel>
            <BaseSelect>
              <StyledSelect
                fullWidth
                labelId="demo-customized-select-label"
                id="demo-customized-select"
                value={baseSelect}
                onChange={(e: any) => handleSelectChange(e)}
                input={<BootstrapInput errorFlag={errorText} />}
                MenuProps={{
                  PaperProps: {
                    onScroll: handleScroll, // 监听菜单滚动
                  },
                }}
                size="small"
                placeholder="请选择资料库"
                sx={{ height: 38 }}
              >
                {groupOption.slice(0, viewNum).map((item) => (
                  <MenuItem key={item.value} value={item.value}>
                    {item.label}
                  </MenuItem>
                ))}
              </StyledSelect>
              <FormHelperText sx={{ mt: 0, ml: 2, color: "#d32f2f" }}>
                {errorText || " "}
              </FormHelperText>
            </BaseSelect>
          </FormControl>
        </FormBox>
      </ContentBox>
    </CustomDialog>
  );
};
export default SelectGroup;
