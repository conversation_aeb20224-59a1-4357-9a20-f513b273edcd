@font-face {
  font-family: "iconfont"; /* Project id 3325749 */
  src: url('iconfont.woff2?t=1652680350714') format('woff2'),
       url('iconfont.woff?t=1652680350714') format('woff'),
       url('iconfont.ttf?t=1652680350714') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-lifangti:before {
  content: "\e654";
}

.icon-component:before {
  content: "\e8b2";
}

.icon-icons8-literature-copy:before {
  content: "\ea9b";
}

.icon-yinzhang:before {
  content: "\e608";
}

.icon-lunwenketi:before {
  content: "\e670";
}

.icon-arrow-down:before {
  content: "\e969";
}

.icon-arrow-right:before {
  content: "\e687";
}

.icon-wodeguanzhu:before {
  content: "\e8bc";
}

.icon-yishoucang:before {
  content: "\e611";
}

.icon-search:before {
  content: "\e621";
}

.icon-news-paper:before {
  content: "\e991";
}

.icon-md-paper:before {
  content: "\e942";
}

.icon-chart:before {
  content: "\e711";
}

.icon-suscribe:before {
  content: "\e629";
}

.icon-reference:before {
  content: "\e62b";
}

.icon-document:before {
  content: "\e612";
}

.icon-author-total:before {
  content: "\e60d";
}

.icon-pen:before {
  content: "\e65e";
}

.icon-person:before {
  content: "\e72a";
}

.icon-star-filled:before {
  content: "\e640";
}

.icon-bigStar:before {
  content: "\e672";
}

.icon-quato:before {
  content: "\e65d";
}

.icon-label:before {
  content: "\e62c";
}

.icon-more-right:before {
  content: "\e61a";
}

.icon-more-down:before {
  content: "\e600";
}

.icon-relations:before {
  content: "\e649";
}

.icon-relation-square:before {
  content: "\e624";
}

.icon-relation:before {
  content: "\e62a";
}

