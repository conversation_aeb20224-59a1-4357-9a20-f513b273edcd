import { FormColumnProps } from "@/components/DynamicForm";
import ChatIcon from "@mui/icons-material/Chat";
import EditIcon from "@mui/icons-material/Edit";
import DeleteOutlineRoundedIcon from "@mui/icons-material/DeleteOutlineRounded";
import { buttonGroupProps } from "../KnowledgeBase/components/common";
import { PERMISSION_MENU } from "@/utils/permission";

export const PaperBaseNavGroup = [
  // {
  //   menuName: "我的资料库",
  //   menuCode: "MyDocDB",
  // },
  {
    name: "资料库",
    keyword: "TopicDocDB",
  },
  // {
  //   name: "公司资料库",
  //   keyword: "company",
  // },
];

export const searchColumns: FormColumnProps[] = [
  {
    name: "updateTime",
    label: "更新时间",
    componentType: "date",
    grid: 12,
  },
];

export const CardActionsGroup = [
  {
    name: "编辑",
    type: "edit",
    icon: EditIcon,
  },
  {
    name: "删除",
    type: "delete",
    icon: DeleteOutlineRoundedIcon,
  },
  {
    name: "发起会话",
    type: "chat",
    icon: ChatIcon,
  },
];

export const breadcrumbList = ["我的资料库", "部门资料库", "公司资料库"];

export const buttonGroup: buttonGroupProps[] = [
  {
    name: "全选",
    keyword: "selectAll",
    role: "NORMAL",
  },
  {
    name: "反向选择",
    keyword: "reverseSelectAll",
    role: "NORMAL",
  },
  {
    name: "取消选择",
    keyword: "clearSelectAll",
    role: "NORMAL",
  },
  {
    name: "删除所选",
    keyword: "deleteSelectAll",
    role: PERMISSION_MENU["edit"],
  },
  {
    name: "下载所选",
    keyword: "downloadSelectAll",
    role: PERMISSION_MENU["edit"],
  },
];

export const replaceNullWithEmptyString = (obj: any): any => {
  if (obj && typeof obj === "object") {
    // 遍历对象中的每个属性
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        // 如果值为 null，将其替换为空字符串
        if (obj[key] === null) {
          obj[key] = "";
        } else if (typeof obj[key] === "object") {
          // 如果是对象，递归调用
          obj[key] = replaceNullWithEmptyString(obj[key]);
        }
      }
    }
  }
  return obj;
};
