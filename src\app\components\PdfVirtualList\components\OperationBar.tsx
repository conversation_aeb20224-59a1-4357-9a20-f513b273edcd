import { styled } from "@mui/material";
import React, { useState, KeyboardEvent } from "react";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";

interface TopBarProps {
  currentPage: number;
  totalPages: number;
  scale: number;
  onPageChange: (page: number) => void;
  onScaleChange: (scale: number) => void;
}

const Root = styled("div")(() => ({
  width: 280,
  height: 56,
  position: "absolute",
  bottom: 20,
  left: `calc(50% - 140px)`,
  backgroundColor: "#fff",
  borderBottom: "1px solid rgba(235, 235, 235, 1)",
  boxShadow: "0 0 16px rgba(0, 0, 0, 0.15)",
  borderRadius: 28,
  display: "flex",
  alignItems: "center",
  justifyContent: "space-around",
  padding: "0 10px",
  gap: "15px",
  zIndex: 1101,
  boxSizing: "border-box",
}));

const OperationNav = styled("div")(() => ({
  height: 18,
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
}));

const StyleBotton = styled("button")(() => ({
  width: 18,
  height: 18,
  borderRadius: "50%",
  background: "#fff",
  border: "1px solid rgba(235, 235, 235, 1)",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  boxSizing: "border-box",
  cursor: "pointer",
  padding: 0,
  margin: 0,
  lineHeight: 1,
}));

const StyleLabel = styled("div")(() => ({
  fontSize: 14,
  fontWeight: 400,
  color: "rgba(0, 0, 0, 1)",
  margin: "0 5px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  height: "18px",
  lineHeight: 1,
  "& > *": {
    display: "inline-flex",
    alignItems: "center",
  },
}));

const PageInput = styled("input")<{ digits: number }>((props) => ({
  width: `${Math.max(30, props.digits * 9 + 6)}px`,
  minWidth: "24px",
  textAlign: "center",
  border: "1px solid rgba(200, 200, 200, 0.5)",
  borderRadius: "4px",
  padding: "0 2px",
  fontSize: "14px",
  height: "18px",
  margin: "0 2px",
  outline: "none",
  backgroundColor: "rgba(245, 245, 245, 0.3)",
  color: "#333",
  boxSizing: "border-box",
  verticalAlign: "baseline",
  lineHeight: "16px",
  fontFamily: "inherit",
  appearance: "none",
  WebkitAppearance: "none",
  MozAppearance: "textfield",
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  "&::-webkit-inner-spin-button, &::-webkit-outer-spin-button": {
    WebkitAppearance: "none",
    margin: 0,
  },
  "&:focus": {
    outline: "none",
    boxShadow: "0 0 0 2px rgba(0,104,177,.5)",
  },
}));

const PageDivider = styled("span")(() => ({
  fontSize: "14px",
  color: "rgba(0, 0, 0, 0.7)",
  margin: "0 2px",
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  height: "18px",
  lineHeight: 1,
  verticalAlign: "baseline",
  transform: "translateY(0)",
}));

const TotalPages = styled("span")(() => ({
  fontSize: "14px",
  color: "rgba(0, 0, 0, 0.7)",
  display: "inline-flex",
  alignItems: "center",
  justifyContent: "center",
  height: "18px",
  lineHeight: 1,
  verticalAlign: "baseline",
  transform: "translateY(0)",
  paddingTop: "0",
}));

const PageNumberContainer = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  height: "18px",
}));

const OperationBar: React.FC<TopBarProps> = ({
  currentPage,
  totalPages,
  scale,
  onPageChange,
  onScaleChange,
}) => {
  const [inputPage, setInputPage] = useState<string>(currentPage.toString());
  const totalDigits = totalPages.toString().length;

  // 在组件初始化和currentPage变化时更新输入框值
  React.useEffect(() => {
    setInputPage(currentPage.toString());
  }, [currentPage]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // 仅允许输入数字
    const value = e.target.value.replace(/[^\d]/g, "");
    // 限制最大长度为总页数的位数
    if (value.length <= totalDigits) {
      setInputPage(value);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const pageNum = parseInt(inputPage);
      if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
        onPageChange(pageNum);
      } else {
        // 如果输入无效，重置为当前页码
        setInputPage(currentPage.toString());
      }
      // 回车后取消焦点，收起软键盘
      (e.target as HTMLElement).blur();
    }
  };

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    // 当获得焦点时自动选中所有文本
    e.target.select();
  };

  const handleBlur = () => {
    const pageNum = parseInt(inputPage);
    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
      onPageChange(pageNum);
    } else {
      // 如果输入无效，重置为当前页码
      setInputPage(currentPage.toString());
    }
  };

  return (
    <Root>
      <OperationNav>
        <StyleBotton onClick={() => onPageChange(Math.max(1, currentPage - 1))}>
          <KeyboardArrowUpIcon sx={{ fontSize: 14 }} />
        </StyleBotton>
        <StyleLabel>
          <PageNumberContainer>
            <PageInput
              value={inputPage}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onBlur={handleBlur}
              onFocus={handleFocus}
              type="text"
              digits={totalDigits}
              aria-label="页码输入框"
            />
            <PageDivider>/</PageDivider>
            <TotalPages>{totalPages}</TotalPages>
          </PageNumberContainer>
        </StyleLabel>
        <StyleBotton
          onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
        >
          <KeyboardArrowDownIcon sx={{ fontSize: 14 }} />
        </StyleBotton>
      </OperationNav>
      <OperationNav>
        <StyleBotton onClick={() => onScaleChange(Math.min(2, scale + 0.1))}>
          <KeyboardArrowUpIcon sx={{ fontSize: 14 }} />
        </StyleBotton>
        <StyleLabel>{Math.round(scale * 100)}%</StyleLabel>
        <StyleBotton onClick={() => onScaleChange(Math.max(0.5, scale - 0.1))}>
          <KeyboardArrowDownIcon sx={{ fontSize: 14 }} />
        </StyleBotton>
      </OperationNav>
    </Root>
  );
};

export default OperationBar;
