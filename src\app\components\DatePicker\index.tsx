import { useState } from "react";
import { PickerModal } from "mui-daterange-picker-plus/dist";
import type { DateRange } from "mui-daterange-picker-plus/dist";
import dayjs from "dayjs";
import { TextField } from "@mui/material";
import { zhCN } from "date-fns/locale";
import dateRang from "@/assets/date.svg";
import arrive from "@/assets/arrive.svg";
import HighlightOffSharpIcon from "@mui/icons-material/HighlightOffSharp";
const Root = styled("div")(() => ({
  height: "30px",
}));

const PickerModalStyle = styled(PickerModal)(() => ({}));
const Interval = styled("img")(() => ({}));

const TextFieldStyle = styled(TextField)(() => ({
  "&>.MuiInputBase-root": {
    height: "40px",
    paddingLeft: "5px",
    boxSizing: "border-box",
    "& fieldset": {
      borderRadius: "24px",
      border: "1px solid rgba(207, 207, 207, 1)",
    },
    ":hover": {
      border: "red",
      borderRadius: "24px",
      boxShadow: "0px 3px 8px  rgba(29, 90, 246, 0.18)",
    },
  },
}));

const TextFieldItemStyle = styled(TextField)(() => ({
  "& .MuiInputBase-root": {
    "& fieldset": {
      border: "none",
    },
  },
  "& .MuiInputBase-input": {
    padding: 0,
    textAlign: "center",
  },
}));
const InputDiv = styled("div")(() => ({
  height: "100%",
  padding: "0 10px",
  display: "flex",
  alignItems: "center",
  boxSizing: "border-box",
}));

interface Props {
  name: string;
  value: string[];
  onChange: (name: string, value: string[]) => void;
}
const DatePicker: React.FC<Props> = memo(({ onChange, name, value }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLInputElement | null>(null);
  const open = Boolean(anchorEl);
  const [showClearButton, setShowClearButton] = useState(false);

  const PickerChang = (
    date: string | number | Date | dayjs.Dayjs | null | undefined,
    status: "start" | "end",
    format: string = "YYYY-MM-DD HH:mm:ss",
  ) => {
    if (status === "start") {
      return dayjs(date).startOf("day").format(format);
    } else {
      return dayjs(date).endOf("day").format(format);
    }
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const handleSetDateRangeOnChange = (dateRange: DateRange) => {
    const newDateRange = [
      PickerChang(dateRange.startDate, "start"),
      PickerChang(dateRange.endDate, "end"),
    ];
    window.console.log(newDateRange);
    setShowClearButton(false);
    onChange(name, newDateRange);
    handleClose();
  };
  const handleClick = (e: React.MouseEvent<HTMLInputElement>) => {
    setAnchorEl(e.currentTarget);
  };
  // icon清除输入
  const handleClear = (event: { stopPropagation: () => void }) => {
    event.stopPropagation();
    onChange(name, []);
  };
  return (
    <Root>
      <TextFieldStyle
        slotProps={{
          input: {
            onMouseEnter: () => setShowClearButton(true),
            onMouseLeave: () => setShowClearButton(false),
            onClick: handleClick,
            inputComponent: forwardRef(() => (
              <InputDiv>
                <img
                  src={dateRang}
                  alt=""
                  style={{ width: 16, height: 16, marginRight: "8px" }}
                />
                <TextFieldItemStyle
                  autoComplete="off"
                  value={value[0]}
                  placeholder="开始日期"
                />
                <Interval
                  src={arrive}
                  alt=""
                  style={{ width: 14, height: 14 }}
                />
                <TextFieldItemStyle
                  autoComplete="off"
                  value={value[1]}
                  placeholder="结束日期"
                />
                <HighlightOffSharpIcon
                  onClick={handleClear}
                  sx={{
                    cursor: "pointer",
                    color:
                      showClearButton && value[0] && value[1]
                        ? "#ccc"
                        : "transparent",
                    transform: "scale(0.6)",
                  }}
                />
              </InputDiv>
            )),
          },
        }}
      ></TextFieldStyle>
      <PickerModalStyle
        locale={zhCN}
        onChange={(range: DateRange) => handleSetDateRangeOnChange(range)}
        customProps={{
          onCloseCallback: handleClose,
          hideActionButtons: true,
        }}
        hideDefaultRanges
        modalProps={{
          open,
          anchorEl,
          onClose: handleClose,
          anchorOrigin: {
            vertical: "bottom",
            horizontal: "left",
          },
          transformOrigin: {
            vertical: "top",
            horizontal: "left",
          },
          slotProps: {
            paper: {
              sx: {
                borderRadius: "8px",
                boxShadow: "rgba(0, 0, 0, 0.21) 0px 0px 4px",
              },
            },
          },
        }}
      />
    </Root>
  );
});

export default DatePicker;
