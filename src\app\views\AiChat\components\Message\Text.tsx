import { useState, FC, useEffect, useMemo, useRef } from "react";
import MarkdownIt from "markdown-it";
import hljs from "highlight.js";
import Katex from "markdown-it-texmath";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import copy from "copy-to-clipboard";
import "highlight.js/styles/github.css";
import "katex/dist/katex.min.css";
import { CircularProgress, styled } from "@mui/material";
import { RootState, useAppSelector } from "@/hooks";
import { textProps } from "./common";
import { setBreadcrumb } from "@/store/breadcrumbSlice";
import { useDispatch } from "react-redux";
import { createPortal } from "react-dom";

// 1. Outer container - ensures no clipping
const RootContainer = styled("div")({
  position: "relative",
  overflow: "visible",
  width: "100%",
});

// 2. Main content area
const Root = styled("div")(() => ({
  display: "flex",
  padding: "4px 0",
  flexDirection: "column",
  wordBreak: "break-all",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, Segoe UI,Noto Sans, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji",
}));

// 3. Answer area
const AnswerDiv = styled("div")(() => ({
  margin: "4px 4px",
  fontSize: "14px",
  position: "relative",

  // Code block styles
  "& .code-block-header": {
    backgroundColor: "#f2f3f7",
    padding: "3px 15px",
    display: "flex",
    justifyContent: "flex-end",
    fontSize: "12px",
    color: "#ccc",
    borderRadius: "5px 5px 0 0",
  },
  "& .code-block-header__copy": {
    cursor: "pointer",
    marginLeft: "10px",
    userSelect: "none",
    ":hover": {
      color: `rgba(24, 112, 199, 1)`,
    },
  },
  ".hljs": {
    lineHeight: "20px",
    borderRadius: "0 0 5px 5px",
    fontFamily:
      "ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace",
    fontSize: "12px",
  },
  ".code-block-body": {
    backgroundColor: "#fafafc",
  },
}));

// 4. Main text area
const DownloadText = styled("div")(() => ({
  lineHeight: "22px",
  fontSize: "14px",
  position: "relative",

  // Code block wrapper
  ".code-block-wrapper": {
    backgroundColor: "#fff",
  },

  // Citation marker container
  "& .citation-container": {
    position: "relative",
    display: "inline-block",
    marginRight: "5px",

    // Citation marker
    "& .citation": {
      fontSize: "12px",
      display: "inline-block",
      borderRadius: "50%",
      lineHeight: "16px",
      textAlign: "center",
      cursor: "pointer",
      padding: "0 0 3px 0",
      color: "#36c",
    },
  },
}));

// Tooltip styles (will be rendered via portal)
const TooltipContent = styled("div")(() => ({
  position: "fixed",
  background: "#fff",
  width: "300px",
  padding: "10px 15px",
  borderRadius: "16px",
  boxShadow:
    "0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)",

  zIndex: 1200,
  // pointerEvents: "none",
  ".citation-tooltip-style": {
    display: "flex",
    ".index": {
      fontWeight: 600,
    },
    ".content": {
      flex: 1,
      marginLeft: "5px",
      ".text": {
        margin: "5px 0 0 0",
        display: "-webkit-box",
        WebkitLineClamp: 3,
        WebkitBoxOrient: "vertical",
        overflow: "hidden",
        textOverflow: "ellipsis",
        color: "#4e5969",
        maxWidth: "270px",
      },
      ".title": {
        display: "-webkit-box",
        WebkitLineClamp: 2,
        WebkitBoxOrient: "vertical",
        overflow: "hidden",
        textOverflow: "ellipsis",
        fontWeight: 600,
      },
    },
  },
}));

// 5. Thinking text style
const ThinkText = styled("div")(() => ({
  lineHeight: "25px",
  fontSize: "12px",
  color: "rgba(128,128,128)",
  margin: "10px 0 10px 5px",
  paddingLeft: "10px",
  borderLeft: "2px solid #e5e5e5",
}));

// 6. Button style
const ButtonStyle = styled("div")(({ theme }) => ({
  width: "100px",
  display: "flex",
  alignItems: "center",
  cursor: "pointer",
  fontSize: "14px",
  color: theme.palette.primary.main,
  lineHeight: "24px",
}));

// 7. Paper info card style
const PaperInfo = styled("div")(() => ({
  border: "1px solid #e9ebf7",
  padding: "16px",
  borderRadius: "12px",
  fontSize: "14px",
  background: "#fff",
}));

// 8. Paper list style
const PaperInfoList = styled("div", {
  shouldForwardProp: (prop) => prop !== "expandedIndex",
})<{ expandedIndex: boolean }>(({ expandedIndex }) => ({
  margin: "12px 0",
  display: "grid",
  gridTemplateColumns: expandedIndex ? "repeat(3, 1fr)" : "repeat(5, 1fr)",
  gap: "10px",
  flex: 1,
}));

// 9. Paper list container
const PaperInfoListDiv = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
}));

// 10. Paper list item
const PaperInfoListItem = styled("div", {
  shouldForwardProp: (prop) => prop !== "state",
})<{ state?: boolean }>(({ state }) => ({
  padding: "15px 12px",
  background: state ? "#e9ebf7" : "#f4f6fb",
  borderRadius: "12px",
  boxSizing: "border-box",
  fontSize: "14px",
  cursor: "pointer",
  border: state ? "1px solid #3b45e5" : "",
  height: "90px",
}));

// 11. Paper title style
const PaperInfoListItemTitle = styled("div")(() => ({
  display: "-webkit-box",
  WebkitBoxOrient: "vertical",
  WebkitLineClamp: 2,
  overflow: "hidden",
  textOverflow: "ellipsis",
}));

// 12. Article detail style
const ArticleDetail = styled("div")(({ maxWidth }: { maxWidth: number }) => ({
  height: 16,
  maxWidth,
  fontSize: 14,
  lineHeight: 1,
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  marginTop: "10px",
  color: "rgba(64, 64, 64, 0.8)",
}));

interface CitationTooltipProps {
  index: string;
  title: string;
  content: string;
  position: { top: number; left: number };
  onMouseEnter: () => void;
  onMouseLeave: () => void;
}

const CitationTooltip: FC<CitationTooltipProps> = ({
  index,
  title,
  content,
  position,
  onMouseEnter,
  onMouseLeave,
}) => {
  const tooltipRef = useRef<HTMLDivElement>(null);

  return createPortal(
    <TooltipContent
      ref={tooltipRef}
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="citation-tooltip-style">
        <span className="index">{index}.</span>
        <div className="content">
          <div className="title">
            {title.replace(/\|\|\|/g, " ").replace(/#/g, "")}
          </div>
          <div
            className="text markdown-body"
            dangerouslySetInnerHTML={{ __html: content }}
          ></div>
        </div>
      </div>
    </TooltipContent>,
    document.body,
  );
};

const Index: FC<textProps> = ({
  inversion,
  text,
  additionalInfo,
  thinkTexts,
  schemeLoading,
  stopMsg,
  progressTexts,
  itemIndex,
  setExpandedIndex,
  expandedIndex,
  setPaperData,
  count,
  setCurrentPaperItemId,
  onGetBoxHeight,
}) => {
  const { selectedBank } = useAppSelector((state: RootState) => state.counter);
  const [html, setHtml] = useState<string>("");
  const [thinkText, setThinkText] = useState<string>("");
  const [progressText, setProgressText] = useState<string>("");
  const [flag, setFlag] = useState<boolean>(true);
  const [activeTooltip, setActiveTooltip] = useState<{
    index: string;
    title: string;
    content: string;
    position: { top: number; left: number };
  } | null>(null);
  const dispatch = useDispatch();

  // Markdown parser configuration
  const mdi = useMemo(
    () =>
      new MarkdownIt({
        html: true,
        linkify: true,
        highlight(str, lang) {
          if (lang && hljs.getLanguage(lang)) {
            return highlightBlock(
              hljs.highlight(str, { language: lang, ignoreIllegals: true })
                .value,
              lang,
            );
          }
          return highlightBlock(hljs.highlightAuto(str).value, "");
        },
      }),
    [],
  );

  // Add Katex support
  mdi.use(Katex, {
    engine: "katex",
    delimiters: [
      "brackets",
      ["[", "]"],
      "dollars",
      "doxygen",
      "gitlab",
      "julia",
      "kramdown",
      "beg_end",
    ],
    displayMode: true,
  });

  // Highlight code blocks
  const highlightBlock = (str: string, lang?: string) =>
    `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">复制代码</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`;

  // Extract think tag and remaining content
  const extractThinkTagAndRest = (text: string) => {
    const regex = /<think>([\s\S]*?)<\/think>/;
    const match = text.match(regex);
    const thinkContent = match ? match[1] : "";
    const rest = text.replace(regex, "");
    return { thinkContent, rest };
  };

  // Format text and handle citation markers
  const formatNewText = (text: string) => {
    const citationRefs = new Map<string, { title: string; content: string }>();

    const processedText = text.replace(
      /\[\s*(参考资料|参考信息|reference|citation)[-:：](\d+\.\d+)\s*\]/g,
      (_, __, value) => {
        const currentIndex = value.split(".")[0];

        if (additionalInfo) {
          const title = additionalInfo.find(
            (item) => item.index === Number(currentIndex),
          );
          const itemText = title?.paragraphs.find(
            (item: { headId: string }) => item.headId === value,
          );

          if (itemText?.paraText) {
            citationRefs.set(value, {
              title: title?.pdfTitle || "暂无标题",
              content: itemText.paraText,
            });
            return `<span class="citation-container" data-citation="${value}">
            <span class="citation">[${currentIndex}]</span>
          </span>`;
          }
        }
        return "";
      },
    );

    return { processedText, citationRefs };
  };

  // Initialize citation hover events
  useEffect(() => {
    const handleMouseEnter = (e: MouseEvent) => {
      const container = (e.target as HTMLElement).closest(
        ".citation-container",
      );
      if (!container) return;

      // 只处理当前消息的引用
      if (!container.closest(`[data-message-id="${itemIndex}"]`)) return;

      const citationId = container.getAttribute("data-citation");
      if (!citationId) return;

      const currentIndex = citationId.split(".")[0];
      const title = additionalInfo?.find(
        (item) => item.index === Number(currentIndex),
      );
      const itemText = title?.paragraphs.find(
        (item: { headId: string }) => item.headId === citationId,
      );

      if (itemText?.paraText) {
        const rect = container.getBoundingClientRect();
        setActiveTooltip({
          index: currentIndex,
          title: title?.pdfTitle || "暂无标题",
          content: itemText.paraText,
          position: {
            top: rect.top + window.scrollY - 145,
            left: rect.left + window.scrollX - 150,
          },
        });
      }
    };

    const handleMouseLeave = () => {
      setActiveTooltip(null);
    };

    const citationContainers = document.querySelectorAll(
      `[data-message-id="${itemIndex}"] .citation-container`,
    );

    citationContainers.forEach((el: any) => {
      el.addEventListener("mouseenter", handleMouseEnter);
      el.addEventListener("mouseleave", handleMouseLeave);
    });

    return () => {
      citationContainers.forEach((el: any) => {
        el.removeEventListener("mouseenter", handleMouseEnter);
        el.removeEventListener("mouseleave", handleMouseLeave);
      });
    };
  }, [html, additionalInfo, itemIndex]); // 添加itemIndex依赖

  // Parse Markdown content
  useEffect(() => {
    if (thinkTexts) {
      setThinkText(mdi.render(thinkTexts));
      const { processedText } = formatNewText(text);
      setHtml(mdi.render(processedText));
    } else if (progressTexts && !thinkTexts && !text) {
      setProgressText(mdi.render(progressTexts));
    } else {
      const result = extractThinkTagAndRest(text);
      setThinkText(mdi.render(result.thinkContent));
      const { processedText } = formatNewText(result.rest);
      setHtml(mdi.render(processedText));
    }
  }, [text, thinkTexts, progressTexts]);

  // Initialize copy functionality
  useEffect(() => {
    const handleDocumentClick = (event: MouseEvent) => {
      // 1. 处理代码复制
      const copyButton = (event.target as HTMLElement).closest(
        ".code-block-header__copy",
      );
      if (copyButton) {
        event.stopPropagation();
        const codeBlockWrapper = copyButton.closest(".code-block-wrapper");
        const codeBlock = codeBlockWrapper?.querySelector(".code-block-body");
        if (codeBlock) {
          copy(codeBlock.textContent ?? "", {
            debug: true,
            message: "Press #{key} to copy",
          });
        }
        return;
      }

      // 2. 处理引用点击 - 确保只在当前消息内处理
      const citation = (event.target as HTMLElement).closest(".citation");
      if (citation) {
        const container = citation.closest(
          `[data-message-id="${itemIndex}"] .citation-container`,
        );
        if (!container || !additionalInfo) return;

        event.stopPropagation();
        const citationId = container.getAttribute("data-citation");
        if (!citationId) return;

        // 3. 精确查找当前消息的引用数据
        const currentIndex = citationId.split(".")[0];
        const title = additionalInfo.find(
          (item) => item.index === Number(currentIndex),
        );
        const itemText = title?.paragraphs.find(
          (item: { headId: string }) => item.headId === citationId,
        );

        if (itemText) {
          setExpandedIndex(itemIndex);
          setPaperData?.(additionalInfo);
          setCurrentPaperItemId?.(itemText.pdfId);
        }
      }
    };

    document.addEventListener("click", handleDocumentClick);
    return () => document.removeEventListener("click", handleDocumentClick);
  }, [itemIndex, additionalInfo]); // 添加必要依赖

  // Toggle thinking content display
  const handleThink = () => {
    setFlag(!flag);
    onGetBoxHeight && onGetBoxHeight();
  };

  // Click paper title to open details
  const onClickTitle = (id: number) => {
    dispatch(setBreadcrumb([]));
    window.open(
      window.APP_CONFIG.BASE_PATH +
        `/#/paper-search/paper-details/paperbase?id=${id}`,
    );
  };

  // Toggle paper list expansion
  const toggleItem = (index: number) => {
    setExpandedIndex(expandedIndex === index ? null : index);
    setCurrentPaperItemId && setCurrentPaperItemId(0);
    if (additionalInfo?.length && setPaperData) {
      setPaperData(additionalInfo);
    }
  };

  return (
    <RootContainer>
      <Root data-message-id={itemIndex}>
        {!inversion ? (
          <AnswerDiv>
            {progressText && !thinkText && !html && !inversion && (
              <DownloadText
                dangerouslySetInnerHTML={{ __html: progressText }}
                className="markdown-body"
              />
            )}

            {selectedBank.bankType === 1 && count && (
              <PaperInfo>参考{count}篇资料</PaperInfo>
            )}

            {selectedBank.bankType === 1 && additionalInfo?.length ? (
              <PaperInfoListDiv>
                <PaperInfoList expandedIndex={!!expandedIndex}>
                  {additionalInfo
                    .slice(0, expandedIndex ? 3 : 5)
                    .map((item: any, index: number) => (
                      <PaperInfoListItem
                        key={index}
                        onClick={() => onClickTitle(item.pdfId)}
                      >
                        <PaperInfoListItemTitle
                          title={
                            item.pdfTitle
                              ?.replace(/\|\|\|/g, " ")
                              .replace(/#/g, " ") ?? ""
                          }
                        >
                          {item.index}.
                          {item.pdfTitle
                            ? item.pdfTitle
                                .replace(/\|\|\|/g, " ")
                                .replace(/#/g, "")
                            : "暂无标题"}
                        </PaperInfoListItemTitle>
                        {item.authors && item.authors.length !== 0 && (
                          <ArticleDetail
                            maxWidth={150}
                            title={
                              item.authors.map((item: string) =>
                                item === "|||" ? " " : item,
                              ) ?? item.authors.join(";")
                            }
                          >
                            {item.authors.map(
                              (author: string, authorIndex: number) => (
                                <span key={authorIndex}>
                                  {author === "|||" ? " " : author}
                                  {authorIndex === item.authors.length - 1
                                    ? ""
                                    : ";"}
                                </span>
                              ),
                            )}
                          </ArticleDetail>
                        )}
                      </PaperInfoListItem>
                    ))}
                </PaperInfoList>
                <PaperInfoListItem
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    width: "120px",
                    marginLeft: "10px",
                  }}
                  state={itemIndex === expandedIndex}
                  onClick={() => toggleItem(itemIndex)}
                >
                  {itemIndex !== expandedIndex
                    ? `参考${count}篇资料`
                    : "收起资料"}
                </PaperInfoListItem>
              </PaperInfoListDiv>
            ) : null}

            {thinkText && (
              <ButtonStyle onClick={handleThink}>
                思考内容
                {flag ? (
                  <ExpandLessIcon sx={{ ml: 0.5 }} />
                ) : (
                  <ExpandMoreIcon sx={{ ml: 0.5 }} />
                )}
              </ButtonStyle>
            )}

            {flag && thinkText ? (
              <ThinkText
                dangerouslySetInnerHTML={{ __html: thinkText }}
                className="markdown-body"
              />
            ) : null}

            <div
              style={{
                display:
                  schemeLoading && text === "任务生成中，请稍后"
                    ? "flex"
                    : "block",
                alignItems: "center",
                fontSize: "12px",
              }}
            >
              <DownloadText
                dangerouslySetInnerHTML={{ __html: html }}
                className="markdown-body"
              />
              {schemeLoading && text === "任务生成中，请稍后" && (
                <CircularProgress
                  sx={{ color: "#ccc", marginLeft: "10px" }}
                  size="15px"
                />
              )}
            </div>

            {stopMsg && <div>{stopMsg}</div>}
          </AnswerDiv>
        ) : (
          <DownloadText
            sx={{
              padding: 0,
              whiteSpace: "pre-wrap",
              fontSize: "20px",
              fontWeight: 500,
            }}
          >
            {text}
          </DownloadText>
        )}
      </Root>

      {/* Render tooltip outside the main flow using portal */}
      {activeTooltip && (
        <CitationTooltip
          index={activeTooltip.index}
          title={activeTooltip.title}
          content={activeTooltip.content}
          position={activeTooltip.position}
          onMouseEnter={() => setActiveTooltip(activeTooltip)}
          onMouseLeave={() => setActiveTooltip(null)}
        />
      )}
    </RootContainer>
  );
};

export default Index;
