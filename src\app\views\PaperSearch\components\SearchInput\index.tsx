import React, { useMemo, useState } from "react";
import {
  styled,
  Button,
  Paper,
  InputBase,
  IconButton,
  Divider,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { v4 as uuidv4 } from "uuid";
import { anyValueProps } from "../../../../types/common";
import AdvancedSearch from "./AdvancedSearch";
import useSearchRecord, {
  SearchRecordProps,
  SearchTypeProps,
  SearchesProps,
} from "@/hooks/useSearchRecord";
// import SearchSelect from "../../components/SearchSelect";
import { cloneDeep } from "lodash";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import { setAnalysis } from "@/store/counterSlice";
import { getPdfCountStats } from "@/api/paperSearch";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

export const SearchSelectWidth = 260;
export const HEIGHT = 45;

const Root = styled("div")(() => ({
  width: "100%",
  height: HEIGHT,
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  position: "relative",
}));
const StyledPaper = styled(Paper)(() => ({
  flex: 1,
  height: HEIGHT,
  display: "flex",
  alignItems: "center",
  borderRadius: 28,
  marginLeft: 20,
  padding: "0 24px 0 22px",
}));

const SearchButton = styled(Button)(() => ({
  height: 32,
  borderRadius: 28,
  color: "#fff",
  fontSize: 14,
  fontWeight: 700,
  marginLeft: 10,
  padding: "7px 10px",
  boxSizing: "border-box",
  background:
    "linear-gradient(90deg, rgba(110, 84, 227, 1) 0%, rgba(27, 130, 227, 1) 100%) ",
  ".MuiButton-icon": {
    marginLeft: 0,
  },
}));

const StyledIconButton = styled(IconButton)(() => ({
  ":disabled": {
    cursor: " not-allowed",
    pointerEvents: "auto",
  },
}));

export interface SearchInputRefProps {
  getSearchParam: () => SearchRecordProps;
}
interface Props {
  authorCount?: any;
  total?: number;
  keywordsCount?: any;
  setIsSearch?: (value: boolean) => void;
  setPageInfo?: (value: { page: number; pageSize: number }) => void;
}
const SearchInput = React.forwardRef(
  (_: Props, ref: React.ForwardedRef<SearchInputRefProps>) => {
    useImperativeHandle(ref, () => ({
      getSearchParam: () => searchParam,
    }));
    const { minScore, topK } = useAppSelector(
      (state: RootState) => state.search,
    );
    const { searchRecord, onSearchClick } = useSearchRecord();
    const [showAdvanced, setShowAdvanced] = useState(false);
    const dispatch = useAppDispatch();

    const [relevance, setRelevance] = useState(minScore ?? 0.5);
    const [number, setNumber] = useState(topK ?? 20);
    // 检索条件param
    const [searchParam, setSearchParam] =
      useState<SearchRecordProps>(searchRecord);

    useEffect(() => {
      setSearchParam(searchRecord);
    }, [searchRecord]);

    const filterHasContent = useMemo(() => {
      const newData = searchRecord.searches.filter(
        (item) => item.content !== "",
      );

      searchRecord.searches = newData;

      return searchRecord;
    }, [searchRecord]);

    // 校验检索内容是否为空
    const checkObj = (dataObj: SearchRecordProps) => {
      if (
        dataObj.fromYear ||
        dataObj.toYear ||
        dataObj.category ||
        dataObj.query
      ) {
        return false;
      } else {
        for (const item of dataObj.searches) {
          if (item.content && item.content.trim() !== "") {
            return false;
          }
        }
      }
      return true;
    };
    const isBaseSearch = useMemo(() => {
      const flag = checkObj(searchParam);
      if (
        searchParam.searches.length > 1 ||
        searchParam.fromYear ||
        searchParam.toYear
      )
        setShowAdvanced(true);
      return flag;
    }, [searchParam]);

    // 一级检索
    const firstSearchParam = useMemo(
      () => searchParam.query ?? null,
      [searchParam],
    );

    const handleFirstSearchParam = (
      field: string,
      value: string | SearchTypeProps,
    ) => {
      const searches = JSON.parse(JSON.stringify(searchParam));
      searches[field] = value; // 修改检索内容|类型
      setSearchParam(searches);
    };
    const addCondition = (type: string) => {
      switch (type) {
        case "search":
          setSearchParam((prev) => ({
            ...prev,
            searches: [
              ...prev.searches,
              {
                content: "",
                logic: 0, // 	检索逻辑
                type: 0, // 检索类型
                id: uuidv4(),
              },
            ],
          }));
          break;
        case "publishTime":
          setSearchParam((prev) => ({
            ...prev,
            fromYear: "",
            toYear: "",
          }));
          break;
        case "category":
          setSearchParam((prev) => ({
            ...prev,
            category: 2,
          }));
          break;
      }
    };

    const swapYears = (data: anyValueProps) => {
      // 类型校验
      if (
        typeof data?.fromYear !== "number" ||
        typeof data?.toYear !== "number"
      ) {
        return data;
      }

      // 交换逻辑
      return data.fromYear > data.toYear
        ? { fromYear: data.toYear, toYear: data.fromYear }
        : data;
    };
    const changeCondition = (data: anyValueProps) => {
      const swapYearsData = swapYears(data);
      // 对传递进来的对象进行筛选
      const newData = Object.fromEntries(
        Object.entries(swapYearsData).filter(([, value]) => value !== ""),
      );
      let newSearchParam = cloneDeep(searchParam);
      // 检索条件改变
      if (newData.id) {
        const index = newSearchParam.searches.findIndex(
          (item: SearchesProps) => item.id === newData.id,
        );
        if (index !== -1)
          newSearchParam.searches.splice(
            index,
            1,
            swapYearsData as SearchesProps,
          );
      } else {
        // 出版日期 | 学科
        newSearchParam = {
          ...newSearchParam,
          ...newData,
        };
      }
      setSearchParam(newSearchParam);
    };
    // 重置查询条件
    // const resetCondition = () => {
    //   setSearchParam({
    //     query: "",
    //     searches: [
    //       {
    //         content: "",
    //         logic: 0,
    //         type: 0,
    //         id: uuidv4(),
    //       },
    //     ],
    //   });
    //   // message.warning("请输入检索内容");
    // };
    const removeCondition = (type: string, id?: string) => {
      switch (type) {
        case "search":
          setSearchParam((prev) => ({
            ...prev,
            searches: searchParam.searches.filter((item) => item.id !== id),
          }));
          break;
        case "publishTime":
          setSearchParam((prev) => {
            const newSearchParam = { ...prev };
            Reflect.deleteProperty(newSearchParam, "fromYear");
            Reflect.deleteProperty(newSearchParam, "toYear");
            return newSearchParam;
          });
          break;
        case "category":
          setSearchParam((prev) => {
            const newSearchParam = { ...prev };
            Reflect.deleteProperty(newSearchParam, "category");
            return newSearchParam;
          });
          break;
      }
    };

    const handleSearch = () => {
      onSearchClick(searchParam, relevance, number);
      _.setIsSearch && _.setIsSearch(true);
      _.setPageInfo && _.setPageInfo({ page: 1, pageSize: 10 });
    };
    const onAdvancedSearch = () => {
      setShowAdvanced(!showAdvanced);
      // if (isBaseSearch) addCondition("search");
    };

    const init = async (newSearchParam: anyValueProps) => {
      try {
        const params = {
          minScore: minScore ? minScore : 0.5,
          topK: topK ? topK : 20,
          ...newSearchParam,
        };
        const {
          data: { data },
        } = await getPdfCountStats(params);

        const { authorCount, journal, keywords, author } = data;
        dispatch(
          setAnalysis({
            authorCount,
            count: _.total && _.total,
            keywordsCount: keywords,
            relevantAuthor: author.splice(0, 20),
            journalCount: journal.splice(0, 10),
            searchParam: params,
          }),
        );
        window.open(
          `${window.APP_CONFIG.BASE_PATH}/#/paper-search/analysis-results/home`,
        );
      } catch (error) {
        message.error("获取检索结果失败，" + (error as Error)?.message);
      }
    };
    // 跳转分析页
    const handleToAnalysis = () => {
      init(filterHasContent);
    };

    return (
      <Root onKeyDown={(e) => e.key === "Enter" && handleSearch()}>
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: "100%",
            height: "100%",
          }}
        >
          {/* <SearchSelect
            sx={{ pl: "30px" }}
            width={SearchSelectWidth}
            value={firstSearchParam?.type || 0}
            optionMap={searchTypeMap}
            onChange={(value) => {
              handleFirstSearchParam("type", Number(value));
            }}
          /> */}
          <StyledPaper>
            <InputBase
              sx={{
                ml: "14px",
                flex: 1,
                "& .Mui-focused": {
                  outline: "none", // 去掉聚焦时的边框（可选）
                  borderColor: "primary.main", // 聚焦时边框颜色（如果需要）
                },
              }}
              placeholder=""
              value={firstSearchParam}
              onChange={(e) => handleFirstSearchParam("query", e.target.value)}
            />
            {firstSearchParam && (
              <StyledIconButton
                type="button"
                aria-label="clear"
                sx={{
                  "& .MuiSvgIcon-root": {
                    width: 16,
                    height: 16,
                  },
                }}
                onClick={() => handleFirstSearchParam("query", "")}
              >
                <ClearIcon />
              </StyledIconButton>
            )}
            <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
            <StyledIconButton
              aria-label="search"
              disabled={isBaseSearch}
              sx={{ color: isBaseSearch ? "#ccc" : "rgb(0, 104, 177)" }}
              onClick={handleSearch}
            >
              <SearchIcon />
            </StyledIconButton>
          </StyledPaper>
          <SearchButton
            variant="contained"
            onClick={onAdvancedSearch}
            endIcon={showAdvanced ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          >
            自定义检索
          </SearchButton>
          {_.authorCount && (
            <SearchButton
              variant="contained"
              onClick={handleToAnalysis}
              disabled={_.total ? false : true}
              sx={{
                background: _.total
                  ? "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)"
                  : "none",
              }}
            >
              分析
            </SearchButton>
          )}
        </Box>

        {showAdvanced && (
          <AdvancedSearch
            searchParam={searchParam}
            addCondition={addCondition}
            changeCondition={changeCondition}
            removeCondition={removeCondition}
            // resetCondition={resetCondition}
            setIsSearch={_.setIsSearch}
            setPageInfo={_.setPageInfo}
            setRelevance={setRelevance}
            setNumber={setNumber}
            relevance={relevance}
            number={number}
          />
        )}
      </Root>
    );
  },
);

export default SearchInput;
