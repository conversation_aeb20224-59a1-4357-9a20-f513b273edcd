// 知识库API
import axios from "axios";
// 获取知识库
const getKnowledgeAll = (params: any) =>
  axios({
    url: `/kb/list-all`,
    method: "post",
    data: params,
  });

// 禁用知识库
const disableKnowledge = (id: number) =>
  axios({
    url: `/kb/disable`,
    method: "post",
    data: { id },
  });

// 创建知识库
const createKnowledge = (params: {
  kb_name: string;
  kb_version: string;
  kb_description: string;
}) =>
  axios({
    url: `/kb/create`,
    method: "post",
    data: {
      kb_name: params.kb_name,
      kb_version: params.kb_version,
      kb_description: params.kb_description,
    },
  });

// 更新知识库
const updateKnowledge = (params: {
  kb_name: string;
  kb_version: string;
  kb_description: string;
  id: number;
}) =>
  axios({
    url: `/kb/update`,
    method: "post",
    data: {
      kb_name: params.kb_name,
      kb_version: params.kb_version,
      kb_description: params.kb_description,
      id: params.id,
    },
  });
// 删除知识库
const deleteKnowledgeApi = (id: number) =>
  axios({
    url: `/kb/delete`,
    method: "post",
    data: { id },
  });
// 分页查询课题组
/**
 *
 * @param params {
 *  page: number;
 *  size: number;
 *  isAll?: boolean; // 是否查询所有(包含禁用课题组)
 * }
 */
const queryGroupList = (params: any) =>
  axios.get(`/api/auth/groupList`, { params });

export {
  getKnowledgeAll,
  disableKnowledge,
  createKnowledge,
  updateKnowledge,
  deleteKnowledgeApi,
  queryGroupList,
};
