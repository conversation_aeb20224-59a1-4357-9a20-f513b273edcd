import React from "react";
import { Box, Paper, SvgIcon } from "@mui/material";
import ArticleItem from "./ArticleItem";

interface Props {
  item: any;
}

const RelatedRecommend: React.FC<Props> = ({ item }) => (
  <Paper
    sx={{
      display: "flex",
      justifyContent: "center",
      borderRadius: "20px",
      // height: 200,
      boxSizing: "border-box",
      p: "24px 39px 21px",
      width: "100%",
    }}
  >
    <SvgIcon sx={{ width: 16, height: 18 }}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="18"
        viewBox="0 0 16 18"
        fill="none"
      >
        <path
          d="M10.6263 2.75121C10.6263 4.06846 11.7599 5.14012 13.154 5.14012L13.154 5.1401L16 5.1401L16 16.5468C16 17.3495 15.3113 18 14.462 18L1.5384 18C0.688709 18 0 17.3495 0 16.5468L0 1.45424C0 0.651501 0.688673 0 1.5384 0L10.6263 0L10.6263 2.75121ZM11.6155 2.75121L11.6155 3.52349e-05L16 4.20446L13.154 4.20446C12.3042 4.20446 11.6155 3.55395 11.6155 2.75121ZM2.53738 8.52759L13.462 8.52759L13.462 7.5534L2.53738 7.5534L2.53738 8.52759ZM2.53738 11.6451L13.462 11.6451L13.462 10.671L2.53738 10.671L2.53738 11.6451ZM2.53738 14.7627L13.462 14.7627L13.462 13.7885L2.53738 13.7885L2.53738 14.7627Z"
          fillRule="evenodd"
          fill="#0068B1"
        ></path>
      </svg>
    </SvgIcon>
    <Box
      sx={{
        flex: 1,
        ml: "10px",
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        width: 0,
      }}
    >
      <Box sx={{ flex: 1 }}>
        <ArticleItem articleInfo={item} addArt={false} addAi={false} />
      </Box>
    </Box>
  </Paper>
);

export default RelatedRecommend;
