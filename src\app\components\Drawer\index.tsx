import React, {
  forwardRef,
  useRef,
  useImperative<PERSON>andle,
  useEffect,
} from "react";
import { SwipeableDrawer, Button, styled } from "@mui/material";
import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";

type Anchor = "left" | "right";

interface Props {
  anchor?: Anchor;
  open?: boolean;
  setOpen: (value: boolean) => void;
  width?: number;
  title?: React.ReactNode;
  children?: React.ReactNode;
  getHeight?: (value: number) => void;
  drawerClose?: () => void;
}

interface HeaderProps {
  title?: React.ReactNode;
  onClose?: () => void;
}

interface ContentProps {
  children?: React.ReactNode;
  getContentHeight?: (value: number) => void;
}

const DrawerRoot = styled("div")<{ width: number }>(({ width }) => ({
  width: `${width}px`,
  height: "100%",
  display: "flex",
  flexDirection: "column",
  boxSizing: "border-box",
  overflow: "hidden",
}));

const DrawerHeader = styled("div")(() => ({
  width: "100%",
  display: "flex",
  alignItems: "center",
  padding: "16px 24px",
  fontSize: "16px",
  lineHeight: 1.5,
  borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
  boxSizing: "border-box",
}));

const DrawerHeaderTitle = styled("div")(() => ({
  display: "flex",
  flex: 1,
  alignItems: "center",
  minHeight: 0,
  minWidth: 0,
}));

const DrawerHeaderClose = styled(Button)(({ theme }) => ({
  minWidth: 0,
  padding: 0,
  color: "#000",
  marginRight: theme.spacing(2),
}));

const DrawerHeaderTitleContent = styled("span")(() => ({
  flex: 1,
  margin: 0,
  fontWeight: 600,
  fontSize: "16px",
  lineHeight: 1.5,
}));

const DrawerContent = styled("div")(() => ({
  width: "100%",
  flex: 1,
  overflowY: "auto",
  overflowX: "hidden",
  boxSizing: "border-box",
}));

const Header = (props: HeaderProps) => {
  const { title, onClose } = props;
  return (
    <DrawerHeader>
      <DrawerHeaderTitle>
        <DrawerHeaderClose onClick={onClose}>
          <CloseOutlinedIcon sx={{ fontSize: "24px" }} />
        </DrawerHeaderClose>
        <DrawerHeaderTitleContent>{title}</DrawerHeaderTitleContent>
      </DrawerHeaderTitle>
    </DrawerHeader>
  );
};

const Content = forwardRef((props: ContentProps, ref) => {
  const { children, getContentHeight } = props;
  const infoRef = useRef<any>(null);

  const getHeight = () => {
    getContentHeight && getContentHeight(infoRef.current?.offsetHeight);
  };

  useImperativeHandle(ref, () => ({
    getHeight,
  }));

  return <DrawerContent ref={infoRef}>{children}</DrawerContent>;
});

const Drawer: React.FC<Props> = ({
  anchor = "right",
  open,
  setOpen,
  width = 350,
  children,
  title = "",
  getHeight,
  drawerClose,
}) => {
  const contentRef = useRef<any>(null);
  const onClose = () => {
    setOpen(false);
  };

  const onOpen = () => {
    setOpen(true);
  };

  const getRef = () => {
    if (!contentRef.current) return;
    contentRef.current.getHeight();
  };
  useEffect(() => {
    const timer = setTimeout(() => {
      getRef();
    }, 500);
    window.addEventListener("resize", getRef);
    return () => {
      window.removeEventListener("resize", getRef);
      clearTimeout(timer);
    };
  }, []);

  return (
    <SwipeableDrawer
      anchor={anchor}
      open={open}
      onClose={drawerClose ? drawerClose : onClose}
      onOpen={onOpen}
    >
      <DrawerRoot width={width}>
        <Header title={title} onClose={drawerClose ? drawerClose : onClose} />
        <Content
          ref={contentRef}
          children={children}
          getContentHeight={getHeight}
        />
      </DrawerRoot>
    </SwipeableDrawer>
  );
};

export default Drawer;
