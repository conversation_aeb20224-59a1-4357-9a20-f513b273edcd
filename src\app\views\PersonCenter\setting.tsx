import { FormColumnProps, RefProps } from "@/components/DynamicForm";
import * as yup from "yup";

export const positionArray = [
  {
    label: "教授",
    value: "professor",
  },
  {
    label: "研究员",
    value: "researcher",
  },
  {
    label: "副教授",
    value: "associate_professor",
  },
  {
    label: "助理教授",
    value: "assistant_professor",
  },
  {
    label: "博士后",
    value: "post-doctoral",
  },
  {
    label: "博士生",
    value: "doctor",
  },
  {
    label: "研究生",
    value: "graduate",
  },
  {
    label: "本科生",
    value: "undergraduate",
  },
  {
    label: "工程师",
    value: "engineer",
  },
  {
    label: "其他",
    value: "other",
  },
];

export const subjectArray = [
  {
    label: "计算机科学",
    value: "computer_science",
  },
  {
    label: "通信与信息工程",
    value: "communication_and_information_engineering",
  },
  {
    label: "数学",
    value: "math",
  },
  {
    label: "物理学",
    value: "physics",
  },
  {
    label: "核科学与技术",
    value: "nuclear_science_and_technology",
  },
  {
    label: "化学",
    value: "chemistry",
  },
  {
    label: "医学",
    value: "medicine",
  },
  {
    label: "临床医学",
    value: "clinical_medicine",
  },
  {
    label: "药学",
    value: "pharmacy",
  },
  {
    label: "光学",
    value: "optics",
  },
  {
    label: "生物学",
    value: "biology",
  },
  {
    label: "免疫与微生物学",
    value: "immunology_and_microbiology",
  },
  {
    label: "神经科学",
    value: "neuroscience",
  },
  {
    label: "生物医学工程",
    value: "biomedical_engineering",
  },
  {
    label: "地理学",
    value: "geography",
  },
  {
    label: "地质学",
    value: "geology",
  },
  {
    label: "地球物理学",
    value: "earth_physics",
  },
  {
    label: "地质工程",
    value: "geological_engineering",
  },
  {
    label: "矿业",
    value: "mining",
  },
  {
    label: "石油工程",
    value: "oil_engineering",
  },
  {
    label: "海洋工程",
    value: "ocean_engineering",
  },
  {
    label: "冶金工程",
    value: "mechanical_engineering",
  },
  {
    label: "电气工程",
    value: "electrical_engineering",
  },
  {
    label: "交通运输",
    value: "transportation",
  },
  {
    label: "机械工程",
    value: "mechanical_engineering",
  },
  {
    label: "天文学",
    value: "astronomy",
  },
  {
    label: "航空航天工程",
    value: "aerospace_engineering",
  },
  {
    label: "仪器科学与技术",
    value: "instrumentation_and_technology",
  },
  {
    label: "食品科学与技术",
    value: "food_science_and_technology",
  },
  {
    label: "建筑学",
    value: "architecture",
  },
  {
    label: "农业工程",
    value: "agriculture_engineering",
  },
  {
    label: "林学",
    value: "landscape",
  },
  {
    label: "环境科学与工程",
    value: "environmental_science_and_engineering",
  },
  {
    label: "经济学",
    value: "economics",
  },
  {
    label: "社会学",
    value: "social_science",
  },
  {
    label: "心理学",
    value: "psychology",
  },
  {
    label: "管理学",
    value: "management",
  },
  {
    label: "教育学",
    value: "education",
  },
  {
    label: "体育学",
    value: "sports",
  },
  {
    label: "历史学",
    value: "history",
  },
];

export const BasicColumns: FormColumnProps[] = [
  {
    name: "username",
    label: "用户名",
    componentType: "input",
    required: true,
    placeholder: "请输入用户名/邮箱",
    grid: 12,
    styleProps: {
      height: 38,
    },
    labelStyleProps: {
      fontSize: 14,
      fontWeight: 400,
      color: "rgba(0, 0, 0, 1)",
    },
  },
  {
    name: "name",
    label: "姓名",
    componentType: "input",
    placeholder: "请输入姓名",
    grid: 12,
    styleProps: {
      height: 38,
    },
    labelStyleProps: {
      fontSize: 14,
      fontWeight: 400,
      color: "rgba(0, 0, 0, 1)",
    },
  },
  {
    name: "phone",
    label: "电话",
    componentType: "input",
    placeholder: "请输入电话号码",
    grid: 12,
    styleProps: {
      height: 38,
    },
    labelStyleProps: {
      fontSize: 14,
      fontWeight: 400,
      color: "rgba(0, 0, 0, 1)",
    },
  },
  {
    name: "gender",
    label: "性别",
    componentType: "radio-group",
    options: [
      { label: "男", value: "男" },
      { label: "女", value: "女" },
      { label: "保密", value: "保密" },
    ],
    // defaultValue: "male",
    grid: 12,
    styleProps: {
      height: 38,
    },
    labelStyleProps: {
      fontSize: 14,
      fontWeight: 400,
      color: "rgba(0, 0, 0, 1)",
    },
  },
  {
    name: "position",
    label: "职位",
    componentType: "select",
    placeholder: "请选择职位",
    options: positionArray,
    grid: 12,
    styleProps: {
      height: 38,
    },
    labelStyleProps: {
      fontSize: 14,
      fontWeight: 400,
      color: "rgba(0, 0, 0, 1)",
    },
  },
  {
    name: "subject",
    label: "学科",
    componentType: "select",
    placeholder: "请选择学科",
    options: subjectArray,
    grid: 12,
    styleProps: {
      height: 38,
    },
    labelStyleProps: {
      fontSize: 14,
      fontWeight: 400,
      color: "rgba(0, 0, 0, 1)",
    },
  },
  {
    name: "company",
    label: "单位",
    componentType: "input",
    placeholder: "请输入单位名称",
    grid: 12,
    styleProps: {
      height: 38,
    },
    labelStyleProps: {
      fontSize: 14,
      fontWeight: 400,
      color: "rgba(0, 0, 0, 1)",
    },
  },
];

const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export const EmailColumns: FormColumnProps[] = [
  {
    name: "email",
    label: "邮箱号",
    componentType: "input",
    required: true,
    placeholder: "请输入邮箱",
    grid: 12,
    validation: yup
      .string()
      .required("请输入邮箱")
      .matches(emailRegex, "请输入有效的邮箱"),
    styleProps: {
      height: 38,
    },
    labelStyleProps: {
      fontSize: 14,
      fontWeight: 400,
      color: "rgba(0, 0, 0, 1)",
    },
  },
];

export interface InfoOptionsProps {
  title: string;
  type: string;
  columns: FormColumnProps[];
}

export const InfoOptions: InfoOptionsProps[] = [
  {
    title: "基本信息",
    type: "basic",
    columns: BasicColumns,
  },
  {
    title: "账号信息",
    type: "email",
    columns: EmailColumns,
  },
];

export interface FormRefProps {
  [key: string]: RefProps | any;
}
