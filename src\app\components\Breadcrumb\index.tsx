import React from "react";
import { styled } from "@mui/material";
import { useNavigate } from "react-router-dom";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
const Root = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  fontSize: 16,
  height: 54,
  opacity: 1,
  width: "100%",
  boxSizing: "border-box",
  background: "rgba(255, 255, 255, 0.3)",
  paddingLeft: 41,
}));

const BreadcrumbItem = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
}));

const BreadcrumbActivePart = styled("div")(() => ({
  color: "rgba(31, 31, 31, 1);",
  cursor: "pointer",
  display: "inline-block",
}));

const BreadcrumbCurrent = styled("div")(() => ({
  background: "rgba(29, 90, 246, 1)",
  backgroundClip: "text" /* 其他浏览器 */,
  color: "transparent",
  WebkitBackgroundClip: "text",
}));

type parentItemProps = { path?: string; name: string; state?: any };

type BreadcrumbProps = {
  parent: parentItemProps[];
  current: string;
};
const Breadcrumb: React.FC<BreadcrumbProps> = ({ parent, current }) => {
  const history = useNavigate();

  const backToParent = (path: string | undefined, state: any) => {
    window.console.log("back to parent", path, state);
    if (path) history(path, { state });
  };

  return (
    <Root>
      {parent.map((parentItem, parentIndex) => (
        <BreadcrumbItem key={`parent-${parentIndex}`}>
          <BreadcrumbActivePart
            onClick={() => backToParent(parentItem.path, parentItem.state)}
          >
            {parentItem.name}
          </BreadcrumbActivePart>
          <ChevronRightIcon sx={{ transform: "scale(0.7)", mx: 1 }} />
        </BreadcrumbItem>
      ))}
      <BreadcrumbCurrent>{current}</BreadcrumbCurrent>
    </Root>
  );
};

export default Breadcrumb;
