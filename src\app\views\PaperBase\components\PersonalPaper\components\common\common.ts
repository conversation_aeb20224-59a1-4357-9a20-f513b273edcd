import { pdfjs } from "react-pdf";
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/legacy/build/pdf.worker.min.js",
  import.meta.url,
).toString();
export type BreadcrumbType = {
  name: string;
  path: string;
};

export interface SearchProps {
  sort: string;
  source?: string;
  search?: string;
}

export interface MyPaginationProps {
  asc?: boolean;
  category?: string;
  page: number;
  size: number;
  sort?: string;
  source?: string;
}

type PdfFile = {
  fileName: string;
  pageNum: number;
};

export const checkPdfPage = (
  file: BlobPart,
  fileName: string,
): Promise<PdfFile> =>
  new Promise((resolve, reject) => {
    const blob = new Blob([file], { type: "application/pdf" });
    const reader = new FileReader();
    reader.readAsArrayBuffer(blob);
    reader.onloadend = async () => {
      try {
        const arrayBuffer: any = reader.result;
        const loadingTask = pdfjs.getDocument(arrayBuffer);
        const pdf = await loadingTask.promise;
        const pdfPageNum = pdf.numPages;
        resolve({ fileName, pageNum: pdfPageNum });
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = (error) => {
      reject(error);
    };
  });

export const concatenateFileNames = (pdfFiles: PdfFile[]): string =>
  pdfFiles
    .filter((file) => file.pageNum > 100)
    .map((file) => file.fileName)
    .join("、");

export const editFrom = [
  {
    keyword: "title",
    label: "标题",
    type: "Input",
    required: false,
    placeholder: "请输入标题",
  },
  {
    keyword: "doi",
    label: "DOI",
    type: "Input",
    required: false,
    placeholder: "请输入doi",
  },
  {
    keyword: "author",
    label: "作者",
    type: "Input",
    required: false,
    placeholder: "请输入作者",
  },
  {
    keyword: "journal",
    label: "期刊",
    type: "Input",
    required: false,
    placeholder: "请输入期刊",
  },
  {
    keyword: "publishedYear",
    label: "出版年",
    type: "input",
    required: false,
    placeholder: "请输入出版年",
    // inputProps: {
    //   type: "number",
    //   endadornment: null,
    // },
  },
];
