import React, {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
} from "react";
import { Uppy } from "@uppy/core";
import zh_CN from "@uppy/locales/lib/zh_CN";
import { styled } from "@mui/material";
import { Dashboard } from "@uppy/react";
import "@uppy/core/dist/style.css";
import "@uppy/drag-drop/dist/style.css";
import "@uppy/dashboard/dist/style.css";

export interface limitProps {
  maxFileSize?: number;
  minFileSize?: number;
  maxTotalFileSize?: number;
  maxNumberOfFiles?: number;
  minNumberOfFiles?: number;
  allowedFileTypes?: string[];
}

interface Props {
  limit?: limitProps;
  uploadFile?: (file: any) => void;
  changeFile?: (files: any) => void;
}

const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  "& .uppy-Dashboard-poweredBy": {
    display: "none",
  },
  "& .uppy-Container": {
    flexGorw: 1,
    height: "100%",
  },
  "& .uppy-Root": {
    height: "100%",
  },
  "& .uppy-Dashboard": {
    height: "100% !important",
  },
  "& .uppy-Dashboard-inner": {
    width: "100% !important",
    height: "100% !important",
  },
  "& .uppy-Dashboard-AddFiles-title": {
    width: "100%",
    height: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  "& .uppy-DashboardContent-bar": {
    zIndex: 1,
  },
  "& .uppy-StatusBar": {
    zIndex: 1,
  },
}));

const UppyUpload = forwardRef((props: Props, ref) => {
  const { uploadFile, changeFile, limit } = props;
  const uppyRef = useRef<Uppy | null>(null);
  if (!uppyRef.current) {
    uppyRef.current = new Uppy({
      restrictions: {
        ...limit,
      },
      locale: {
        pluralize() {
          return 0;
        },
        strings: {
          ...zh_CN.strings,
          browse: "点击上传",
          browseFiles: "点击上传",
          aggregateExceedsSize:
            "选择的文件总大小%{size}，但允许的最大总大小为%{sizeAllowed}",
        },
      },
    });
  }

  const uploadFiles = async (file: any) => {
    try {
      const { successful } = await file;
      uploadFile && uploadFile(successful);
    } catch (error) {
      uppyRef.current?.info({
        message: `上传失败: ${error}`,
        details: "error",
      });
    }
  };

  // 文件改变——增加 | 减少
  const fileChange = () => {
    const files = uppyRef.current?.getFiles();
    changeFile && changeFile(files);
  };

  const onFileAdd = (file: any) => {
    if (!uppyRef.current) return;
    uppyRef.current.setFileMeta("file", file);
  };
  // 可外部触发重置
  const reset = () => {
    if (!uppyRef.current) return;
    uppyRef.current.cancelAll();
  };

  useImperativeHandle(ref, () => ({
    reset,
  }));

  useEffect(() => {
    const uppy = uppyRef.current;
    if (!uppy) return;
    uppy.on("file-added", onFileAdd);
    uppy.on("files-added", () => fileChange());
    uppy.on("file-removed", () => fileChange());
    uppy.on("complete", (file) => uploadFiles(file));
    return () => {
      uppy.off("file-added", onFileAdd);
      uppy.off("files-added", () => fileChange());
      uppy.off("file-removed", () => fileChange());
      uppy.off("complete", (file) => uploadFiles(file));
    };
  }, []);
  return (
    <Root>
      <Dashboard
        uppy={uppyRef.current}
        hideUploadButton={true}
        showProgressDetails={true}
      />
    </Root>
  );
});

export default memo(UppyUpload);
