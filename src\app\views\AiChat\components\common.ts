import { FormColumnProps } from "@/components/DynamicForm";
export interface tabListProps {
  name: string;
  key: number;
}

export interface leftHistoryListProps {
  id: string;
  uid: string;
  name: string;
  type: 0 | 1 | 2 | 3;
  externalIds: string;
  topped: number;
  note: null;
  lastChatTime: string;
  createTime: string;
  updateTime: string;
  deleted: number;
  temperature: number;
  retrievalThreshold: number;
  status: number;
}
const tabList: tabListProps[] = [
  {
    name: "资料库",
    key: 1,
  },
  // {
  //   name: "实验数据库",
  //   key: 3,
  // },
  {
    name: "单/多资料",
    key: 2,
  },
];

const operateList = [
  { key: "del", name: "删除对话" },
  { key: "copy", name: "复制对话" },
  { key: "edit", name: "重命名" },
  { key: "topUp", name: "置顶对话", cancelName: "取消置顶" },
  { key: "add", name: "添加资料" },
];

export const paperFormColumns: FormColumnProps[] = [
  {
    name: "fuzzyQuery",
    label: "关键词查询",
    componentType: "input",
    grid: 12,
    placeholder: "请输入关键词",
  },
];
export { tabList, operateList };
