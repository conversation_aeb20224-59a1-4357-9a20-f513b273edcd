import { anyValueProps } from "@/types/common";
import AddPaperBaseDialog from "@/views/PaperBase/components/PersonalPaper/components/PersonalPaperDialog/AddPaperBaseDialog";
import {
  Checkbox,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Popover,
} from "@mui/material";
import FolderIcon from "@mui/icons-material/Folder";
import { useQuery } from "@tanstack/react-query";
import Dialog from "@/components/Dialog";
import { getShareList, sharePaperBase } from "@/api/personalpaper";

interface AnchorOriginProps {
  vertical: "top" | "bottom";
  horizontal: "left" | "right";
}

interface PaperPopoverIProps {
  anchorEl: HTMLButtonElement | null;
  id?: "simple-popover" | undefined;
  open: boolean;
  onClose: () => void;
  documentId: string;
  anchorOrigin?: AnchorOriginProps;
  transformOrigin?: AnchorOriginProps;
}

const Root = styled("div")(() => ({
  width: 350,
  maxHeight: 500,
}));

const PopoverHeader = styled("div")(() => ({
  width: "100%",
  height: 40,
  background: "#f5f5f5",
  fontSize: 20,
  fontWeight: 600,
  lineHeight: "40px",
  paddingLeft: "10px",
}));

const PopoverContent = styled("div")(() => ({
  width: "100%",
  // maxHeight: "calc(100% - 120px)",
  height: 300,
  overflow: "auto",
  background: "#fff",
  padding: "10px",
  boxSizing: "border-box",
}));

const EmptyTypography = styled(Typography)(() => ({
  width: "100%",
  textAlign: "center",
}));

const PopoverFooter = styled("div")(() => ({
  width: "100%",
  // height: 90,
  background: "#f5f5f5",
  padding: "5px 10px",
  boxSizing: "border-box",
}));

const ButtonGroup = styled("div")(() => ({
  display: "flex",
  justifyContent: "flex-end",
  marginTop: 5,
}));

const StyledList = styled(List)(() => ({
  height: "100%",
  // overflow: "auto",
}));

const StyledListItemButton = styled(ListItemButton)(() => ({
  paddingTop: 2,
  paddingBottom: 2,
  paddingLeft: 0,
}));

const StyledListItemIcon = styled(ListItemIcon)(() => ({
  minWidth: "30px",
}));

const StyledCheckbox = styled(Checkbox)(({ theme }) => ({
  padding: 0,
  margin: `${theme.spacing()} ${theme.spacing()} ${theme.spacing()} 0`,
}));

const ListItemTextInfo = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
}));

const ContentBox = styled("div")(() => ({
  width: "100%",
  height: 80,
  display: "flex",
  // flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
}));

const GroupPopover: React.FC<PaperPopoverIProps> = (props) => {
  const {
    id,
    open,
    onClose,
    anchorEl,
    documentId,
    anchorOrigin = { vertical: "bottom", horizontal: "left" },
    transformOrigin,
  } = props;
  const [list, setList] = useState<any[]>([]);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [oldStatus, setOldStatus] = useState<boolean>(false);
  const [messageDialogOpen, setMessageDialogOpen] = useState<boolean>(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const [viewNum, setViewNum] = useState(5);
  // const userRole = useAppSelector((state) => state.user.roleOption);
  const queryRequest = async () => {
    const response = await getShareList({ documentId });
    return response.data;
  };

  const { data, status, refetch, error } = useQuery({
    queryKey: ["getShareList"],
    queryFn: queryRequest,
  });

  const handleScroll = (total: number) => {
    window.console.log(total, "total");
    if (scrollRef.current) {
      const scrollTop = scrollRef.current.scrollTop;
      const scrollHeight = scrollRef.current.scrollHeight;
      const clientHeight = scrollRef.current.clientHeight;
      if (scrollTop + clientHeight >= scrollHeight) {
        if (viewNum + 10 < total) {
          setViewNum((prev) => prev + 10);
          window.console.log("加载中");
        } else {
          setViewNum(total);
          window.console.log("到底了");
        }
      }
    }
  };
  const moveExistTrueToFront = (arr: any) =>
    arr.sort((a: any, b: any) => (b.exist ? 1 : 0) - (a.exist ? 1 : 0));

  useEffect(() => {
    switch (status) {
      case "success": {
        setList(moveExistTrueToFront(data.data));
        setOldStatus(
          data.data.length !== 0
            ? data.data.every((item: any) => item.exist === false)
            : false,
        );
        setViewNum(data.data.length < 10 ? data.data.length : 10);
        break;
      }
      case "error":
        message.error("获取课题组列表失败" + error.message);
        break;
      default:
        break;
    }
    window.console.log(scrollRef.current, "scrollRef.current");
    scrollRef.current?.addEventListener("scroll", () =>
      handleScroll(data.data.length),
    );
    return () => {
      scrollRef.current?.removeEventListener("scroll", () =>
        handleScroll(data.data.length),
      );
    };
  }, [data, status, open, scrollRef.current]);

  // const handleAddClick = () => {
  //   if (userRole.resourceCode) {
  //     setDialogOpen(true);
  //   } else {
  //     message.warning("请先选择课题组");
  //   }
  // };

  const saveLiterature = async (existFlag?: boolean) => {
    try {
      const addList = list
        .filter((item) => item.exist)
        .map((item) => String(item.resourceCode));

      if (existFlag) {
        handleClose();
        return 0;
      }
      const params = {
        resourceCodeList: String(addList),
        documentId,
      };
      const { data } = await sharePaperBase(params);
      if (data.code === 200) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      message.error("分享资料库失败" + error);
      return false;
    }
  };

  const handleSubmit = async () => {
    const IndexStatus = list.every((item) => item.exist === false);
    if (!oldStatus && IndexStatus) {
      setMessageDialogOpen(true);
      return;
    } else {
      const flag = await saveLiterature(IndexStatus);
      if (flag === true) {
        handleClose();
        message.success("分享成功");
      } else if (flag === false) {
        message.error("分享失败");
      } else {
        return;
      }
    }
  };

  const handleCheck = (item: anyValueProps, index: number) => {
    const itemTemp = { ...item, exist: item.exist ? !item.exist : true };
    const collectListTemp = [...list];
    collectListTemp[index] = itemTemp;
    setList(collectListTemp);
  };

  const handleClose = () => {
    setList([]);
    onClose();
    setViewNum(5);
  };

  const submit = async () => {
    const flag = await saveLiterature();
    if (flag) {
      handleClose();
      message.success("取消分享成功");
    } else {
      message.error("取消分享失败");
    }
    setMessageDialogOpen(false);
  };

  return (
    <Popover
      id={id}
      open={open}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={anchorOrigin}
      transformOrigin={transformOrigin}
    >
      <Root>
        <PopoverHeader>选择课题组</PopoverHeader>
        <PopoverContent ref={scrollRef}>
          {list.length > 0 && (
            <StyledList>
              {list.slice(0, viewNum).map((item, index) => (
                <ListItem key={item.id} disablePadding>
                  <StyledListItemButton
                    onClick={() => handleCheck(item, index)}
                  >
                    <StyledListItemIcon>
                      <StyledCheckbox edge="start" checked={item.exist} />
                    </StyledListItemIcon>
                    <ListItemText>
                      <ListItemTextInfo>
                        <FolderIcon
                          fontSize="small"
                          sx={{
                            mr: 1,
                            color: item.exist ? "rgba(0, 104, 177)" : "gray",
                          }}
                        />
                        {item.groupName}
                      </ListItemTextInfo>
                    </ListItemText>
                  </StyledListItemButton>
                </ListItem>
              ))}
            </StyledList>
          )}
          {list.length === 0 && <EmptyTypography>暂无课题组</EmptyTypography>}
        </PopoverContent>
        <PopoverFooter>
          <ButtonGroup>
            <Button
              variant="outlined"
              sx={{ mr: 1, height: 33 }}
              onClick={handleClose}
            >
              取消
            </Button>
            <Button
              variant="contained"
              sx={{ height: 33 }}
              onClick={handleSubmit}
            >
              确定
            </Button>
          </ButtonGroup>
        </PopoverFooter>
        <AddPaperBaseDialog
          open={dialogOpen}
          setOpen={setDialogOpen}
          reload={refetch}
          type="add"
        />
        <Dialog
          setDialogOpen={setMessageDialogOpen}
          open={messageDialogOpen}
          title="提示"
          okButtonProps={{ onOk: submit }}
        >
          <ContentBox slot="content">
            <span>是否需要取消分享?</span>
          </ContentBox>
        </Dialog>
      </Root>
    </Popover>
  );
};
export default GroupPopover;
