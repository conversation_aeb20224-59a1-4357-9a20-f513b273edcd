// 1. 定义数据结构
interface Document {
  id: number;
  type: string;
  name: string;
  userId: string;
  defaultState: boolean;
  version: string;
  note: string;
  createTime: string;
  updateTime: string;
  deleted: number;
  resourceCode: string;
  pdfNumber: null | number;
  exist: boolean;
  documentSourceType: null | string;
  fromResourceName: null | string;
}

interface Resource {
  resourceCode: string;
  resourceName: string;
  documentList: Document[];
}

interface FlatDocument extends Document {
  resourceName: string;
}

// 2. 转换函数
export function flattenDocuments(resources: Resource[]): FlatDocument[] {
  // 安全性检查：如果输入为空或不是数组，返回空数组
  if (!resources || !Array.isArray(resources) || resources.length === 0) {
    return [];
  }

  return resources
    .filter(
      (resource) =>
        // 过滤掉无效的资源对象
        resource &&
        typeof resource === "object" &&
        resource.documentList &&
        Array.isArray(resource.documentList),
    )
    .flatMap(({ resourceCode, resourceName, documentList }) => {
      // 确保 documentList 存在且为数组
      if (!documentList || !Array.isArray(documentList)) {
        return [];
      }

      return documentList
        .filter(
          (doc) =>
            // 过滤掉无效的文档对象
            doc && typeof doc === "object" && doc.id,
        )
        .map((doc) => ({
          ...doc,
          // 安全地处理字符串字段，避免 null/undefined
          resourceCode: doc.resourceCode || resourceCode || "",
          resourceName: resourceName || "",
          name: doc.name || "",
          type: doc.type || "",
        }));
    });
}
