import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// 定义面包屑项的类型
interface BreadcrumbItem {
  name: string;
  path: string;
}

// Redux state 结构
interface BreadcrumbState {
  breadcrumb: BreadcrumbItem[];
}

// 初始状态
const initialState: BreadcrumbState = {
  breadcrumb: [],
};

// 创建 slice
const breadcrumbSlice = createSlice({
  name: "breadcrumb",
  initialState,
  reducers: {
    // 直接存储传入的 breadcrumb 数据
    setBreadcrumb: (state, action: PayloadAction<BreadcrumbItem[]>) => {
      state.breadcrumb = action.payload;
    },
    // 清空面包屑
    clearBreadcrumb: (state) => {
      state.breadcrumb = [];
    },
  },
});

// 导出 actions
export const { setBreadcrumb, clearBreadcrumb } = breadcrumbSlice.actions;

// 导出 reducer
export default breadcrumbSlice.reducer;
