import { paperFormColumns } from "./common";
import PaginatedTable from "@/components/PaginatedTable";
import {
  columns,
  buttonGroup as buttonGroups,
} from "@/views/PaperBase/components/PersonalPaper/setting";
import { anyValueProps } from "@/types/common";
import { getPersonalPaperList } from "@/api/personalpaper";
import { useQuery } from "@tanstack/react-query";
import SearchIcon from "@mui/icons-material/Search";
import { TextField } from "@mui/material";
import DynamicForm, { RefProps } from "@/components/DynamicForm";
import ButtonGroup from "@/views/KnowledgeBase/components/ButtonGroup";
import { SearchParamProp } from "@/views/PaperBase/components/PersonalPaper";
import { RootState, useAppSelector } from "@/hooks";
import { getDocumentByUid } from "@/api/chat";
import { debounce } from "lodash";
const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
}));

const Content = styled("div", {
  shouldForwardProp: (p) => p !== "isDialog",
})<{
  isDialog: boolean;
}>(({ isDialog }) => ({
  flex: 1,
  display: "flex",
  padding: isDialog ? "13px 0" : "10px 41px",
  boxSizing: "border-box",
}));

const LeftList = styled(Box)(() => ({
  width: "200px",
  height: "100%",
  boxSizing: "border-box",
  borderRadius: 20,
  background:
    "radial-gradient(400% 69.42% at 17.272727272727273% -40.128755364806864%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(255, 255, 255, 1)",
  padding: "20px 10px",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
}));

const ListItemDiv = styled("div")(() => ({
  width: "100%",
  height: "500px",
  boxSizing: "border-box",
  background:
    "linear-gradient(90deg, rgba(19, 108, 191, 0.1) 0%, rgba(38, 124, 222, 0.1) 100%)",
  padding: "5px 10px",
  borderRadius: "16px",
}));

const ListItems = styled(Box)(() => ({
  width: "100%",
  height: "100%",
  overflowY: "scroll",
  padding: "10px 0",
  boxSizing: "border-box",
}));
const TableBar = styled(Box)(({}) => ({
  display: "flex",
  width: "100%",
  height: "100%",
  flexDirection: "column",
  background:
    "radial-gradient(46.19% 59.91% at 17.11229946524064% -40.09433962264151%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  borderRadius: 20,
  marginLeft: 20,
  padding: "10px",
  boxSizing: "border-box",
  boxShadow: "none",
}));
const FromDiv = styled("div")(() => ({
  width: "180px",
  height: "38px",
  borderRadius: "24px",
  background: "rgba(255, 255, 255, 1)",
  marginBottom: "5px",
}));

const ListItemButtonStyle = styled("div", {
  shouldForwardProp: (p) => p !== "active",
})<{ active: boolean }>(({ active }) => ({
  width: "100%",
  borderRadius: "16px",
  color: active ? "rgba(24, 112, 199, 1)" : "",
  height: "37px",
  lineHeight: "37px",
  textAlign: "center",
  margin: "5px 0",
  cursor: "pointer",
  whiteSpace: "nowrap", // 禁止换行
  overflow: "hidden", // 隐藏溢出内容
  textOverflow: "ellipsis", // 显示省略号
  padding: "0 10px",
  boxSizing: "border-box",
  ":hover": {
    background: "rgba(0, 0, 0, 0.1)",
  },
}));

const FormBox = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  height: "40px",
  borderRadius: 20,
  // background: "#fff",
  marginBottom: "10px",
  justifyContent: "flex-end",
}));

const StyledTextField = styled(TextField)(() => ({
  height: "100%",
  "& .MuiInputBase-input": {
    paddingTop: 0,
    paddingBottom: 0,
    height: "100%",
  },
  "& .MuiInputBase-root": {
    height: "100%",
    borderRadius: 28,
  },
}));

const ButtonGroups = styled("div")(() => ({
  width: "100%",
  height: 40,
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
}));

const StyleButton = styled(Button, {
  shouldForwardProp: (prop) => prop !== "MyType",
})<{ MyType?: string; width?: number }>(({ MyType, width }) => ({
  width: width || 60,
  height: 32,
  background:
    MyType == "gary"
      ? "rgba(242, 242, 242, 1)"
      : "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
  color: MyType == "gary" ? "#000" : "#fff",
  borderRadius: 28,
  cursor: "pointer",
}));

interface Props {
  isDialog: boolean;
  setCheckedData: (data: any) => void;
  isAddPaper?: boolean;
}
interface PaginationProps {
  page: number;
  size: number;
}

interface SortInfoProps {
  sort: string;
  asc: boolean;
}
const Index: React.FC<Props> = ({ isDialog, setCheckedData, isAddPaper }) => {
  const formRef = useRef<RefProps>();
  const [itemActive, setItemActive] = useState(0);
  const [value, setValue] = useState<string>("");
  const [total, setTotal] = useState<number>(0);
  const [bankTotal, setBankTotal] = useState<number>(0);
  const [pagination, setPagination] = useState<PaginationProps>({
    page: 1,
    size: 10,
  });
  const [info, setInfo] = useState<PaginationProps>({
    page: 1,
    size: 15,
  });
  const [tableData, setTableData] = useState<anyValueProps[]>([]);
  const [currentData, setCurrentData] = useState<anyValueProps[]>([]);
  const [sortInfo, setSortInfo] = useState<SortInfoProps>({
    sort: "", // 排序字段
    asc: true, // 正序 | 反序
  });
  const [searchParam, setSearchParam] = useState<SearchParamProp>({});
  const [queryParams, setQueryParams] = useState<anyValueProps>({});
  const [isLoading, setIsLoading] = useState(false);
  const { selectedBank } = useAppSelector((state: RootState) => state.counter);
  const onPageChange = (paginationParams: anyValueProps) => {
    const { page, pageSize: size } = paginationParams;
    setPagination({ page, size });
  };

  const queryRequest = async () => {
    const response = await getPersonalPaperList({
      ...pagination,
      ...sortInfo,
      ...queryParams,
      ...{ externalId: itemActive },
    });
    return response.data;
  };
  const { data, status, error } = useQuery({
    queryKey: ["get-person", pagination, itemActive, queryParams], // 使用唯一键，将参数作为查询键的一部分
    queryFn: queryRequest,
  });
  const onSortChange = (
    sort: SortInfoProps["sort"],
    asc: SortInfoProps["asc"],
  ) => {
    setSortInfo({ sort, asc });
  };

  useEffect(() => {
    const newData = tableData
      .filter((item) => item.checked)
      .map((item) => item.id);
    setCheckedData(newData);
  }, [tableData]);
  useEffect(() => {
    switch (status) {
      case "success":
        setTableData(
          data?.data?.map((item: any) => ({
            ...item,
            ...{
              checked:
                isAddPaper &&
                selectedBank.externalIds &&
                selectedBank.externalIds.includes(item.id)
                  ? true
                  : false,
            },
            ...{
              disabled:
                isAddPaper &&
                selectedBank.externalIds &&
                selectedBank.externalIds.includes(item.id)
                  ? true
                  : false,
            },
          })) || [],
        );
        setTotal(data?.total || 0);
        break;
      case "error":
        message.error("获取资料列表失败, " + error.message);
        break;
    }
  }, [status, data]);

  const getPaper = async (newInfo?: PaginationProps) => {
    try {
      const { data } = await getDocumentByUid({
        ...(newInfo ? newInfo : info),
        ...(value ? { fuzzyQuery: value } : {}),
      });
      setCurrentData(data.data);
      setBankTotal(data.total);

      if (!itemActive) {
        setItemActive(data.data[0]?.id);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      message.error("获取库信息失败" + (error as Error).message);
    }
  };

  useEffect(() => {
    setSearchParam({});
  }, [itemActive]);
  const handleSearchParams = (value: any) => {
    const { fuzzyQuery, date } = value;

    const queryParams: any = {
      ...(fuzzyQuery && { fuzzyQuery }),
      parseStatusCode: 3000,
    };
    if (date && date.length > 0) {
      queryParams.updateStartTime = date[0];
      queryParams.updateEndTime = date[1];
    }

    setQueryParams(queryParams);
    setPagination((prev) => ({ page: 1, size: prev.size }));
  };

  useEffect(() => {
    handleSearchParams(searchParam);
  }, [searchParam]);
  const handleClick = (id: number) => {
    setItemActive(id);
    setPagination({ page: 1, size: 10 });
  };

  const searchChang = (value: string) => {
    setInfo({ page: 1, size: 15 });
    setValue(value);
    setItemActive(0);
  };

  const handelReset = () => {
    setSearchParam({});
  };

  const handleSubmit = () => {
    formRef.current?.submit().then((res) => {
      const newSearchParam = { ...searchParam, ...(res || {}) };
      setSearchParam(newSearchParam);
    });
  };

  useEffect(() => {
    getPaper();
  }, [info.size, value]);

  const debouncedScroll = debounce((e) => {
    if (isLoading) return;

    const bottom =
      e.target.scrollHeight - 3 <= e.target.scrollTop + e.target.clientHeight;
    const { page, size } = info;
    const isHasMoreData = bankTotal > size; // 修正判断逻辑

    if (bottom && isHasMoreData) {
      setIsLoading(true);
      setInfo({ page, size: size + 15 });
    }
  }, 200);
  const handleScroll = (e: any) => {
    debouncedScroll(e);
  };
  return (
    <Root>
      <Content isDialog={isDialog}>
        <LeftList>
          <FromDiv>
            <StyledTextField
              placeholder="请输入关键词"
              autoComplete="off"
              slotProps={{
                input: {
                  startAdornment: <SearchIcon />,
                },
              }}
              onChange={(e) => searchChang(e.target.value)}
            />
          </FromDiv>
          <div style={{ flex: 1, width: "100%" }}>
            <ListItemDiv>
              {currentData.length ? (
                <ListItems onScroll={handleScroll}>
                  {currentData &&
                    currentData.map((ite, i) => (
                      <ListItemButtonStyle
                        key={i}
                        onClick={() => handleClick(ite.id)}
                        active={itemActive === ite.id}
                        title={ite.name}
                      >
                        {ite.name}
                      </ListItemButtonStyle>
                    ))}
                </ListItems>
              ) : (
                ""
              )}
            </ListItemDiv>
          </div>
        </LeftList>
        <TableBar>
          <FormBox>
            <Box sx={{ height: 40, mr: 4, width: "24%" }}>
              <DynamicForm
                ref={formRef}
                columns={paperFormColumns}
                formData={searchParam}
                // // onChange={onChange}
                labelWidth={100}
                size="small"
              />
            </Box>
            <Box sx={{ height: 40, display: "flex" }}>
              <ButtonGroups>
                <StyleButton
                  MyType="gary"
                  sx={{ mr: 1.25 }}
                  onClick={handelReset}
                >
                  重置
                </StyleButton>
                <StyleButton onClick={handleSubmit}>查询</StyleButton>
              </ButtonGroups>
            </Box>
          </FormBox>
          <PaginatedTable
            total={total}
            page={pagination.page}
            pageSize={pagination.size}
            onChangePage={onPageChange}
            onSortChange={onSortChange}
            rows={tableData}
            headCells={columns}
            selection={true}
            buttonGroup={
              <ButtonGroup
                data={buttonGroups.slice(0, 3)}
                checkedList={tableData}
                setCheckedList={setTableData}
              />
            }
            setCheckedList={setTableData}
          />
        </TableBar>
      </Content>
    </Root>
  );
};
export default Index;
