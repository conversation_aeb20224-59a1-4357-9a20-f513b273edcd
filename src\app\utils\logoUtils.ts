import LogoUrlOther from "@/assets/logo-other.png";
import LogoMaxUrlOther from "@/assets/maxlogo-other.png";
import LogoUrlJS from "@/assets/logo-js.png";
import LogoMaxUrlJS from "@/assets/maxlogo-js.png";

/**
 * 根据构建类型获取logo配置
 */
export const getLogoConfig = () => {
  const isGnBuild =
    typeof __LOGO_TYPE__ !== "undefined" && __LOGO_TYPE__ === "other";

  if (isGnBuild) {
    return {
      shrinkLogo: LogoUrlOther,
      expandLogo: LogoMaxUrlOther,
      defaultLogo: LogoUrlOther,
    };
  } else {
    return {
      shrinkLogo: LogoUrlJS,
      expandLogo: LogoMaxUrlJS,
      defaultLogo: LogoUrlJS,
    };
  }
};

/**
 * 获取默认logo（用于Avatar等场景）
 */
export const getDefaultLogo = () => getLogoConfig().defaultLogo;

/**
 * 根据收缩状态获取对应的logo（用于Layout等场景）
 */
export const getLogoByState = (isShrink: boolean) => {
  const config = getLogoConfig();
  return isShrink ? config.shrinkLogo : config.expandLogo;
};
