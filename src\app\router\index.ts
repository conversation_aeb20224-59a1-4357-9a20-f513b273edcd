import NotFound from "../views/404";
import NorRole from "../views/401";
import { paperRoute } from "./modules/PaperSearch";
import { chatRoute } from "./modules/AiChat";
import { personalRoute } from "./modules/PersonalPaper";
// import { knowledgeRoute } from "./modules/KnowledgeBase";
import { personCenter } from "./modules/PersonCenter";
import Login from "@/views/Login";
// import Login from "@/views/Login";

export interface RouteProps {
  path: string;
  name?: string;
  description: string;
  lazyComponent: () => Promise<any>;
  components: React.FC<any>;
  /**
   * 特殊情况下：如 界面离开前需要提示
   * 则配置components属性
   */
  hidden?: boolean;
  role?: string;
  children?: RouteProps[];
  icon?: any;
  menuCode?: string;
}

export interface StaticRouteProps {
  path: string;
  name: string;
  description: string;
  components: React.FC<any>;
  hidden?: boolean;
  role?: string;
  children?: RouteProps[];
  icon?: any;
}

/**
 * 动态路由
 */
const dynamicRouteList: Array<RouteProps> = [
  ...paperRoute,
  ...chatRoute,
  // ...knowledgeRoute,
  ...personalRoute,
  ...personCenter,
];

/**
 * 静态路由(如 login 404 forgetPassword 等 不需要登录信息的界面)
 */
const staticRouteList: Array<StaticRouteProps> = [
  {
    path: "/404",
    name: "404",
    description: "404",
    components: NotFound,
  },
  {
    path: "/401",
    name: "401",
    description: "401",
    components: NorRole,
  },
  {
    path: "/login",
    name: "login",
    description: "login",
    components: Login,
  },
];

export { staticRouteList, dynamicRouteList };
