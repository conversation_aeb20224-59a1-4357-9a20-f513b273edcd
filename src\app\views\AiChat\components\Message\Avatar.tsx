import { FC } from "react";
import { getDefaultLogo } from "@/utils/logoUtils";
import aiAvatar from "@/assets/ai-avatar.jpeg";
import { Avatar } from "@mui/material";
import { styled } from "@mui/material";
const AvatarDiv = styled(Avatar)(() => ({
  width: "auto",
  ".css-1pqm26d-MuiAvatar-img": {
    width: "auto",
  },
}));
interface AvatarProps {
  image?: boolean;
}
const Index: FC<AvatarProps> = ({ image }) => (
  <div>
    {image ? <AvatarDiv src={getDefaultLogo()} /> : <Avatar src={aiAvatar} />}
  </div>
);

export default Index;
