import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { AddPaperBaseColumns } from "./setting";
import CloseIcon from "@mui/icons-material/Close";
import PromptIcon from "@/assets/prompt.svg";
interface Props {
  setEditOpen: (value: boolean) => void;
}

const Root = styled("div")(() => ({
  position: "absolute",
  right: 0,
  top: "100%",
  width: "710px",
  padding: "10px 15px 20px 15px",
  background: "#fff",
  borderRadius: "20px",
  border: "1px solid #E5E5E5",
  zIndex: 300,
  boxShadow:
    "0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12)",
}));

const FootDiv = styled("div")(() => ({
  width: "100%",
  marginTop: 10,
  display: "flex",
  justifyContent: "flex-end",
}));

const CancelButton = styled(Button)(() => ({
  width: 60,
  height: 32,
  borderRadius: 28,
  background: "rgba(242, 242, 242, 1)",
  color: "rgba(31, 31, 31, 1)",
  marginRight: 10,
}));

const SubmitButton = styled(Button)(() => ({
  width: 60,
  height: 32,
  borderRadius: 28,
  background:
    "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
  color: "rgba(255, 255, 255, 1)",
}));

const CloseDiv = styled("div")(() => ({
  display: "flex",
  justifyContent: "flex-end",
}));

const DialogClose = styled(Button)(() => ({
  minWidth: 0,
  padding: 0,
  color: "#ccc",
  borderRadius: "50%",
}));

const Prompt = styled("div")(() => ({
  fontSize: 13,
  display: "flex",
  alignItems: "center",
  marginTop: 5,
  color: "rgba(31, 31, 31, 1)",
}));

const IconStyle = styled("img")(() => ({
  width: 18,
  height: 18,
  marginRight: 5,
}));
const Index: React.FC<Props> = ({ setEditOpen }) => {
  const formRef = useRef<RefProps>(null);

  const handleSubmit = async () => {
    try {
      const values = await formRef.current?.submit();
      console.log(values);
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <Root>
      <CloseDiv>
        <DialogClose aria-label="close" onClick={() => setEditOpen(false)}>
          <CloseIcon style={{ fontSize: "20px" }} />
        </DialogClose>
      </CloseDiv>
      <DynamicForm
        ref={formRef}
        columns={AddPaperBaseColumns}
        size="small"
        direction="column"
        rowSpacing={1.5}
      />
      <Prompt>
        <IconStyle src={PromptIcon} />
        小提示：添加提示词，可以帮助我给出更精准的建议哦~
      </Prompt>
      <FootDiv>
        <>
          <CancelButton onClick={() => setEditOpen(false)}>取消</CancelButton>
          <SubmitButton onClick={handleSubmit}>确定</SubmitButton>
        </>
      </FootDiv>
    </Root>
  );
};
export default Index;
