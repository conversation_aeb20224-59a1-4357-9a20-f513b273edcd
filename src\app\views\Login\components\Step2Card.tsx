import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { forgotStep2Columns } from "../setting";
import { anyValueProps } from "@/types/common";
import { findPassword } from "@/api/login";

interface Props {
  rowData: anyValueProps;
  setStep: (value: any) => void;
  step: number;
}

const Step2Box = styled("div", {
  shouldForwardProp: (props) => props !== "step",
})<{ step: number }>(({ step }) => ({
  width: "100%",
  height: "100%",
  padding: "0 60px",
  boxSizing: "border-box",
  display: step === 1 ? "block" : "none",
}));

const FormBox = styled("div")(() => ({
  width: "100%",
  marginBottom: 30,
  paddingBottom: "11px",
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
}));

const PrevButton = styled("div")(() => ({
  width: "100%",
  display: "flex",
  justifyContent: "center",
  fontSize: 14,
  marginTop: 19,
  color: "#4248B5",
  cursor: "pointer",
}));

const Step2Card: React.FC<Props> = ({ setStep, rowData, step }) => {
  const formRef = useRef<RefProps>(null);

  const handleSbumit = () => {
    formRef.current?.submit().then(async (res: any) => {
      if (res) {
        const { password } = res;
        const params = {
          password,
          ...rowData,
        };
        const {
          data: { code },
        } = await findPassword(params);
        if (code !== 200) {
          message.error("密码修改失败");
          return;
        }
        setStep(2);
      }
    });
  };

  return (
    <Step2Box step={step}>
      <FormBox>
        <DynamicForm
          ref={formRef}
          columns={forgotStep2Columns}
          direction="column"
          rowSpacing={0}
        />
      </FormBox>
      <Button variant="contained" fullWidth onClick={handleSbumit}>
        下一步
      </Button>
      <PrevButton onClick={() => setStep(0)}>上一步</PrevButton>
    </Step2Box>
  );
};
export default Step2Card;
