import AgreementDialog from "@/views/Login/components/Agreement/AgreementDialog";

const Root = styled("div")(() => ({
  width: "100%",
  height: 30,
  display: "flex",
  alignItems: "flex-end",
  justifyContent: "center",
  background: "transparent",
  boxSizing: "border-box",
  position: "absolute",
  bottom: 0,
  zIndex: 1000,
}));

const PrivacyLabel = styled("div")(() => ({
  fontSize: 14,
  fontWeight: 400,
  lineHeight: "30px",
  color: "rgba(0, 51, 125, 1)",
  marginRight: 16,
  cursor: "pointer",
}));

const CopyRight: React.FC = () => {
  const [open, setOpen] = useState(false);

  const handleClick = () => {
    setOpen(true);
  };
  return (
    <Root>
      <PrivacyLabel onClick={() => handleClick()}>
        《智能助手用户隐私协议》
      </PrivacyLabel>
      {open && (
        <AgreementDialog open={open} setOpen={setOpen}></AgreementDialog>
      )}
    </Root>
  );
};
export default CopyRight;
