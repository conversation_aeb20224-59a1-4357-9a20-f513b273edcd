import React, { useEffect, useRef, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { styled, Box } from "@mui/material";
import { MyPaginationProps } from "../common/common";
import { getPdfTask } from "@/api/personalpaper";
import Breadcrumb from "@/components/Breadcrumb";
import { SearchParamProp } from "../..";
import UploadRecordHeader from "./UploadRecordHeader";
import PaginatedTable from "@/components/PaginatedTable";
import { columns } from "./setting";
import { anyValueProps } from "@/types/common";
import { defalutBreadcrumb } from "../../setting";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import { PERMISSION_MENU } from "@/utils/permission";
import { useAppSelector } from "@/hooks";
import { checkPermission } from "@/utils/auth";

const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
}));

const RootContent = styled("div")(() => ({
  width: "100%",
  height: "calc(100% - 54px)",
  padding: "18px 41px 10px 41px",
  boxSizing: "border-box",
}));

const OperationBar = styled(Box)(() => ({
  width: "100%",
  height: 64,
  boxSizing: "border-box",
  display: "flex",
  alignItems: "center",
}));

const BackBox = styled("div")(() => ({
  width: 86,
  height: "100%",
  borderRadius: 14,
  background:
    "radial-gradient(70.93% 70.31% at 17.441860465116278% -40.625%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(242, 243, 247, 1)",
  border: "2px solid rgba(255, 255, 255, 1)",
  marginRight: 17,
  boxSizing: "border-box",
  fontSize: 14,
  color: "#000",
  fontWeight: 400,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  cursor: "pointer",
}));

const BackIcon = styled("div")(() => ({
  width: 16,
  height: 16,
  borderRadius: "50%",
  background: "rgba(255, 255, 255, 0.88)",
  marginRight: 8,
  boxSizing: "border-box",
}));

const TableBar = styled(Box)(() => ({
  display: "flex",
  width: "100%",
  height: "calc(100% - 80px)",
  background:
    "radial-gradient(46.19% 59.91% at 17.11229946524064% -40.09433962264151%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  borderRadius: 20,
  marginTop: "20px",
  padding: "10px",
  boxSizing: "border-box",
  boxShadow:
    "0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12)",
  paddingBottom: "25px",
}));

interface SortInfoProps {
  sort: string;
  asc: boolean;
}

const TaskDetail: React.FC = () => {
  const { type } = useParams();
  const { roleOption } = useAppSelector((state) => state.user);
  const navigator = useNavigate();
  const location = useLocation();
  const [pagination, setPagination] = useState<MyPaginationProps>({
    page: 1,
    size: 50,
  });
  const [total, setTotal] = useState<number>(0);
  const [tableData, setTableData] = useState<anyValueProps[]>([]);
  const [searchParams, setSearchParams] = useState<SearchParamProp>({
    fuzzyQuery: "",
    status: "3",
    ...(checkPermission(roleOption) ? { resourceCode: "1" } : {}),
  });

  const [queryParams, setQueryParams] = useState<anyValueProps>({});
  const [sortInfo, setSortInfo] = useState<SortInfoProps>({
    sort: "createTime", // 排序字段
    asc: false,
  });
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const tableCloumns = useMemo(() => columns(checkPermission(roleOption)), []);

  const queryRequest = async () => {
    try {
      const { data } = await getPdfTask({
        ...pagination,
        ...queryParams,
        ...sortInfo,
      });
      setTableData(data.data);
      setTotal(data.total);
      const refetchFlag = data.data.some(
        (task: any) => task.status === -1 || task.status === 0,
      );
      if (refetchFlag) delayReload(data.data);
    } catch {
      message.error("获取任务列表失败");
    }
  };

  const onPageChange = (paginationParams: anyValueProps) => {
    const { page, pageSize: size } = paginationParams;
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    setPagination({ page, size });
  };

  const handleBack = () => {
    const index = defalutBreadcrumb[type ? type : "MyDocDB"].length - 1;
    const path = defalutBreadcrumb[type ? type : "MyDocDB"][index].path;
    navigator(path);
  };

  const viewTaskDetail = async (rowData: anyValueProps) => {
    window.console.log(`${location.pathname}/${rowData.id}`);
    navigator(`${location.pathname}/${rowData.id}`);
  };

  const handleAction = (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    key: string,
    row: anyValueProps,
  ) => {
    if (key === "查看") {
      viewTaskDetail(row);
    }
  };

  const onSortChange = (sort: string, asc: boolean) => {
    setSortInfo({ sort, asc });
  };

  // // 判断当前页pdf是否全部解析完成，未完成则每过15秒刷新表格
  const delayReload = (tasks: any) => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    const refetchFlag = tasks.some(
      (task: any) => task.status === -1 || task.status === 0,
    );
    if (refetchFlag) {
      timerRef.current = setTimeout(() => {
        queryRequest();
        delayReload(tableData || []);
      }, 5000);
    } else {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    }
  };

  const handleSearchParams = (value: any) => {
    // if (Object.keys(value).length === 0) return;
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    const { fuzzyQuery, status, resourceCode } = value;
    if (status === "3") {
      setQueryParams({
        ...(fuzzyQuery ? { fuzzyQuery } : {}),
        ...(resourceCode !== "1" ? { resourceCode } : {}),
        status: "",
      });
    } else {
      setQueryParams({
        ...(fuzzyQuery ? { fuzzyQuery } : {}),
        ...(resourceCode !== "1" ? { resourceCode } : {}),
        status,
      });
    }
    setPagination((prev) => ({ page: 1, size: prev.size }));
  };

  useEffect(() => {
    handleSearchParams(searchParams);
  }, [searchParams]);

  useEffect(() => {
    queryRequest();
  }, [pagination, queryParams, sortInfo]);

  useEffect(
    () => () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    },
    [],
  );
  return (
    <Root>
      <Breadcrumb
        parent={[...defalutBreadcrumb[type ? type : "MyDocDB"]]}
        current={`上传记录`}
      />
      <RootContent>
        <OperationBar>
          <BackBox onClick={() => handleBack()}>
            <BackIcon>
              <ArrowBackIosNewIcon sx={{ fontSize: 14 }} />
            </BackIcon>
            返回
          </BackBox>
          <UploadRecordHeader
            searchParam={searchParams}
            setSearchParam={setSearchParams}
          />
        </OperationBar>
        <TableBar>
          <PaginatedTable
            selection={true}
            total={total}
            page={pagination.page}
            pageSize={pagination.size}
            onChangePage={onPageChange}
            rows={tableData}
            headCells={tableCloumns}
            actionButtons={[{ type: "查看", role: PERMISSION_MENU["edit"] }]}
            actionClick={handleAction}
            onSortChange={onSortChange}
            pageSizeOptions={[50, 100, 150]}
          />
        </TableBar>
      </RootContent>
    </Root>
  );
};

export default TaskDetail;
