import { MenuItem, Select } from "@mui/material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";

export interface PageProps {
  page: number;
  pageSize: number;
}

interface Props {
  total: number;
  page: number;
  onChangePage: (pageParams: PageProps) => void;
  size?: "small" | "medium" | "large";
  pageSize?: number;
  pageSizeOptions?: number[];
}

const PaginationBar = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
}));

const CountBox = styled("div")(() => ({
  fontSize: "14px",
  marginInlineEnd: "8px",
  color: "rgba(36, 36, 36, 1)",
  fontWeight: 400,
}));

const CountSpan = styled("span")(() => ({
  color: "rgba(24, 112, 199, 1)",
  fontWeight: 700,
}));

const PageSizeSelectBox = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  marginRight: 20,
}));
const PageSizeSelectSpan = styled("span")(() => ({
  fontSize: 14,
  fontWeight: 400,
  color: "rgba(64, 64, 64, 1)",
  marginRight: 14,
}));

const PageSizeSelect = styled(Select)(() => ({
  width: "115px !important",
  height: 32,
  border: "1px solid rgba(235, 235, 235, 1)",
  borderRadius: 20,
}));

const PageBox = styled("div")(() => ({
  marginInlineEnd: "8px",
  display: "flex",
  alignItems: "center",
}));

const PageButton = styled("div", {
  shouldForwardProp: (props) => props !== "disable",
})<{ disable: boolean }>(({ disable }) => ({
  width: 32,
  height: 32,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  borderRadius: 20,
  background: disable ? "rgba(235, 235, 235, 1)" : "#fff",
  border: "1px solid rgba(235, 235, 235, 1)",
  cursor: "pointer",
  color: disable ? "rgba(207, 207, 207, 1)" : "#000",
}));

const PageNumInput = styled("input")(() => ({
  width: 48,
  height: 32,
  borderRadius: 20,
  border: "1px solid rgba(235, 235, 235, 1)",
  textAlign: "center",
  outline: "none",
  ":active": {
    outline: "1px solid #1870c7",
  },
  ":focus": {
    outline: "1px solid #1870c7",
  },
  margin: "0 5px 0 10px",
}));

const PageNumCount = styled("div")(() => ({
  height: "100%",
  display: "flex",
  alignItems: "center",
  fontSize: 14,
  lineHeight: "16px",
  fontWeight: 400,
  color: "rgba(64, 64, 64, 1)",
  marginRight: 10,
}));

const Pagination: React.FC<Props> = (props) => {
  const {
    total,
    page,
    onChangePage,
    pageSize = 10,
    pageSizeOptions = [],
  } = props;
  const [value, setValue] = useState("");
  const [oldValue, setOldValue] = useState(page);
  const handleChange = (type: string, value: number) => {
    const totalPages = Math.ceil(total / pageSize);
    if (type === "page") {
      if (isNaN(value) || value < 1 || value > totalPages) return;
      onChangePage({ page: value, pageSize });
      setOldValue(value);
    } else if (type === "pageSize") {
      const newTotalPages = Math.ceil(total / value);
      const newPage = Math.min(page, newTotalPages);
      onChangePage({ page: newPage, pageSize: value });
      setOldValue(newPage);
    } else if (type === "pageDown") {
      if (value > 1) {
        onChangePage({ page: value - 1, pageSize });
        setOldValue(value - 1);
      }
    } else if (type === "pageUp") {
      if (value < totalPages) {
        onChangePage({ page: value + 1, pageSize });
        setOldValue(value + 1);
      }
    }
  };

  const handleBlur = (e: any, value: number) => {
    if (isNaN(value) || value < 1 || value > Math.ceil(total / pageSize)) {
      setValue(oldValue.toString());
      return;
    }
    handleChange("page", value);
  };

  useEffect(() => {
    setValue(String(page));
  }, [page]);

  return (
    <PaginationBar>
      <CountBox>
        共<CountSpan>{total}</CountSpan>条
      </CountBox>
      <PageSizeSelectBox>
        <PageSizeSelectSpan>每页显示</PageSizeSelectSpan>
        <PageSizeSelect
          value={pageSize.toString()}
          MenuProps={{
            anchorOrigin: {
              vertical: "top", // 相对于触发元素的垂直位置
              horizontal: "center", // 相对于触发元素的水平位置
            },
            transformOrigin: {
              vertical: "bottom", // 下拉菜单自身的垂直起点
              horizontal: "center", // 下拉菜单自身的水平起点
            },
          }}
          onChange={(e) => handleChange("pageSize", Number(e.target.value))}
        >
          {pageSizeOptions.map((item: any) => (
            <MenuItem key={item} value={String(item)}>
              {item}条
            </MenuItem>
          ))}
        </PageSizeSelect>
      </PageSizeSelectBox>
      <PageBox>
        <PageButton
          onClick={() => handleChange("pageDown", page)}
          disable={page === 1}
        >
          <ArrowBackIosNewIcon sx={{ fontSize: 16 }} />
        </PageButton>
        <PageNumInput
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleChange("page", Number(value));
            }
          }}
          onBlur={(e) => handleBlur(e, Number(value))}
        />
        <PageNumCount>{`/ ${Math.ceil(total / pageSize)}`}</PageNumCount>
        <PageButton
          onClick={() => handleChange("pageUp", page)}
          disable={page === Math.ceil(total / pageSize)}
        >
          <ArrowForwardIosIcon sx={{ fontSize: 16 }} />
        </PageButton>
      </PageBox>
    </PaginationBar>
  );
};
export default Pagination;
