import CustomDialog from "@/components/Dialog";
import { AddPaperBaseColumns } from "./setting";
import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { createPaperBase, editPaperBase } from "@/api/personalpaper";
import { anyValueProps } from "@/types/common";
import { useAppSelector } from "@/hooks";
import { checkPermission } from "@/utils/auth";
import {
  FormControl,
  FormHelperText,
  FormLabel,
  InputBase,
  MenuItem,
  Select,
} from "@mui/material";
import { queryGroupList } from "@/api/knowledgeBase";

interface Props {
  open: boolean;
  setOpen: (value: boolean) => void;
  reload: () => void;
  editOption?: anyValueProps;
  type: string;
}

const ContentBox = styled("div")(() => ({
  height: "100%",
  padding: "15px 20px 0 20px",
}));

const FormBox = styled("div")(() => ({
  width: "100%",
}));

const BaseSelect = styled("div")(() => ({
  width: "calc(100% - 70px)",
}));

const StyledSelect = styled(Select)(() => ({
  "& .MuiSelect-select": {
    borderRadius: 28,
  },
  "&.MuiInputBase-root": {
    borderRadius: 28,
  },
  "& #demo-customized-select": {
    // height: 10,
    padding: "6.5px 10px",
    borderRadius: 28,
    boxSize: "border-box",
  },
}));

const BootstrapInput = styled(InputBase, {
  shouldForwardProp: (props) => props !== "errorFlag",
})<{ errorFlag: string }>(({ theme, errorFlag }) => ({
  "label + &": {
    marginTop: theme.spacing(3),
  },
  "& .MuiInputBase-input": {
    // height: "30px",
    borderRadius: 4,
    position: "relative",
    backgroundColor: theme.palette.background.paper,
    border: "1px solid #ced4da",
    borderColor: errorFlag ? "#d32f2f" : "#ced4da",
    fontSize: 16,
    // padding: "10px 26px 10px 12px",
    padding: "5px 0 5px 12px",
    transition: theme.transitions.create(["border-color", "box-shadow"]),
    boxSize: "border-box",
    // Use the system font instead of the default Roboto font.
    fontFamily: [
      "-apple-system",
      "BlinkMacSystemFont",
      '"Segoe UI"',
      "Roboto",
      '"Helvetica Neue"',
      "Arial",
      "sans-serif",
      '"Apple Color Emoji"',
      '"Segoe UI Emoji"',
      '"Segoe UI Symbol"',
    ].join(","),
    "&:focus": {
      borderRadius: 4,
      borderColor: "#4248B5",
      boxShadow: "0 0 0 0.2rem rgba(64,72,181,.25)",
    },
  },
}));

const FooterBox = styled("div")(() => ({
  width: "100%",
  height: 63,
  display: "flex",
  justifyContent: "flex-end",
  alignItems: "center",
}));

const CancelButton = styled(Button)(() => ({
  width: 60,
  height: 32,
  borderRadius: 28,
  background: "rgba(242, 242, 242, 1)",
  color: "rgba(31, 31, 31, 1)",
  marginRight: 10,
}));

const SubmitButton = styled(Button)(() => ({
  width: 60,
  height: 32,
  borderRadius: 28,
  background:
    "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
  color: "rgba(255, 255, 255, 1)",
}));

const AddPaperBaseDialog: React.FC<Props> = ({
  open,
  setOpen,
  reload,
  editOption,
  type,
}) => {
  const formRef = useRef<RefProps>(null);
  const { roleOption } = useAppSelector((state) => state.user);
  const [groupList, setGroupList] = useState<any[]>([]);
  const [baseSelect, setBaseSelect] = useState("");
  const [errorText, setErrorText] = useState<string>("");
  const [groupPagination, setGroupPagination] = useState({
    page: 1,
    size: 10,
  });
  const [groupTotal, setGroupTotal] = useState<number>(0);
  const [groupCountPage, setGroupCountPage] = useState<number>(1);

  const init = () => {
    if (type !== "add" && editOption) {
      setBaseSelect(editOption.resourceCode);
    }
  };

  useEffect(() => {
    init();
  }, []);

  const queruGroupList = async (params: anyValueProps) => {
    const { data } = await queryGroupList({
      ...params,
    });
    if (data.code === 200) {
      setGroupList(data.result);
      setGroupTotal(data.total);
    }
  };

  useEffect(() => {
    if (roleOption.roleCode === "ADMIN") queruGroupList(groupPagination);
  }, [groupPagination]);

  const adminAddRequest = async (params: anyValueProps) => {
    try {
      const {
        data: { code, message },
      } = await createPaperBase({
        ...params,
        resourceCode: baseSelect,
        type: "document",
      });
      if (code === 200) {
        setOpen(false);
        reload();
      } else {
        message.error(message);
      }
    } catch (e: any) {
      message.error(e.message);
    }
  };

  const normalAddRequest = async (params: anyValueProps) => {
    try {
      const {
        data: { code, message },
      } = await createPaperBase({
        ...params,
        type: "document",
      });
      if (code === 200) {
        reload();
        setOpen(false);
      } else {
        message.error(message);
      }
    } catch (e: any) {
      message.error(e.message);
    }
  };

  const addRequest = async (params: anyValueProps) => {
    if (checkPermission(roleOption)) {
      adminAddRequest(params);
    } else {
      normalAddRequest(params);
    }
  };

  const editRequest = async (params: anyValueProps) => {
    const isAdmin = checkPermission(roleOption);
    try {
      const { id, name, version, note } = params;
      const {
        data: { code, message },
      } = await editPaperBase({
        id,
        name,
        version,
        note,
        ...(isAdmin ? { resourceCode: baseSelect } : {}),
      });
      if (code === 200) {
        reload();
        setOpen(false);
      } else {
        message.error(message);
      }
    } catch (e: any) {
      message.error(e.message);
    }
  };

  const handleGroupList = (event: any) => {
    const bottom =
      event.target.scrollHeight ===
      event.target.scrollTop + event.target.clientHeight;
    if (bottom) {
      // 滚动到底部并且不是正在加载
      const newPage = groupCountPage + 1;
      const maxPage = Math.ceil(groupTotal / 10);
      setGroupCountPage(newPage);
      if (newPage <= maxPage) {
        setGroupPagination({ size: 10 * newPage, page: 1 });
      }
    }
  };

  const handleSelectChange = (event: { target: { value: string } }) => {
    setBaseSelect(event.target.value);
    setErrorText("");
  };

  const handleSubmit = () => {
    if (!baseSelect) {
      setErrorText("请选择课题组");
    }

    formRef.current?.submit().then(async (res: any) => {
      switch (type) {
        case "add":
          await addRequest(res);
          break;
        case "edit":
          await editRequest(res);
          break;
        default:
          break;
      }
    });
  };

  const filterGroupList = (
    groupList: any[],
    editOption?: anyValueProps,
  ): any[] => {
    if (!editOption?.resourceCode) return groupList;
    return groupList.filter(
      (item) => item.resourceCode !== editOption.resourceCode,
    );
  };

  return (
    <CustomDialog
      open={open}
      setDialogOpen={setOpen}
      title={type === "add" ? "新建资料库" : "编辑资料库"}
      width={410}
    >
      <ContentBox slot="content">
        {checkPermission(roleOption) && (
          <FormBox>
            <FormControl
              fullWidth
              error={errorText ? true : false}
              sx={{
                flexDirection: "row",
                height: "100%",
              }}
            >
              <FormLabel
                component={"div"}
                required={true}
                sx={{
                  textWrap: "nowrap",
                  mr: 1.25,
                  mt: 1,
                  textAlign: "right",
                  width: 60,
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  color: "rgba(31, 31, 31, 1)",
                  fontWeight: 400,
                  fontSize: 16,
                  boxSizing: "border-box",
                  justifyContent: "flex-end",
                }}
              >
                课题组
              </FormLabel>
              <BaseSelect>
                <StyledSelect
                  fullWidth
                  labelId="demo-customized-select-label"
                  id="demo-customized-select"
                  value={baseSelect}
                  onChange={(e: any) => handleSelectChange(e)}
                  input={<BootstrapInput errorFlag={errorText} />}
                  MenuProps={{
                    PaperProps: {
                      onScroll: handleGroupList, // 监听菜单滚动
                    },
                  }}
                  size="small"
                  placeholder="请选择资料库"
                  sx={{ height: 38 }}
                >
                  {editOption && type !== "add" && (
                    <MenuItem
                      key={editOption.resourceCode}
                      value={editOption.resourceCode}
                    >
                      {editOption.fromResourceName}
                    </MenuItem>
                  )}
                  {filterGroupList(groupList, editOption).map((item) => (
                    <MenuItem key={item.resourceCode} value={item.resourceCode}>
                      {item.groupName}
                    </MenuItem>
                  ))}
                </StyledSelect>
                <FormHelperText sx={{ mt: 0, ml: 2, color: "#d32f2f" }}>
                  {errorText || " "}
                </FormHelperText>
              </BaseSelect>
            </FormControl>
          </FormBox>
        )}
        <DynamicForm
          ref={formRef}
          columns={AddPaperBaseColumns}
          size="small"
          labelWidth={60}
          formData={type !== "add" ? editOption : null}
        />
      </ContentBox>
      <FooterBox slot="footer">
        <CancelButton onClick={() => setOpen(false)}>取消</CancelButton>
        <SubmitButton onClick={handleSubmit}>
          {type === "add" ? "创建" : "保存"}
        </SubmitButton>
      </FooterBox>
    </CustomDialog>
  );
};
export default AddPaperBaseDialog;
