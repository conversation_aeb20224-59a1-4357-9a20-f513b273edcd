import { defineConfig, normalizePath } from "vite";
import react from "@vitejs/plugin-react";
import { join } from "path";
import AutoImport from "unplugin-auto-import/vite";
import { createHtmlPlugin } from "vite-plugin-html";
import { commitHash } from "./buildInfo";
import path from "node:path";
import { createRequire } from "node:module";
import { viteStaticCopy } from "vite-plugin-static-copy";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";

const require = createRequire(import.meta.url);
const cMapsDir = normalizePath(
  path.join(path.dirname(require.resolve("pdfjs-dist/package.json")), "cmaps"),
);

export default defineConfig(({ mode }) => {
  // 根据构建模式决定logo配置
  const isOtherBuild = mode === "other";

  return {
    base: "/aihub-chat/",
    define: {
      // 将logo配置传递给应用程序
      __LOGO_TYPE__: JSON.stringify(isOtherBuild ? "other" : "default"),
    },
    plugins: [
      react(),
      createHtmlPlugin({
        minify: false,
        pages: [
          {
            template: "index.html",
            filename: "index.html",
            injectOptions: {
              data: {
                buildTime: new Date().toLocaleString(),
                version: commitHash,
                // 根据构建类型选择不同的favicon
                favicon: isOtherBuild ? "/favicon-other.ico" : "/favicon.ico",
              },
            },
          },
        ],
      }),
      AutoImport({
        imports: [
          "react",
          {
            "@mui/material": [
              "styled",
              "Button",
              "Box",
              "Typography",
              // 可以添加更多MUI组件
            ],
          },
          {
            "@/components/MessageBox/message": ["message"],
          },
        ],
        dts: "./auto-imports.d.ts",
      }),
      viteStaticCopy({
        targets: [
          {
            src: cMapsDir,
            dest: "",
          },
        ],
      }),
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), "src/app/assets/svgs")],
        symbolId: "icon-[name]",
      }),
    ],
    server: {
      host: true, // 允许通过 IP 地址访问
      port: 7777,
      open: true, // 自动打开浏览器
      proxy: {
        "/chat": {
          // target: "http://************:9086", // wzy
          target: "http://************:9086", // lmm
          // target: "http://************:9086", // zxt
          changeOrigin: true,
        },
        "/engine": {
          // target: "http://************:9066", // wzy
          // target: "http://************:9066", // lmm
          target: "http://************:8080", // zxt
          // target: "http://*************:9066", // hhw
          changeOrigin: true,
        },
        "/api": {
          // target: "http://************:8086",
          target: "http://************:8080",
          // target: "http://************:8086",
          // target: "http://************:8086",
          changeOrigin: true,
        },
        "/worker": {
          // target: "http://************:6066", //hgw
          target: "http://*************:6066", //hhw
          // target: "http://************:6066", //hgw
          changeOrigin: true,
        },
      },
    },
    build: {
      outDir: isOtherBuild ? "ai-system-other" : "ai-system", // 根据构建类型设置不同的输出目录
    },
    // 路径别名
    resolve: {
      alias: {
        "@": join(__dirname, "src/app"),
      },
    },
  };
});
