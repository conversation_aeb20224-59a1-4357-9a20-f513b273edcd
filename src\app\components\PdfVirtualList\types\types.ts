export interface FormattedAnnotation {
  id: string;
  text: string;
  x: [number, number];
  y: [number, number];
  page: number;
  originalWidth?: number;
  originalHeight?: number;
}

export interface RawAnnotation {
  id: number;
  head: string;
  paragraph: string;
  coords: string[];
  frameCoords: {
    page: number;
    pageSize: {
      widthPt: number;
      heightPt: number;
    };
    upLeft: number[];
    lowRight: number[];
    upLeftScale: number[];
    lowRightScale: number[];
  }[];
  paragraphId: number;
  type: string;
}

export interface Operation {
  type: string;
  label: string;
  disable?: boolean;
}

export interface PDFVirtualListProps {
  url: string;
  initialVisiblePages?: number;
  coordsData?: RawAnnotation[];
  operation?: Operation[];
  getParagraph?: (paragraph: string, type: string) => void;
}

// 新增的优化类型定义
export interface PageDimensions {
  readonly width: number;
  readonly height: number;
}

export interface PageLayout {
  readonly y: number;
  readonly height: number;
  readonly pageIndex?: number;
}

export interface VisibleRange {
  readonly start: number;
  readonly end: number;
}

export interface ScrollState {
  scrollTop: number;
  containerHeight: number;
  isScrolling: boolean;
  lastScrollTime: number;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
}

export interface PerformanceMetrics {
  scrollEventCount: number;
  renderCount: number;
  cacheHitRate: number;
  averageRenderTime: number;
}
