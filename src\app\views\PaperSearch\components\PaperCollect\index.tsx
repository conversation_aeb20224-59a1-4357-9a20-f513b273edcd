import CustomDialog from "@/components/Dialog";
import {
  Checkbox,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  TextField,
  Tooltip,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import {
  addFavorite,
  addPdfAssociation,
  getFavorites,
} from "@/api/paperSearch";
import { getErrorMessage } from "@/utils/errorParse";

interface Props {
  open: boolean;
  setOpen: (value: boolean) => void;
  reload: () => void;
  pdfId: string;
}

const DialogHeader = styled("div")(({ theme }) => ({
  width: 400,
  height: "100%",
  fontSize: "20px",
  fontWeight: "700",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  padding: theme.spacing(2),
  paddingBottom: 0,
}));

const StyledTitleText = styled("div")(({ theme }) => ({
  borderBottom: `4px solid ${theme.palette.primary.main}`,
  padding: "0 8px",
}));

const DialogClose = styled(Button)(() => ({
  minWidth: 0,
  padding: 0,
  color: "#000",
  marginTop: "-8px",
}));

const DialogContent = styled("div")(() => ({
  height: "100%",
  padding: "0 20px",
}));

const StyledList = styled(List)(({ theme }) => ({
  maxHeight: "200px",
  overflow: "auto",
  marginTop: theme.spacing(2),
  marginBottom: theme.spacing(2),
}));

const StyledListItemButton = styled(ListItemButton)(() => ({
  paddingTop: 2,
  paddingBottom: 2,
  paddingLeft: 0,
}));

const StyledListItemIcon = styled(ListItemIcon)(() => ({
  minWidth: "30px",
}));

const StyledCheckbox = styled(Checkbox)(({ theme }) => ({
  padding: 0,
  margin: `${theme.spacing()} ${theme.spacing()} ${theme.spacing()} 0`,
}));

const StyledNewFolderButton = styled(Button)(({ theme }) => ({
  width: "100%",
  marginBottom: theme.spacing(2),
  textAlign: "left",
}));

const StyledTextField = styled(TextField)(() => ({
  width: 310,
}));

const DialogFooter = styled("div")(() => ({
  width: "100%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
}));

const StyledConfirmButton = styled(Button)(() => ({
  width: "40%",
}));

interface CollectItemProps {
  id: number;
  name: string;
  pdfNumber: number;
  exist: boolean;
}

const PaperCollect: React.FC<Props> = ({ open, setOpen, reload, pdfId }) => {
  const [collectList, setCollectList] = useState<CollectItemProps[]>([]);
  const [isAdd, setIsAdd] = useState<boolean>(false);
  const [isTextError, setIsTextError] = useState<boolean>(false);
  const [toggleFlag, setToggleFlag] = useState(false);
  const [folderName, setFolderName] = useState<string>("");
  const [helper, setHelper] = useState("长度不超过20个字符");
  const handleOk = async () => {
    try {
      const favoriteItems = collectList
        .filter((item) => item.exist)
        .map((item) => ({
          pdfId: Number(pdfId),
          type: "collect",
          externalId: String(item.id),
        }));

      if (favoriteItems.length === 0) {
        message.warning("请选择要收藏到的收藏夹");
        return;
      }
      const { data } = await addPdfAssociation(favoriteItems);
      if (data.code === 200) {
        message.success("收藏成功");
      } else {
        message.error("收藏失败");
      }
    } catch (error) {
      console.error("收藏失败:", error);
      message.error("收藏失败");
    }
    setOpen(false);
    resetTextField();
    reload();
  };

  const getCollectList = async () => {
    try {
      const {
        data: { data },
      } = await getFavorites();
      const newData = data.map((item: any) => ({
        ...item,
        exist: false,
      }));
      setCollectList(
        newData && newData.length > 0 ? (newData as CollectItemProps[]) : [],
      );
    } catch (error) {
      console.log(error);
    }
  };

  const handleCheck = (item: CollectItemProps, index: number) => {
    const itemTemp = { ...item, exist: !item.exist };
    const collectListTemp = [...collectList];
    collectListTemp[index] = itemTemp;
    setCollectList(collectListTemp);
  };

  const onAddNewFolder = () => {
    setIsAdd(true);
  };

  const onChangeFolderName = (
    target: EventTarget & (HTMLTextAreaElement | HTMLInputElement),
  ) => {
    if (toggleFlag) {
      const toggle = toggleError(target.value);
      if (!toggle) {
        setIsTextError(true);
      }
      if (toggle) {
        setIsTextError(false);
        setHelper("长度不超过20个字符");
      }
    }
    setFolderName(target.value);
  };

  const toggleError = (name: string | undefined) => {
    setToggleFlag(true);
    if (!name?.trim()) {
      setHelper(name ? "收藏夹名称不可为空" : "请输入新建收藏夹名称");
      return false;
    }
    if (name.length > 20) {
      setHelper("长度不超过20个字符");
      return false;
    }
    return true;
  };

  const resetTextField = () => {
    setIsAdd(false);
    setFolderName("");
    setIsTextError(false);
    setHelper("长度不超过20个字符");
    setToggleFlag(false);
  };

  const onSaveAddFolder = async (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.code && e.code === "Enter") {
      const toggle = toggleError(folderName);
      if (!toggle) {
        setIsTextError(true);
        return;
      }
      try {
        await addFavorite({ name: String(folderName), type: "bookmarks" });
        getCollectList();
      } catch (error) {
        message.error(`新增收藏夹失败,${getErrorMessage(error)}`);
      }
      resetTextField();
    }
  };

  useEffect(() => {
    getCollectList();
  }, []);

  return (
    <CustomDialog open={open} setDialogOpen={setOpen}>
      <DialogHeader slot="header">
        <StyledTitleText>添加到收藏夹</StyledTitleText>
        <DialogClose aria-label="close" onClick={() => setOpen(false)}>
          <CloseIcon style={{ fontSize: "28px" }} />
        </DialogClose>
      </DialogHeader>
      <DialogContent slot="content">
        <StyledList>
          {collectList.map((item, index) => (
            <ListItem
              key={item.id}
              disablePadding
              secondaryAction={<ListItemText primary={item.pdfNumber} />}
            >
              <StyledListItemButton onClick={() => handleCheck(item, index)}>
                <StyledListItemIcon>
                  <StyledCheckbox edge="start" checked={item.exist} />
                </StyledListItemIcon>
                <ListItemText>{item.name}</ListItemText>
              </StyledListItemButton>
            </ListItem>
          ))}
        </StyledList>
        {!isAdd ? (
          <StyledNewFolderButton
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={onAddNewFolder}
          >
            新建收藏夹
          </StyledNewFolderButton>
        ) : (
          <>
            <StyledTextField
              label={`收藏夹名称`}
              color={"primary"}
              size={"small"}
              autoFocus={true}
              variant="outlined"
              error={isTextError}
              value={folderName}
              helperText={helper}
              autoComplete="off"
              onChange={(evt) => onChangeFolderName(evt.target)}
              onKeyDown={(e) => onSaveAddFolder(e)}
            />
            <Tooltip placement="top" title="保存">
              <IconButton
                color="primary"
                onClick={() => onSaveAddFolder({ code: "Enter" } as any)}
              >
                <CheckCircleOutlineIcon />
              </IconButton>
            </Tooltip>
            <Tooltip placement="top" title="取消">
              <IconButton onClick={resetTextField}>
                <HighlightOffIcon />
              </IconButton>
            </Tooltip>
          </>
        )}
      </DialogContent>
      <DialogFooter slot="footer">
        <StyledConfirmButton variant="contained" onClick={handleOk}>
          确定
        </StyledConfirmButton>
      </DialogFooter>
    </CustomDialog>
  );
};
export default PaperCollect;
