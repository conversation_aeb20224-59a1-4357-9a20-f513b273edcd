// 对话API
import axios from "axios";
import { anyValueProps } from "@/types/common";

// 根据chatDetailId获取详情
export const getChatDetailById = (params: anyValueProps) =>
  axios.get("/chat/api/chat/get-chat-detail-by-id", { params });

// 获取新对话chatid
export const getNewChatId = () => axios.get("/chat/api/chat/get-new-chatId");

// 实验任务生成的个数
export const getCountLoading = (params: anyValueProps) =>
  axios.get("/chat/api/chat/count-loading", { params });

// 获取用户权限内所有的用户信息
export const getUserQueryByMenu = (params: anyValueProps) =>
  axios.get("/api/auth/user/queryByMenu", { params });

// 根据用户id获取权限内所有的资料库
export const getDocumentByUid = (params: anyValueProps) =>
  axios.get("/engine/api/group/documentByUid", { params });

//分享对话到其他课题组
export const getChatShare = (params: anyValueProps) =>
  axios.post(`/chat/api/chat/share`, params);

export const speechRecognition = (data: any) =>
  axios.post("/chat/api/speech/recognition", data);

// 获取对话历史记录
export const getChatHistory = (data: anyValueProps) =>
  axios.get(`/chat/api/chat/history?page=${data.page}&size=${data.size}`);

// 获取对话历史详情
export const getChatHistoryDetail = (id: string) =>
  axios.get(`/chat/api/chat/history-detail?chatId=${id}`);

// 获取prompt
export const getChatPrompt = () => axios.get(`/chat/api/chat/prompt`);
// 清空对话
export const clearChat = (id: string) =>
  axios.get(`/chat/api/chat/delete?chatId=${id}`);

// 停止对话
export const stopChat = (data: anyValueProps) =>
  axios.post(`/chat/api/chat/stopChat`, data);

// 对话重置
export const resetSession = (params: anyValueProps) =>
  axios.get(
    `/chat/api/chat/reset-session?chatId=${params.active}&chatDetailId=${params.chatDetailId}`,
  );
// 导出对话
export const exportChat = (id: string) =>
  axios({
    url: `/chat/api/chat/export?chatId=${id}`,
    method: "get",
    responseType: "blob",
  });

// 导入对话
export const importChat = (data: anyValueProps) =>
  axios.post(`/chat/api/chat/import`, data);

// 删除某条对话
export const deleteDetailChat = (params: anyValueProps) => {
  const queryParams = new URLSearchParams();

  // 遍历 params 对象，动态添加查询参数
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString());
    }
  });
  return axios.get(`/chat/api/chat/delete—detail?${queryParams.toString()}`);
};
// 获取对话历史详情
export const chatUpdate = (data: any) =>
  axios.post("/chat/api/chat/update", data);

// 对话接口
export const chatConversation = (data: any) =>
  axios.post("/chat/api/chat", data);

// 获取库信息
export const getPaperItem = (ids: number[]) =>
  axios.post(`/engine/api/group/documentById`, ids);

// 获取库信息
export const getPaperIds = (ids: number[]) =>
  axios.post(`/engine/api/pdf/list`, ids);

// 根据id查询pdf概要信息
export const getPdfId = (id: number) =>
  axios.post(`/engine/api/pdf/get?id=${id}`);

// 根据ids查询pdf概要信息
export const getPdfIds = (ids: number[]) =>
  axios.post(`/engine/api/pdf/list`, ids);

// 生成实验方案
export const genExpstep = (params: anyValueProps) =>
  axios.post(`/chat/api/chat/gen-expsteps`, params);

// 生成实验方案data id
export const saveDataId = (params: anyValueProps) =>
  axios.post(`/worker/data`, params);

// 生成实验方案模板id
export const templateSaveDataId = (key: string) =>
  axios.get(`/worker/system/config?key=${key}`);
