import { anyValueProps } from "@/types/common";
import axios from "axios";
import { encode } from "base-64";

// 登录
export const login = (params: anyValueProps) => {
  const { password, email } = params;
  const data = {
    password: encode(password),
    email,
  };
  return axios.post("/api/public/login", data);
};

// 退出登录
export const logoutUser = () => axios.post("/api/auth/logout");

// 注册
export const register = (params: anyValueProps) => {
  const data = {
    ...params,
    password: encode(params.password),
  };
  return axios.post("/api/auth/register", data);
};

// 找回密码
export const findPassword = (params: anyValueProps) => {
  const data = {
    ...params,
    password: encode(params.password),
  };
  return axios.post("/api/auth/findPassword", data);
};

// 获取验证码
export const getVerifyCode = (params: anyValueProps) =>
  axios.get("/api/auth/verificationCode", { params });

// 获取用户信息
export const getUserInfomation = () => axios.get("/api/auth/me");

// 检查验证码
export const checkVerify = (data: anyValueProps) =>
  axios.post("/api/public/checkVerificationCode", data);

// 修改用户基本信息
export const editUserInfo = (data: anyValueProps) =>
  axios.put("/api/auth/changeBasicUserInfo", data);

// 修改公司信息
export const editDepartMent = (data: anyValueProps) =>
  axios.put("/api/auth/changeDepartmentUserInfo", data);

export const queryMenuList = async (params: any) =>
  axios({
    url: "/api/app/listMenu",
    method: "get",
    params,
  });

export const resetPassword = (params: anyValueProps) => {
  const data = {
    newPassword: encode(params.newPassword),
    password: encode(params.password),
  };
  return axios.post("/api/auth/resetPassword", data);
};

export const getLoginUrl = (params: any) =>
  axios({
    url: "/api/system/setting",
    method: "get",
    params,
  });
