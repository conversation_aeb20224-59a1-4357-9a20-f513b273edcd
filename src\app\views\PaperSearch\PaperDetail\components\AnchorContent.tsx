import React, { useMemo } from "react";
import { styled } from "@mui/material/styles";
import { Box } from "@mui/material";
import MarkdownIt from "markdown-it";
import Katex from "markdown-it-texmath";
import "katex/dist/katex.min.css";

import Reference from "./Reference";
import PaperChart from "./PaperChart";
import KeyWords from "../../components/KeyWords";
import { ContentParamsPorps } from "../common";
import En_Icon from "@/assets/en-icon.svg";
import Zh_Icon from "@/assets/zh-icon.svg";

interface PaperAnchorProps {
  contentParams: ContentParamsPorps | undefined;
}

// 常量定义
const EMPTY_DATA_TEXT = "暂无数据";
const COMMON_DOC_TYPE = "common_doc";

// 样式组件
const Root = styled("div")(({ theme }) => ({
  background: "#fff",
  paddingTop: theme.spacing(2),
  width: "100%",
  maxWidth: "100%",
  boxSizing: "border-box",
  minHeight: "100%",
}));

const BoxTitle = styled("div")(() => ({
  fontSize: 18,
  fontWeight: 700,
  lineHeight: "18px",
  color: "rgba(31, 31, 31, 1)",
  marginBottom: 14,
}));

const SectionBox = styled("div")(({ theme }) => ({
  marginBottom: theme.spacing(3),
  width: "100%",
  maxWidth: "100%",
  overflow: "hidden",
  wordWrap: "break-word",
}));

const KeywordsBox = styled("div")(({ theme }) => ({
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
  paddingBottom: theme.spacing(3),
  width: "100%",
  maxWidth: "100%",
  overflow: "hidden",
}));

const AbstractContent = styled("div")(() => ({
  fontSize: 14,
  fontWeight: 400,
  color: "rgba(64, 64, 64, 0.8)",
  lineHeight: "21px",
  verticalAlign: "top",
  textAlign: "left",
  letterSpacing: "0",
  textIndent: 0,
  marginBottom: 16,
  display: "flex",
  alignItems: "flex-start",
  width: "100%",
  maxWidth: "100%",
  overflow: "hidden",
  "& .abstract-icon": {
    width: 16,
    height: 16,
    marginRight: 8,
    flexShrink: 0,
    marginTop: 2,
  },
  "& .abstract-text": {
    flex: 1,
    minWidth: 0,
    maxWidth: "100%",
    wordWrap: "break-word",
    overflowWrap: "break-word",
    wordBreak: "break-word",
    "& p": {
      margin: 0,
      textIndent: "4ch",
      wordWrap: "break-word",
      overflowWrap: "break-word",
      maxWidth: "100%",
    },
    "& *": {
      maxWidth: "100%",
      wordWrap: "break-word",
      overflowWrap: "break-word",
    },
  },
}));

const EmptyBox = styled("div")(({ theme }) => ({
  width: "100%",
  padding: theme.spacing(2),
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  boxSizing: "border-box",
  color: "#67676A",
}));

const PictureBox = styled("div")(() => ({
  width: "100%",
}));

const KeywordsContainer = styled("div")(() => ({
  display: "flex",
  flexDirection: "column",
  gap: 12,
}));

const KeywordsRow = styled("div")(() => ({
  display: "flex",
  alignItems: "flex-start",
  gap: 8,
  "& .keywords-icon": {
    width: 16,
    height: 16,
    flexShrink: 0,
    marginTop: 2,
  },
  "& .keywords-content": {
    flex: 1,
  },
}));

const PaperAnchor: React.FC<PaperAnchorProps> = ({ contentParams }) => {
  // 使用 useMemo 优化 MarkdownIt 实例创建
  const markdownRenderer = useMemo(() => {
    const mdi = new MarkdownIt({
      html: true,
      linkify: false,
    });

    mdi.use(Katex, {
      engine: "katex",
      delimiters: [
        "brackets",
        ["[", "]"],
        "dollars",
        "doxygen",
        "gitlab",
        "julia",
        "kramdown",
        "beg_end",
      ],
      displayMode: true,
    });

    return mdi;
  }, []);

  // 计算派生状态
  const isTestReport = contentParams?.docType === COMMON_DOC_TYPE;
  const hasAbstract = contentParams?.abstract;
  const hasZhAbstract = contentParams?.zhAbstract;
  const hasKeywords =
    contentParams?.keywords && contentParams.keywords.length > 0;
  const hasZhKeywords =
    contentParams?.zhKeywords && contentParams.zhKeywords.length > 0;
  const pdfId = contentParams?.id || "";
  const shouldShowIcons = hasKeywords && hasZhKeywords;
  const shouldShowAbstractIcons = hasAbstract && hasZhAbstract;

  // 渲染抽象内容
  const renderAbstractContent = useMemo(() => {
    if (!hasAbstract || !contentParams) return null;

    const englishContent = shouldShowAbstractIcons
      ? `<img src="${En_Icon}" alt="English" class="abstract-icon" /><div class="abstract-text">${markdownRenderer.render(
          contentParams.abstract.replace(/;/g, " "),
        )}</div>`
      : `<div class="abstract-text">${markdownRenderer.render(
          contentParams.abstract.replace(/;/g, " "),
        )}</div>`;

    const chineseContent = hasZhAbstract
      ? shouldShowAbstractIcons
        ? `<img src="${Zh_Icon}" alt="中文" class="abstract-icon" /><div class="abstract-text">${markdownRenderer.render(
            contentParams.zhAbstract.replace(/;/g, " "),
          )}</div>`
        : `<div class="abstract-text">${markdownRenderer.render(
            contentParams.zhAbstract.replace(/;/g, " "),
          )}</div>`
      : "";

    return (
      <>
        {hasZhAbstract && (
          <AbstractContent
            dangerouslySetInnerHTML={{
              __html: chineseContent,
            }}
          />
        )}
        <AbstractContent
          dangerouslySetInnerHTML={{
            __html: englishContent,
          }}
        />
      </>
    );
  }, [
    hasAbstract,
    hasZhAbstract,
    contentParams?.abstract,
    contentParams?.zhAbstract,
    markdownRenderer,
    shouldShowIcons,
  ]);

  return (
    <Root>
      {/* 目录部分 - 仅测试报告显示 */}
      {/* {isTestReport && (
        <SectionBox>
          <BoxTitle>目录</BoxTitle>
          {contentParams?.catalogue ? (
            <TableOfContents
              data={contentParams?.catalogue}
              maxHeight="400px"
            />
          ) : (
            <EmptyBox>{EMPTY_DATA_TEXT}</EmptyBox>
          )}
        </SectionBox>
      )} */}

      {/* 摘要部分 - 非测试报告显示 */}
      {!isTestReport && (
        <SectionBox>
          <BoxTitle>摘要</BoxTitle>
          {hasAbstract ? (
            renderAbstractContent
          ) : (
            <EmptyBox>{EMPTY_DATA_TEXT}</EmptyBox>
          )}
        </SectionBox>
      )}

      {/* 关键词部分 - 非测试报告显示 */}
      {!isTestReport && (
        <KeywordsBox>
          <BoxTitle>关键词</BoxTitle>
          {hasKeywords || hasZhKeywords ? (
            <KeywordsContainer>
              {hasZhKeywords && (
                <KeywordsRow>
                  {shouldShowIcons && (
                    <img src={Zh_Icon} alt="中文" className="keywords-icon" />
                  )}
                  <div className="keywords-content">
                    <KeyWords data={contentParams!.zhKeywords} />
                  </div>
                </KeywordsRow>
              )}
              {hasKeywords && (
                <KeywordsRow>
                  {shouldShowIcons && (
                    <img
                      src={En_Icon}
                      alt="English"
                      className="keywords-icon"
                    />
                  )}
                  <div className="keywords-content">
                    <KeyWords data={contentParams!.keywords} />
                  </div>
                </KeywordsRow>
              )}
            </KeywordsContainer>
          ) : (
            <EmptyBox>{EMPTY_DATA_TEXT}</EmptyBox>
          )}
        </KeywordsBox>
      )}

      {/* 图表部分 */}
      <PictureBox>
        <PaperChart
          type={0}
          pdfId={pdfId}
          imgCount={contentParams?.figureCount || 0}
        />
        <PaperChart
          type={1}
          pdfId={pdfId}
          imgCount={contentParams?.tableCount || 0}
        />
      </PictureBox>

      {/* 参考文献部分 - 非测试报告显示 */}
      {!isTestReport && (
        <Box sx={{ mb: 2 }}>
          <Reference
            list={contentParams?.bibls || []}
            language={contentParams?.language || ""}
          />
        </Box>
      )}
    </Root>
  );
};

export default PaperAnchor;
