export interface frameCoordinatesProps {
  lowRight: number[];
  lowRightScale: number[];
  page: number;
  upLeft: number[];
  upLeftScale: number[];
}

export interface ParagraphProps {
  coord: string[];
  frameCoords: frameCoordinatesProps[];
  paragraph: string;
  paragraphId: number;
}

export interface PdfDataProps {
  pdfName: string;
  paragraph: ParagraphProps[];
}

export interface AnnotationProps {
  id: string;
  page: number;
  text: string;
  x: number[];
  y: number[];
}

export interface OperationProps {
  type: string;
  label: string;
}

export interface TextSelectionProp {
  text: string;
  y: number;
}
