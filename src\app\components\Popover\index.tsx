import React from "react";
import { Box, Button, Popover, Typography } from "@mui/material";
import ErrorIcon from "@mui/icons-material/Error";

type horizontalType = number | "center" | "left" | "right";

interface PopconfirmProps {
  title: string;
  anchorEl: HTMLButtonElement | null;
  horizontal?: horizontalType;
  handleConfirm: () => void;
  handleClose: () => void;
}
const PopConfirm: React.FC<PopconfirmProps> = ({
  title,
  anchorEl,
  handleConfirm,
  handleClose,
  horizontal,
}) => (
  <Popover
    open={Boolean(anchorEl)}
    anchorEl={anchorEl}
    onClose={handleClose}
    anchorOrigin={{
      vertical: "bottom",
      horizontal: horizontal ? horizontal : "left",
    }}
  >
    <Box sx={{ p: 2 }}>
      <Typography sx={{ display: "flex", alignItems: "center" }}>
        <ErrorIcon color="warning" sx={{ mr: 1 }} />
        {title}
      </Typography>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          mt: 1,
        }}
      >
        <Button
          variant="outlined"
          size="small"
          sx={{ mr: 1, height: "32px" }}
          onClick={handleClose}
        >
          取消
        </Button>
        <Button
          sx={{ height: "32px" }}
          variant="contained"
          size="small"
          onClick={handleConfirm}
        >
          确定
        </Button>
      </Box>
    </Box>
  </Popover>
);

export default PopConfirm;
