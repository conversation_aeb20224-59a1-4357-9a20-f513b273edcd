import React from "react";
import KeyWords from "../KeyWords";
import { anyValueProps } from "@/types/common";
import PaperPopover from "../PaperPopover";
import { PERMISSION_MENU } from "@/utils/permission";
import { withPermission } from "@/components/HocButton";
import { useAppSelector } from "@/hooks";
import AdminPaperPopover from "../PaperPopover/AdminPaperPopover";
import { checkPermission } from "@/utils/auth";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setBreadcrumb } from "@/store/breadcrumbSlice";
// import MarkdownIt from "markdown-it";
// import Katex from "markdown-it-texmath";
const ArticleTitle = styled(Typography, {
  shouldForwardProp: (prop) => prop !== "isTextTitle" && prop !== "isFontSize",
})<{ isTextTitle: boolean | undefined; isFontSize: boolean | undefined }>(
  ({ theme, isTextTitle, isFontSize }) => ({
    marginBottom: 10,
    cursor: isTextTitle ? "auto" : "pointer",
    lineHeight: "21px",
    fontWeight: isFontSize ? 500 : 600,
    display: "-webkit-box",
    WebkitBoxOrient: "vertical",
    WebkitLineClamp: 1,
    overflow: "hidden",
    borderBottom: "1px solid transparent",
    fontSize: isFontSize ? 16 : 18,
    "& span": {
      "&:hover": {
        borderBottom: isTextTitle
          ? ""
          : `1px solid ${theme.palette.primary.main}`,
        color: isTextTitle ? "" : theme.palette.primary.main,
      },
    },
  }),
);
// const ArticleSubTitle = styled(Typography)(() => ({
//   marginBottom: 10,
//   lineHeight: "21px",
//   "& .highlight": {
//     "& .abstract-text": {
//       "& p": {
//         margin: 0,
//         width: "100%",
//         display: "-webkit-box",
//         WebkitBoxOrient: "vertical",
//         overflow: "hidden",
//         WebkitLineClamp: 1,
//       },
//     },
//   },
// }));

// const ArticleSubTitleText = styled(Typography)(() => ({
//   marginBottom: 10,
//   lineHeight: "21px",
//   display: "-webkit-box",
//   WebkitBoxOrient: "vertical",
//   overflow: "hidden",
//   WebkitLineClamp: 2,
//   "& .highlight": {
//     "& .abstract-text": {
//       "& p": {
//         margin: 0,
//         width: "100%",
//         display: "-webkit-box",
//         WebkitBoxOrient: "vertical",
//         overflow: "hidden",
//         WebkitLineClamp: 1,
//       },
//     },
//   },
// }));
const ArticleDetail = styled("div")(({ maxWidth }: { maxWidth: number }) => ({
  height: 16,
  maxWidth,
  fontSize: 14,
  lineHeight: 1,
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  display: "table-cell",
}));
const ArticleDetailDiv = styled("div")(() => ({
  height: 16,
  fontSize: 14,
  lineHeight: 1,
}));

const DetailContainer = styled("div")(({ theme }) => ({
  marginBottom: 10,
  color: theme.typography.body2.color,
}));

const StyleButton = styled("span")(() => ({
  cursor: "pointer",
  fontSize: 14,
  fontWeight: 400,
  color: "rgba(0, 48, 122, 1)",
  marginRight: 16,
}));

interface ArticleItemProps {
  articleInfo: anyValueProps;
  addArt?: boolean;
  addAi?: boolean;
  preview?: boolean;
  del?: boolean;
  closeTab?: () => void;
  chatPaper?: (id: number) => void;
  handlePreview?: () => void;
  downLoadPdf?: () => void;
  type?: string;
  searchId?: string;
  isTextTitle?: boolean;
  download?: boolean;
  isFontSize?: boolean;
  isSearch?: boolean;
  index?: number;
}

const AiChatButton = ({ action }: { action: any }) => {
  const StyleAiChatButton: React.FC<any> = ({ action }) => (
    <StyleButton onClick={action}>+AI对话</StyleButton>
  );
  const PermissionButton = withPermission(
    StyleAiChatButton,
    PERMISSION_MENU["chat"],
  );
  return <PermissionButton action={action} />;
};

const AddPaperBaseButton = ({ id }: any) => {
  const StyleAiChatButton: React.FC<any> = ({ id }) => {
    const { roleOption } = useAppSelector((state) => state.user);
    const [pdfId, setPdfId] = useState<string>("");
    const [anchorEl, setAnchorEl] = useState<null | HTMLButtonElement>(null);
    const [adminanchorEl, setAdminAnchorEl] =
      useState<null | HTMLButtonElement>(null);
    const onClickPaperBase = (event: any) => {
      if (!id) return;
      setPdfId(id);
      if (checkPermission(roleOption)) {
        setAdminAnchorEl(event.currentTarget);
      } else {
        setAnchorEl(event.currentTarget);
      }
    };

    return (
      <>
        <StyleButton onClick={(e: any) => onClickPaperBase(e)}>
          加入资料库
        </StyleButton>
        {Boolean(anchorEl) && (
          <PaperPopover
            id={anchorEl ? "simple-popover" : undefined}
            open={Boolean(anchorEl)}
            onClose={() => setAnchorEl(null)}
            anchorEl={anchorEl}
            pdfId={pdfId}
            anchorOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
            transformOrigin={{
              vertical: "bottom",
              horizontal: "left",
            }}
          />
        )}
        {Boolean(adminanchorEl) && (
          <AdminPaperPopover
            id={adminanchorEl ? "simple-popover" : undefined}
            open={Boolean(adminanchorEl)}
            onClose={() => setAdminAnchorEl(null)}
            anchorEl={adminanchorEl}
            pdfId={pdfId}
          />
        )}
      </>
    );
  };
  const PermissionButton = withPermission(
    StyleAiChatButton,
    PERMISSION_MENU["edit"],
  );
  return <PermissionButton id={id} />;
};

const ArticleItem: React.FC<ArticleItemProps> = ({
  articleInfo,
  addArt,
  addAi,
  preview,
  del,
  closeTab,
  chatPaper,
  handlePreview,
  downLoadPdf,
  type,
  searchId,
  isTextTitle,
  download,
  isFontSize,
  isSearch,
}) => {
  const {
    title,
    authors,
    pdfId,
    keywords,
    externalId,
    index,
    docType,
    ext2,
    // paragraph,
    // text,
    // secondPara,
    // journal,
    // publishedYear,
  } = articleInfo;
  const navigator = useNavigate();
  const dispatch = useDispatch();
  // const markdownRenderer = useMemo(() => {
  //   const mdi = new MarkdownIt({
  //     html: true,
  //     linkify: false,
  //   });

  //   mdi.use(Katex, {
  //     engine: "katex",
  //     delimiters: [
  //       "brackets",
  //       ["[", "]"],
  //       "dollars",
  //       "doxygen",
  //       "gitlab",
  //       "julia",
  //       "kramdown",
  //       "beg_end",
  //     ],
  //     displayMode: true,
  //   });

  //   return mdi;
  // }, []);
  const showHtml = (htmlString: string) => (
    <span
      className="highlight"
      dangerouslySetInnerHTML={{
        __html: htmlString,
      }}
    />
  );
  // 点击标题跳转详情
  const onClickTitle = async (id: number) => {
    if (!id) return;
    if (type === "searchResult") {
      window.open(
        `${window.APP_CONFIG.BASE_PATH}/#/paper-search/paper-details/searchResult?id=${id}&searchId=${searchId}`,
      );
    } else if (type === "aiDetail") {
      dispatch(setBreadcrumb([]));
      window.open(
        window.APP_CONFIG.BASE_PATH +
          `/#/paper-search/paper-details/paperbase?id=${id}`,
      );
    } else {
      navigator(`/paper-search/paper-details/home?id=${id}`);
    }
  };

  const handleAction = (type: string) => {
    switch (type) {
      case "preview":
        handlePreview && handlePreview();
        break;
      case "ai":
        chatPaper && chatPaper(pdfId);
        break;
      case "del":
        closeTab && closeTab();
        break;
      case "download":
        downLoadPdf && downLoadPdf();
        break;
    }
  };

  const removeEmTags = (htmlString: string) => {
    if (!htmlString) return "";
    return htmlString?.replace?.(/<\/?em[^>]*>/gi, "");
  };
  // 使用正则表达式匹配所有em标签及其属性

  const isKeywords = keywords.includes("|||");
  const isAuthors = authors.includes("|||");
  const splitArrayBySeparator = (arr: any[], separator = "|||") =>
    arr.reduce(
      (result, item) => {
        if (item === separator) {
          result.push([]); // 遇到分隔符时新建一个子数组
        } else {
          result[result.length - 1].push(item); // 将元素添加到当前子数组
        }
        return result;
      },
      [[]],
    ); // 初始值包含一个空子数组
  return (
    <div style={{ width: "100%" }}>
      {/* 资料标题 */}
      {/\|\|\|/.test(title) ? (
        title
          .replace(/#/g, "")
          .split("|||")
          .map((item: string, titleIndex: number) => (
            <div key={titleIndex}>
              {item ? (
                <ArticleTitle
                  variant="subtitle1"
                  onClick={() => onClickTitle(externalId || pdfId)}
                  title={removeEmTags(item)}
                  isTextTitle={isTextTitle}
                  isFontSize={isFontSize}
                >
                  {index && titleIndex !== 1 ? index + "." : " "}
                  {item && showHtml(item)}
                </ArticleTitle>
              ) : (
                ""
              )}
            </div>
          ))
      ) : (
        <ArticleTitle
          variant="subtitle1"
          onClick={() => onClickTitle(externalId || pdfId)}
          title={removeEmTags(title)}
          isTextTitle={isTextTitle}
          isFontSize={isFontSize}
        >
          {index && index + "."}
          {title ? showHtml(title) : "暂无标题"}
        </ArticleTitle>
      )}
      {/* 关键词 */}
      {keywords?.length > 0 && isKeywords ? (
        <>
          <Box sx={{ mb: "10px" }}>
            {/* 中文关键词部分（||| 之前） */}
            <KeyWords
              data={
                isSearch
                  ? keywords
                      .split("|||")[0]
                      .split(";")
                      .filter((item: string) => item.trim() !== "")
                  : splitArrayBySeparator(keywords)[0].filter(
                      (item: string) => item !== "",
                    )
              }
            />
          </Box>
          <Box sx={{ mb: "10px" }}>
            {/* 英文关键词部分（||| 之后） */}
            <KeyWords
              data={
                isSearch
                  ? keywords
                      .split("|||")[1]
                      .split(";")
                      .filter((item: string) => item.trim() !== "")
                  : splitArrayBySeparator(keywords)[1].filter(
                      (item: string) => item !== "",
                    )
              }
            />
          </Box>
        </>
      ) : keywords?.length ? (
        <Box sx={{ mb: "10px" }}>
          <KeyWords
            data={
              isSearch
                ? keywords.split(";").filter((item: string) => item !== "")
                : keywords.filter((item: string) => item !== "")
            }
          />
        </Box>
      ) : (
        ""
      )}

      {/* 中文资料展示 */}
      {/* {secondPara && (
        <ArticleSubTitle variant="body2" title={removeEmTags(secondPara)}>
          {showHtml(
            `<div class="abstract-text">${markdownRenderer.render(
              secondPara,
            )}</div>`,
          )}
        </ArticleSubTitle>
      )} */}

      {/* 资料内容 */}
      {/* {paragraph && (
        <ArticleSubTitle
          variant="body2"
          title={removeEmTags(paragraph.replace(/#/g, ""))}
        >
          {showHtml(
            `<div class="abstract-text">${markdownRenderer.render(
              paragraph,
            )}</div>`,
          )}
        </ArticleSubTitle>
      )} */}
      {/* 检索展示文章段落 */}
      {/* {text && (
        <ArticleSubTitleText variant="body2" title={removeEmTags(text)}>
          {showHtml(markdownRenderer.render(text))}
        </ArticleSubTitleText>
      )} */}
      <DetailContainer>
        {authors && isAuthors ? (
          <>
            <ArticleDetail
              maxWidth={300}
              title={removeEmTags(
                isSearch
                  ? authors.split("; |||;")[0]
                  : splitArrayBySeparator(authors)[0].join(),
              )}
            >
              {isSearch
                ? authors.split("; |||;")[0]
                : splitArrayBySeparator(authors)[0].join(";")}
            </ArticleDetail>
            <DetailContainer sx={{ marginTop: "10px" }}>
              <ArticleDetail
                maxWidth={400}
                title={removeEmTags(
                  isSearch
                    ? authors.split("; |||;")[1]
                    : splitArrayBySeparator(authors)[1].join(";"),
                )}
              >
                {isSearch
                  ? authors.split("; |||;")[1]
                  : splitArrayBySeparator(authors)[1].join(";")}
              </ArticleDetail>
            </DetailContainer>
          </>
        ) : authors.length ? (
          <ArticleDetail
            maxWidth={300}
            title={removeEmTags(
              !isSearch ? authors.join(";") : authors.split(";").join(","),
            )}
          >
            {!isSearch ? authors.join(";") : authors.split(";")}
          </ArticleDetail>
        ) : (
          ""
        )}
      </DetailContainer>

      <DetailContainer>
        {docType === "common_doc" && ext2 && (
          <ArticleDetailDiv>{ext2}</ArticleDetailDiv>
        )}
      </DetailContainer>
      {/* <DetailContainer>
        {journal && (
          <ArticleDetailDiv title={removeEmTags(journal)}>
            {showHtml(journal)}
          </ArticleDetailDiv>
        )}
      </DetailContainer>
      <DetailContainer>
        {publishedYear && ![0, "0"].includes(publishedYear) && (
          <ArticleDetailDiv>{publishedYear}</ArticleDetailDiv>
        )}
      </DetailContainer> */}
      <>
        {addArt && <AddPaperBaseButton id={externalId || pdfId} />}
        {addAi && <AiChatButton action={() => handleAction("ai")} />}
        {preview && (
          <StyleButton onClick={() => handleAction("preview")}>
            预览
          </StyleButton>
        )}
        {del && (
          <StyleButton
            onClick={() => handleAction("del")}
            style={{ color: "rgba(255, 87, 51, 1)" }}
          >
            删除
          </StyleButton>
        )}
        {download && (
          <StyleButton onClick={() => handleAction("download")}>
            下载
          </StyleButton>
        )}
      </>
    </div>
  );
};

export default ArticleItem;
