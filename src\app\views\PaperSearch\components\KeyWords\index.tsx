interface Props {
  data: any[];
}

const KeyWordBox = styled("div")(() => ({
  width: "100%",

  maxHeight: "500px",
  display: "flex",
  overflow: "auto",
  flexWrap: "wrap",
  gap: 10,
}));

const KeyWordsItem = styled("div")(({ theme }) => ({
  background: "rgba(222, 235, 255, 1)",
  maxWidth: 240,
  overflow: "hidden",
  textOverflow: "ellipsis",
  padding: 5,
  fontSize: 14,
  lineHeight: "14px",
  fontWeight: 400,
  color: theme.palette.primary.main,
  whiteSpace: "nowrap",
  flexShrink: 0,
}));

const KeyWords: React.FC<Props> = ({ data }) => {
  // const removeEmTags = (htmlString: string): string =>
  //   // 使用正则表达式匹配所有em标签及其属性
  //   htmlString.replace(/<\/?em[^>]*>/gi, "");
  const showHtml = (htmlString: string) => (
    <span
      className="highlight"
      dangerouslySetInnerHTML={{
        __html: htmlString,
      }}
    />
  );
  return (
    <KeyWordBox>
      {data.length
        ? data.map((item, index) => (
            <KeyWordsItem key={index} title={item}>
              {showHtml(item)}
            </KeyWordsItem>
          ))
        : null}
    </KeyWordBox>
  );
};
export default KeyWords;
