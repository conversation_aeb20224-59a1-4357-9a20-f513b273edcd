import React, { useRef, useEffect, useMemo } from "react";
import { Document } from "react-pdf";
import "react-pdf/dist/Page/TextLayer.css";
import "react-pdf/dist/Page/AnnotationLayer.css";

import PdfStyles from "./components/PdfStyles";
import OperationBar from "./components/OperationBar";
import PdfPageRenderer from "./components/PdfPageRenderer";
import { PdfError } from "./components/PdfLoaders";
import WorkerError from "./components/WorkerError";
import { usePdfVirtualList } from "./hooks/usePdfVirtualList";
import { PDFVirtualListProps } from "./types/types";
import { validateWorker } from "./utils/pdfUtils";

// 动态获取cMapUrl路径
const getCMapUrl = (): string => {
  // 1. 优先使用全局配置的BASE_PATH
  if (typeof window !== "undefined" && (window as any).APP_CONFIG?.BASE_PATH) {
    return `${(window as any).APP_CONFIG.BASE_PATH}/cmaps/`;
  }

  // 2. 检查是否在开发环境
  if (process.env.NODE_ENV === "development") {
    return "/cmaps/";
  }

  // 3. 尝试从当前路径推断base path
  if (typeof window !== "undefined") {
    const currentPath = window.location.pathname;
    if (currentPath.includes("/aihub-chat")) {
      return "/aihub-chat/cmaps/";
    }
  }

  // 4. 默认路径
  return "/cmaps/";
};

const options = {
  cMapUrl: getCMapUrl(),
};
const PDFVirtualList: React.FC<PDFVirtualListProps> = ({
  url,
  initialVisiblePages = 3,
  coordsData,
  operation,
  getParagraph,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const file = useMemo(() => ({ url }), [url]);
  const [workerError, setWorkerError] = useState<string | null>(null);

  // 验证PDF worker
  useEffect(() => {
    const checkWorker = async () => {
      try {
        const isValid = await validateWorker();
        if (!isValid) {
          setWorkerError("PDF Worker 加载失败，请检查网络连接或刷新页面重试");
        } else {
          setWorkerError(null);
        }
      } catch (error) {
        console.error("Worker validation error:", error);
        setWorkerError("PDF Worker 验证失败");
      }
    };

    checkWorker();
  }, []);

  const {
    numPages,
    pageLayouts,
    visibleRange,
    currentPage,
    scale,
    containerWidth,
    isDocumentLoaded,
    updatePageHeight,
    updatePageDimensions,
    handlePageChange,
    handleDocumentLoadSuccess,
    handleDocumentLoadError,
    setScale,
    calculateTotalHeight,
    formattedAnnotations,
    forceUpdatePageGaps,
    scrollToPage,
    getStats,
    clearCaches,
  } = usePdfVirtualList({
    initialVisiblePages,
    containerRef,
    coordsData,
  });

  const totalHeight = calculateTotalHeight();

  // 监听缩放变化，强制更新页面间距
  useEffect(() => {
    if (isDocumentLoaded && scale > 0) {
      // 延迟一下，等待页面渲染完成
      const timeoutId = setTimeout(() => {
        forceUpdatePageGaps();
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [scale, isDocumentLoaded, forceUpdatePageGaps]);

  // 开发模式下的性能监控
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      const interval = setInterval(() => {
        const stats = getStats();
        console.log("PDF Virtual List Performance Stats:", stats);
      }, 10000); // 每10秒输出一次统计信息

      return () => clearInterval(interval);
    }
  }, [getStats]);

  // 组件卸载时清理缓存
  useEffect(() => {
    return () => {
      clearCaches();
    };
  }, [clearCaches]);

  return (
    <div
      style={{
        position: "relative",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <PdfStyles />
      <OperationBar
        currentPage={currentPage}
        totalPages={numPages}
        scale={scale}
        onPageChange={handlePageChange}
        onScaleChange={setScale}
      />
      <div
        style={{
          flex: 1,
          position: "relative",
          display: "flex",
          justifyContent: "center",
          overflow: "hidden",
        }}
      >
        <div
          ref={containerRef}
          id="pdf-virtual-list-container"
          style={{
            width: "100%",
            height: "100%",
            overflow: "auto",
            padding: "0",
            boxSizing: "border-box",
            backgroundColor: "#fff",
            WebkitOverflowScrolling: "touch",
            touchAction: "auto",
            userSelect: "none",
          }}
        >
          {workerError ? (
            <WorkerError
              error={workerError}
              onRetry={async () => {
                setWorkerError(null);
                const isValid = await validateWorker();
                if (!isValid) {
                  setWorkerError("PDF Worker 仍然无法加载，请刷新页面重试");
                }
              }}
            />
          ) : (
            <Document
              key={url}
              file={file}
              onLoadSuccess={handleDocumentLoadSuccess}
              onLoadError={handleDocumentLoadError}
              loading=""
              error={<PdfError />}
              externalLinkTarget="_blank"
              options={options}
            >
              {isDocumentLoaded && numPages > 0 ? (
                <div
                  style={{
                    minHeight: "100%",
                    position: "relative",
                    height: totalHeight,
                    width: scale > 1 ? `${containerWidth * scale}px` : "100%",
                    maxWidth:
                      scale > 1 ? `${containerWidth * scale}px` : "100%",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                  }}
                >
                  <PdfPageRenderer
                    visibleRange={visibleRange}
                    numPages={numPages}
                    pageLayouts={pageLayouts}
                    containerWidth={containerWidth}
                    scale={scale}
                    updatePageHeight={updatePageHeight}
                    updatePageDimensions={updatePageDimensions}
                    annotations={formattedAnnotations}
                    operation={operation}
                    getParagraph={getParagraph}
                  />
                </div>
              ) : null}
            </Document>
          )}
        </div>
      </div>
    </div>
  );
};

export default PDFVirtualList;
