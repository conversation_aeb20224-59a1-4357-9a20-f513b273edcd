import { BpCheckbox, SelectType, LoginColumns } from "../setting";
import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { useNavigate } from "react-router-dom";
// import { login } from "@/api/login";
import { login } from "@/store/user";
import { LoadingButton } from "@mui/lab";
import SliderVerify from "./SliderVerify";
import { useAppDispatch } from "@/hooks";

interface Props {
  setAction: (value: SelectType) => void;
}

const LoginCard = styled("div")(() => ({
  width: 400,
  borderRadius: 20,
  background:
    "radial-gradient(116.67% 69.36% at 17.083333333333332% -40.06734006734007%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(255, 255, 255, 1)",
  padding: "10px 41px 40px 39px",
  boxSizing: "border-box",
  paddingBottom: 40,
}));

const FormLabel = styled("div")(() => ({
  marginBottom: 30,
  boxSizing: "border-box",
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
  paddingBottom: 20,
}));

const LoginTitle = styled("div")(() => ({
  height: 57,
  marginBottom: 20,
  display: "flex",
  alignItems: "flex-end",
  justifyContent: "space-between",
}));

const LoginLabel = styled("div")(() => ({
  fontSize: 18,
  fontWeight: 700,
  color: "rgba(64, 64, 64, 1)",
  marginLeft: "calc(50% - 36px)",
}));

const OnTrialButton = styled("div")(() => ({
  fontSize: 14,
  fontWeight: 400,
  color: "rgba(24, 112, 199, 1)",
  cursor: "pointer",
  marginRight: 12,
}));

const SubmitBox = styled("div")(() => ({
  width: "100%",
}));

const ActionBar = styled("div")(() => ({
  width: "100%",
  fontSize: "14px",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: 15,
}));

const KeepLogged = styled("div")(() => ({
  fontSize: 14,
  display: "flex",
}));

const RegisterButton = styled("div")(() => ({
  cursor: "pointer",
  fontSize: 14,
  fontWeight: 400,
  color: "rgba(24, 112, 199, 1)",
  marginLeft: 10,
  marginTop: 12,
}));

const LoginSide = styled("div")(() => ({
  width: "100%",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
}));

const LoginButton = styled(LoadingButton)(() => ({
  height: 56,
  borderRadius: 28,
  background:
    "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(40, 139, 252, 1) 100%)",
}));

const LoginBox: React.FC<Props> = ({ setAction }) => {
  const navigator = useNavigate();
  const dispatch = useAppDispatch();
  const formRef = useRef<RefProps>(null);
  const [remember, setRemeber] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [verifyCheck, setVerifyCheck] = useState<boolean>(false);

  const handleLogin = () => {
    formRef.current?.submit().then(async (res: any) => {
      setLoading(true);
      if (!verifyCheck) {
        message.error("请滑动滑块验证");
        setLoading(false);
        return;
      }
      try {
        dispatch(
          login({
            ...res,
            keepLoggedIn: remember,
          }),
        ).then(() => {
          setTimeout(() => {
            setLoading(false);
            navigator("/");
          }, 100);
        });
      } catch (error: any) {
        setLoading(false);
        console.error(error.response.data.msg || "登录失败");
      }
    });
  };

  const handleStay = (event: React.ChangeEvent<any>) => {
    setRemeber(event.target.checked);
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === "Enter" || event.key === "NumpadEnter") {
      handleLogin();
    }
  };

  const handleVerifySuccess = () => {
    setVerifyCheck(true);
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  return (
    <LoginCard>
      <LoginTitle>
        <LoginLabel>账号登录</LoginLabel>
        <OnTrialButton onClick={() => setAction("trialuse")}>
          申请试用
        </OnTrialButton>
      </LoginTitle>
      <FormLabel>
        <DynamicForm
          ref={formRef}
          columns={LoginColumns}
          size="small"
          rowSpacing={1.5}
          direction="column"
        />
        <div style={{ marginTop: 25, boxSizing: "border-box" }}>
          <SliderVerify onSuccess={handleVerifySuccess} />
        </div>
      </FormLabel>
      <SubmitBox>
        <ActionBar>
          <KeepLogged>
            <BpCheckbox
              size="small"
              onChange={handleStay}
              value={remember}
              defaultChecked={true}
            />
            <div
              style={{
                fontSize: 14,
                fontWeight: 400,
                color: "rgba(125, 125, 125, 1)",
              }}
            >
              记住登录状态
            </div>
          </KeepLogged>
        </ActionBar>
        <LoginSide>
          <LoginButton
            loading={loading}
            variant="contained"
            fullWidth
            onClick={handleLogin}
          >
            登录
          </LoginButton>
          <RegisterButton onClick={() => setAction("forgotpw")}>
            忘记密码？
          </RegisterButton>
        </LoginSide>
      </SubmitBox>
    </LoginCard>
  );
};
export default LoginBox;
