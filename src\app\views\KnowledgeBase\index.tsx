import Breadcrumb from "@/components/Breadcrumb";
import React from "react";
import { Grid2 as Grid } from "@mui/material";
import { disableKnowledge, getKnowledgeAll } from "@/api/knowledgeBase";
// import MessageCard from "./components/MessageCard";
// import MyPaperPagination, { PageProps } from "@/components/MyPaperPagination";
import { PageProps } from "@/components/MyPaperPagination";
import PopoverDelete from "@/components/Popover";
// import AddIcon from "@mui/icons-material/Add";
// import PopUp from "./components/Popup";
// import Empty from "@/assets/empty.png";
import {
  buttonGroup,
  databaseButtonGroup,
  knowledgeDataProps,
  knowledgeOptionProps,
  searchColumns,
} from "./components/common";
import { useNavigate } from "react-router-dom";
import DynamicForm from "@/components/DynamicForm";
import SortPart from "./components/SortPart";
import ButtonGroup from "./components/ButtonGroup";
import CardTable from "@/components/CardTable";
// import { useSelector } from "react-redux";
import AddPaperBaseDialog from "../PaperBase/components/PersonalPaper/components/PersonalPaperDialog/AddPaperBaseDialog";
import { useQuery } from "@tanstack/react-query";
const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
}));

const Main = styled("div", {
  shouldForwardProp: (p) => p !== "isDialog",
})<{
  isDialog: boolean;
}>(({ isDialog }) => ({
  flex: 1,
  display: "flex",
  padding: isDialog ? 0 : "18px 41px 10px 41px",
}));

const List = styled("div", {
  shouldForwardProp: (p) => p !== "isDialog",
})<{ isDialog: boolean }>(({ isDialog }) => ({
  width: "200px",
  height: "100%",
  boxSizing: "border-box",
  borderRadius: 20,
  paddingTop: "20px",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  background:
    "radial-gradient(400% 69.42% at 17.272727272727273% -40.128755364806864%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(255, 255, 255, 1)",
  border: isDialog ? "" : "2px solid rgba(255, 255, 255, 1)",
}));
const ListItem = styled("div")<{ active: boolean; isDialog: boolean }>(
  ({ active, isDialog }) => ({
    width: 180,
    height: 48,
    padding: "15px 10px",
    textAlign: "center",
    background: active
      ? `linear-gradient(90deg, rgba(19, 108, 191, 0.1) 0%, rgba(38, 124, 222, 0.1) 100%)`
      : "transparent",
    color: active ? "rgba(24, 112, 199, 1)" : "rgba(64, 64, 64, 1)",
    display: "flex",
    justifyContent: isDialog ? "center" : "space-between",
    alignItems: "center",
    cursor: "pointer",
    boxSizing: "border-box",
    borderRadius: 16,
    marginBottom: 10,
    ":hover": {
      background: `linear-gradient(90deg, rgba(19, 108, 191, 0.1) 0%, rgba(38, 124, 222, 0.1) 100%)`,
      color: "rgba(24, 112, 199, 1)",
    },
  }),
);

const SearchStyle = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  marginBottom: "20px",
  justifyContent: "space-between",
  background: "#fff",
  borderRadius: 16,
  height: 64,
}));

const ContentBox = styled("div")(() => ({
  flex: 1,
  marginLeft: "20px",
  display: "flex",
  flexDirection: "column",
}));

const ContentDiv = styled("div")(() => ({
  flex: "1 0 auto",
  height: 0,
}));

const ButtonStyle = styled(Button, {
  shouldForwardProp: (props) => props !== "isColor",
})<{ isColor: boolean }>(({ isColor }) => ({
  width: "60px",
  height: 32,
  borderRadius: 28,
  background: isColor
    ? " linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)"
    : "rgba(242, 242, 242, 1)",
  color: isColor ? "#fff" : "rgba(31, 31, 31, 1)",
}));

const AddButton = styled("div")(() => ({
  width: 13.33,
  height: 13.33,
  border: "1.33px solid rgba(235, 235, 235, 1)",
  borderRadius: "50%",
  display: "flex",
  justifyContent: "center",
  boxSizing: "border-box",
  lineHeight: "8px",
  color: "rgba(66, 66, 66, 1)",
  cursor: "pointer",
  ":hover": {
    borderColor: "rgba(24, 112, 199, 1)",
    color: "rgba(24, 112, 199, 1)",
  },
}));

export interface sortState {
  order: string;
  name: string;
}
interface Props {
  isDialog: boolean;
  setCheckedData: (data: any) => void;
}
const KnowledgeBase: React.FC<Props> = ({ isDialog, setCheckedData }) => {
  const navigator = useNavigate();
  const [pageInfo, setPageInfo] = useState({ limit: 12, offset: 0 });
  const [sortInfo, setSortInfo] = useState<sortState>({
    order: "desc",
    name: "update_dt",
  });
  const [total, setTotal] = useState(0);
  const [checkedList, setCheckedList] = useState<any[]>([]);
  const [knowledgeOpen, setKnowledgeOpen] = useState<boolean>(false);
  const [types, setTypes] = useState<string>("");
  const [popover, setPopover] = useState<HTMLButtonElement | null>(null);
  const [knowledgeOption, setKnowledgeOption] = useState<
    knowledgeOptionProps[]
  >([]);
  // const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(
  //   null,
  // );
  // const [delIndex, setDelIndex] = useState<number>(0);
  const [searchParam, setSearchParam] = useState<any>({});
  const [searchValues, setSearchValues] = useState<any>([]);
  // const [itemValue, setItemValue] = useState<knowledgeDataProps>();
  const [active, setActive] = useState<number>(0);
  const init = useCallback(async () => {
    try {
      const {
        data: { data, total },
      } = await getKnowledgeAll({
        limit: pageInfo.limit,
        offset: pageInfo.offset,
        order_by: sortInfo,
        conditions: searchValues,
      });
      const newData = data.map((item: any) => ({
        paper_count: item.paper_count,
        ...item.kb_info,
      }));
      setCheckedList(newData);
      setTotal(total);
      // if (!data.length) {
      // 	dispatch(changeValue({ field: "knowledge", value: 0 }));
      // }
    } catch (error: any) {
      window.console.log(error);
      message.error("获取知识库列表失败");
    }
  }, [pageInfo, sortInfo]);

  const onPageChange = (pageParams: PageProps) => {
    const { pageSize, page } = pageParams;
    const offset = (page - 1) * pageSize;
    setPageInfo({ limit: pageSize, offset });
  };
  useEffect(() => {
    init();
  }, [init]);
  const handlePopoverClose = () => {
    setPopover(null);
  };

  const { refetch } = useQuery({
    queryKey: ["getPaperBase"],
    // queryFn: queryRequest,
  });
  const onChange = (e: React.ChangeEvent<HTMLInputElement>, id: number) => {
    const updatedCheckboxes = checkedList.map((checkbox) =>
      checkbox.id === id
        ? { ...checkbox, checked: e.target.checked }
        : checkbox.id !== id && isDialog
          ? { ...checkbox, checked: false }
          : checkbox,
    );
    const newData = updatedCheckboxes.filter((item) => item.checked)[0];
    setCheckedData && setCheckedData(newData);
    setCheckedList(updatedCheckboxes);
  };
  // 批量删除按钮弹框
  const handlePopoverConfirm = () => {
    const promises = checkedList.map(async (item, index) => {
      if (item.checked) {
        const {
          data: { code },
        } = await disableKnowledge(item.id);
        // dispatch(delKbHistory(item.id));
        if (code !== 200) {
          throw new Error("删除失败");
        }
      } else if (
        index === checkedList.length - 1 &&
        !checkedList.some((item) => item.checked)
      ) {
        throw new Error("请选择要删除的知识库");
      }
    });
    Promise.all(promises)
      .then(() => {
        if (checkedList.some((item) => item.checked)) {
          // const delLength = checkedList.filter((item) => item.checked).length;
          // const offsetPage = page / pageSize + 1;
          // const totalPage = Math.ceil((total - delLength) / pageSize);
          // if (offsetPage > totalPage) {
          // 	const currentPage = offsetPage !== 1 ? (offsetPage - 2) * pageSize : 0;
          // 	setPage(currentPage);
          // } else {
          // 	init();
          // }
          message.success("删除成功");
          handlePopoverClose();
        }
      })
      .catch((error) => {
        message.warning(error.message);
      });
  };

  // 创建知识库
  const handleAdd = () => {
    setKnowledgeOpen(true);
    setTypes("add");
  };

  // from对象集合
  const onChanges = (key: string, value: any) => {
    const newSearchParam = { ...searchParam, [key]: value };
    setSearchParam(newSearchParam);
  };
  // 重置
  const handleReset = () => {
    setSearchParam({});
    setSearchValues([]);
    setPageInfo((prev) => ({ ...prev, offset: 0 }));
  };
  // 查询
  const handleQuery = () => {
    const { range_value, kb_name } = searchParam;
    const newArr = [];
    for (const key in searchParam) {
      if (key === "range_value" && range_value.length) {
        const start = range_value[0] + " 00:00:00";
        const end = range_value[1] + " 23:59:59";
        searchParam[key] &&
          newArr.push({
            name: "update_dt",
            range_value: [start, end],
            operator: "between",
          });
      } else if (key === "kb_name" && kb_name) {
        searchParam[key] &&
          newArr.push({
            name: key,
            value: kb_name,
            operator: "like",
          });
      }
    }
    setSearchValues(newArr);
    setPageInfo((prev) => ({ ...prev, offset: 0 }));
  };

  // 取消删除
  // const handleClose = () => {
  //   setAnchorEl(null);
  // };

  // // 确认删除
  // const handleConfirm = async () => {
  //   handleClose();
  //   try {
  //     const {
  //       data: { code },
  //     } = await disableKnowledge(delIndex);
  //     // dispatch(delKbHistory(delIndex));
  //     if (code === 200) {
  //       const { limit, offset } = pageInfo;
  //       const offsetPage = limit / offset + 1;
  //       const totalPage = Math.ceil((total - 1) / offset);
  //       if (offsetPage > totalPage) {
  //         const currentPage = offsetPage !== 1 ? (offsetPage - 2) * offset : 0;
  //         setPageInfo((prev) => ({ ...prev, limit: currentPage }));
  //       } else {
  //         init();
  //       }
  //       message.success("删除成功");
  //     }
  //   } catch (error) {
  //     window.console.log(error);
  //     message.error("删除失败");
  //   }
  // };
  // 点击title跳详情
  const handleTitle = (item: knowledgeDataProps) => {
    navigator("/knowledge-base/knowledge-detail", {
      state: { documentId: 962675482067968, kb_name: item.kb_name },
    });
  };

  const handleEdit = () => {
    const option = checkedList.map((item: { id: number; kb_name: string }) => ({
      label: item.kb_name,
      value: item.id,
    }));
    setKnowledgeOption(option);
    setKnowledgeOpen(true);
    // setItemValue(item);
    setTypes("edit");
  };

  const handleDelete = async (item: any) => {
    const { id } = item;
    try {
      const {
        data: { code },
      } = await disableKnowledge(id);
      // dispatch(delKbHistory(delIndex));
      if (code === 200) {
        const { limit, offset } = pageInfo;
        const offsetPage = limit / offset + 1;
        const totalPage = Math.ceil((total - 1) / offset);
        if (offsetPage > totalPage) {
          const currentPage = offsetPage !== 1 ? (offsetPage - 2) * offset : 0;
          setPageInfo((prev) => ({ ...prev, limit: currentPage }));
        } else {
          init();
        }
        return true;
      }
    } catch (error) {
      window.console.log(error);
      return false;
    }
  };
  // 发起会话
  const handleChat = (item: any) => {
    window.console.log(item);
    // navigator(`/ai-chat`);
  };

  // 切换点击tabs
  const handleClick = (index: number, key: string) => {
    window.console.log(key);
    setActive(index);
  };
  return (
    <Root>
      {!isDialog && <Breadcrumb parent={[]} current={"实验数据库"} />}
      <Main isDialog={isDialog}>
        <List isDialog={isDialog}>
          {databaseButtonGroup.map((item, index) => (
            <ListItem
              key={index}
              active={active === index}
              onClick={() => handleClick(index, item.keyword)}
              isDialog={isDialog}
            >
              {item.name}
              {!isDialog && <AddButton onClick={handleAdd}>+</AddButton>}
            </ListItem>
          ))}
        </List>
        <ContentBox>
          {/* 检索 */}
          {!isDialog && (
            <SearchStyle>
              <div
                style={{
                  width: "100%",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <Grid size={15} sx={{ display: "flex" }}>
                  <div style={{ marginRight: "32px" }}>
                    <DynamicForm
                      columns={searchColumns}
                      formData={searchParam}
                      onChange={onChanges}
                      size="small"
                    />
                  </div>
                  <SortPart
                    label="更新时间"
                    name="update_dt"
                    setSortInfo={setSortInfo}
                  />
                </Grid>
                <Grid
                  size={3}
                  sx={{ mr: 3, display: "flex", alignItems: "center" }}
                >
                  <ButtonStyle
                    sx={{ mr: 3 }}
                    onClick={handleReset}
                    isColor={false}
                  >
                    取消
                  </ButtonStyle>
                  <ButtonStyle onClick={handleQuery} isColor={true}>
                    确定
                  </ButtonStyle>
                </Grid>
              </div>
            </SearchStyle>
          )}
          <ContentDiv>
            <CardTable
              data={checkedList}
              total={total}
              pagination={{
                page: pageInfo.offset / pageInfo.limit + 1,
                pageSize: pageInfo.limit,
              }}
              onChangePage={onPageChange}
              onSelectChange={onChange}
              onClickCard={!isDialog ? handleTitle : () => {}}
              operations={
                !isDialog
                  ? {
                      edit: handleEdit,
                      delete: handleDelete,
                      chat: handleChat,
                      // transfer: "handleTransfer",
                    }
                  : undefined
              }
              type="database"
              buttonGroup={
                !isDialog && (
                  <ButtonGroup
                    data={buttonGroup}
                    checkedList={checkedList}
                    setCheckedList={setCheckedList}
                    setPopover={setPopover}
                  />
                )
              }
              columnsTypeName={{
                title: "kb_name",
                version: "kb_version",
                count: "paper_count",
                updateTime: "update_dt",
              }}
            />
          </ContentDiv>
        </ContentBox>
      </Main>
      {/* {knowledgeOpen && (
        <PopUp
          setKnowledgeOpen={setKnowledgeOpen}
          init={init}
          types={types}
          knowledgeOption={knowledgeOption}
          itemValue={itemValue}
        ></PopUp>
      )} */}
      <AddPaperBaseDialog
        type={types}
        open={knowledgeOpen}
        setOpen={setKnowledgeOpen}
        reload={refetch}
        editOption={knowledgeOption}
      />
      {/* {!!anchorEl && (
        <PopoverDelete
          title="确定删除该知识库吗?"
          anchorEl={anchorEl}
          handleClose={handleClose}
          handleConfirm={handleConfirm}
        />
      )} */}
      {!!popover && (
        <PopoverDelete
          title="确定批量删除知识库吗?"
          anchorEl={popover}
          handleClose={handlePopoverClose}
          handleConfirm={handlePopoverConfirm}
          horizontal={"right"}
        />
      )}
    </Root>
  );
};
export default KnowledgeBase;
