import React from "react";
import {
  Ava<PERSON>,
  IconButton,
  ListItemIcon,
  Menu,
  MenuItem,
  Tooltip,
  styled,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/hooks";
import Logout from "@mui/icons-material/Logout";
import PowerSettingsNewIcon from "@mui/icons-material/PowerSettingsNew";
import { getRoleInfo } from "@/utils/auth";
import { logout as logoutApi } from "@/store/user";
import { getDefaultLogo } from "@/utils/logoUtils";
const Root = styled("div")(() => ({
  width: "100%",
  display: "flex",
  justifyContent: "center",
}));

const StyleMenuItem = styled("div")(() => ({
  maxWidth: 105,
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
}));

const Footer: React.FC<{ isMinSize: boolean }> = ({ isMinSize }) => {
  const navigator = useNavigate();
  const dispatch = useAppDispatch();
  const roleInfoStorage = getRoleInfo();
  const roleInfo = roleInfoStorage;
  const userInfo = useAppSelector((state) => state.user.userInfo);
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: any) => {
    if (userInfo) {
      setAnchorEl(event.currentTarget);
    } else {
      navigator("/login");
    }
  };
  const checkHttpCode = (data: string | string[]) => {
    if (!data) return "";
    const index = data.indexOf("://");
    if (index === -1) {
      return `${window.location.origin}${data}`;
    }
    return data;
  };

  const handleMenuClick = (key: string) => {
    const url = localStorage.getItem("loginUrl");
    const checkUrl = url ? (url.endsWith("/") ? url : url + "/") : "";
    const portalSiteUrl = checkHttpCode(checkUrl);
    switch (key) {
      // case "personCenter":
      //   navigator("/person-center");
      //   break;
      case "portal-site":
        window.location.href = portalSiteUrl + "portal-site";
        break;
      case "logout": {
        dispatch(logoutApi()).then(() => {
          setAnchorEl(null);
        });
        break;
      }
    }
  };

  return (
    <Root>
      <Tooltip title="">
        <Box
          sx={{
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
          onClick={handleClick}
        >
          <IconButton
            size="small"
            aria-controls={open ? "account-menu" : undefined}
            aria-haspopup="true"
            aria-expanded={open ? "true" : undefined}
          >
            <Avatar
              sx={{ width: 32, height: 32 }}
              src={getDefaultLogo()}
            ></Avatar>
          </IconButton>

          <Typography
            component="span"
            variant="subtitle1"
            sx={{
              fontWeight: 400,
              maxWidth: isMinSize ? "0" : "auto",
              whiteSpace: "nowrap",
              overflow: "hidden",
              opacity: isMinSize ? 0 : 1,
              transition:
                "opacity .3s cubic-bezier(.645,.045,.355,1), width .3s cubic-bezier(.645,.045,.355,1)",
            }}
          >
            个人中心
          </Typography>
        </Box>
      </Tooltip>
      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={() => setAnchorEl(null)}
        onClick={() => setAnchorEl(null)}
        slotProps={{
          paper: {
            elevation: 0,
            sx: {
              overflow: "visible",
              filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
              mt: 1.5,
              ml: 1.5,
              "& .MuiAvatar-root": {
                width: 32,
                height: 32,
                ml: -0.5,
                mr: 1,
              },
              "&::before": {
                content: '""',
                display: "block",
                position: "absolute",
                bottom: 10,
                left: -5,
                width: 10,
                height: 10,
                bgcolor: "background.paper",
                transform: "translateY(50%) rotate(45deg)",
                zIndex: 0,
              },
            },
          },
        }}
        transformOrigin={{ horizontal: "left", vertical: "bottom" }}
        anchorOrigin={{ horizontal: "right", vertical: "center" }}
      >
        <MenuItem>
          <StyleMenuItem
            title={userInfo.name + `(${roleInfo?.groupName || "全部课题组"})`}
          >
            {userInfo.name + `(${roleInfo?.groupName || "全部课题组"})`}
          </StyleMenuItem>
        </MenuItem>
        {/* <MenuItem onClick={() => handleMenuClick("personCenter")}>
          <ListItemIcon>
            <PersonSharpIcon fontSize="small" />
          </ListItemIcon>
          个人中心
        </MenuItem> */}
        <MenuItem onClick={() => handleMenuClick("portal-site")}>
          <ListItemIcon>
            <Logout fontSize="small" />
          </ListItemIcon>
          返回主页
        </MenuItem>
        <MenuItem onClick={() => handleMenuClick("logout")}>
          <ListItemIcon>
            <PowerSettingsNewIcon fontSize="small" />
          </ListItemIcon>
          退出登录
        </MenuItem>
      </Menu>
    </Root>
  );
};

export default Footer;
