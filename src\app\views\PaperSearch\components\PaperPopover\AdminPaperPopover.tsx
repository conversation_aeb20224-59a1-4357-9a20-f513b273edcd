import {
  Checkbox,
  <PERSON>Item,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Popover,
} from "@mui/material";
import MuiAccordion, { AccordionProps } from "@mui/material/Accordion";
import MuiAccordionSummary, {
  AccordionSummaryProps,
} from "@mui/material/AccordionSummary";
import MuiAccordionDetails from "@mui/material/AccordionDetails";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import FolderIcon from "@mui/icons-material/Folder";
import { anyValueProps } from "@/types/common";
import { cloneDeep, debounce } from "lodash";
import { useQuery } from "@tanstack/react-query";
import { adminDocumentAdd, getAdminDocumentList } from "@/api/paperSearch";
interface AnchorOriginProps {
  vertical: "top" | "bottom";
  horizontal: "left" | "right";
}

interface PaperPopoverIProps {
  anchorEl: HTMLButtonElement | null;
  id: "simple-popover" | undefined;
  open: boolean;
  onClose: () => void;
  pdfId: string;
  anchorOrigin?: AnchorOriginProps;
  transformOrigin?: AnchorOriginProps;
}

const Root = styled("div")(() => ({
  width: 300,
  height: 400,
}));

const PopoverHeader = styled("div")(() => ({
  width: "100%",
  height: 40,
  background: "#f5f5f5",
  fontSize: 20,
  fontWeight: 600,
  lineHeight: "40px",
  paddingLeft: "10px",
  boxSizing: "border-box",
}));

const PopoverContent = styled("div")(() => ({
  width: "100%",
  height: 310,
  overflow: "auto",
  background: "#fff",
  padding: "10px 0",
  boxSizing: "border-box",
}));

const PopoverFooter = styled("div")(() => ({
  width: "100%",
  height: 50,
  background: "#f5f5f5",
  padding: "0 10px",
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
  boxSizing: "border-box",
}));

const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(() => ({
  width: "100%",
  background: "none",
  "&:not(:last-child)": {
    borderBottom: 0,
  },
  "&::before": {
    display: "none",
  },
}));

const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary
    expandIcon={<PlayArrowIcon sx={{ fontSize: "0.9rem" }} />}
    {...props}
  />
))(({ theme }) => ({
  borderRadius: "5px",
  height: "40px",
  minHeight: "25px",
  flexDirection: "row-reverse",
  background: "none",
  "& .MuiAccordionSummary-expandIconWrapper.Mui-expanded": {
    transform: "rotate(90deg)",
  },
  "& .MuiAccordionSummary-content": {
    marginLeft: theme.spacing(1),
  },
}));

const AccordionDetails = styled(MuiAccordionDetails)(() => ({
  padding: "0 16px",
  marginTop: "3px",
  borderRadius: "8px",
  boxSizing: "border-box",
}));

const StyledListItemButton = styled(ListItemButton)(() => ({
  paddingTop: 2,
  paddingBottom: 2,
  paddingLeft: 0,
}));

const StyledListItemIcon = styled(ListItemIcon)(() => ({
  minWidth: "30px",
}));

const StyledCheckbox = styled(Checkbox)(({ theme }) => ({
  padding: 0,
  margin: `${theme.spacing()} ${theme.spacing()} ${theme.spacing()} 0`,
}));

const ListItemTextInfo = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
}));
const AdminPaperPopover: React.FC<PaperPopoverIProps> = (props) => {
  const {
    id,
    open,
    onClose,
    anchorEl,
    pdfId,
    anchorOrigin = { vertical: "bottom", horizontal: "left" },
    transformOrigin,
  } = props;
  const [expanded, setExpanded] = useState<string | false>("");
  const [list, setList] = useState<anyValueProps[]>([]);
  const [disable, setDisable] = useState(false);

  const queryRequest = async () => {
    const response = await getAdminDocumentList({ pdfId });
    return response.data;
  };

  const { data, status } = useQuery({
    queryKey: ["getAdminDocumentList"],
    queryFn: queryRequest,
  });

  useEffect(() => {
    switch (status) {
      case "success": {
        setList(data.data);
        break;
      }
      case "error":
        message.error("获取资料库列表失败");
        break;
      default:
        break;
    }
  }, [data, status]);

  const handleClose = () => {
    setList([]);
    onClose();
  };

  const accordionHandleChange =
    (resourceCode: string) =>
    (_: React.SyntheticEvent, isExpanded: boolean) => {
      setExpanded(isExpanded ? resourceCode : false);
    };

  const handleCheck = (parent: string, checkItem: anyValueProps) => {
    const deepCloneData = cloneDeep(list);
    const updateExistData = deepCloneData.map((group) => {
      // 如果当前 group 的 resourceCode 匹配
      if (group.resourceCode === parent) {
        // 深拷贝 group，避免直接修改原数据
        const updatedGroup = { ...group };
        // 遍历 groupList，找到目标 id 并更新
        updatedGroup.documentList = updatedGroup.documentList.map(
          (item: any) => {
            if (item.id === checkItem.id) {
              return { ...item, exist: !item.exist }; // 更新 exist 属性
            }
            return item;
          },
        );
        return updatedGroup;
      }
      return group;
    });
    setList(updateExistData);
  };

  const filterExistGroups = (list: anyValueProps[]) => {
    const grouped = list.reduce(
      (acc, group) => {
        group.documentList.forEach((item: any) => {
          if (item.exist) {
            const resourceCode = group.resourceCode;
            const id = item.id;
            acc[resourceCode] = acc[resourceCode] || [];
            acc[resourceCode].push(id);
          }
        });
        return acc;
      },
      {} as Record<string, string[]>,
    );
    return Object.entries(grouped).map(([resourceCode, documentIds]) => ({
      resourceCode,
      documentIds,
    }));
  };

  const handleSubmit = async () => {
    const params = filterExistGroups(list);
    setDisable(true);
    const queryParams = {
      pdfId: Number(pdfId),
      resourceDocuments: params,
    };
    try {
      const {
        data: { code },
      } = await adminDocumentAdd(queryParams);
      if (code === 200) {
        message.success("添加成功");
        setDisable(false);
        handleClose();
      } else {
        message.error("添加失败");
        setDisable(false);
      }
    } catch {
      message.error("添加失败");
      setDisable(false);
    }
  };
  return (
    <Popover
      id={id}
      open={open}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={anchorOrigin}
      transformOrigin={transformOrigin}
    >
      <Root>
        <PopoverHeader>选择资料库</PopoverHeader>
        <PopoverContent>
          {list.length &&
            list.map((item) => (
              <Accordion
                key={item.resourceCode}
                expanded={expanded === item.resourceCode}
                onChange={accordionHandleChange(item.resourceCode)}
              >
                <AccordionSummary>
                  {item.resourceName}
                  <span>({item.documentList.length}个)</span>
                </AccordionSummary>
                <AccordionDetails>
                  {item.documentList.map((subItem: any) => (
                    <ListItem key={subItem.id} disablePadding>
                      <StyledListItemButton
                        onClick={() => handleCheck(item.resourceCode, subItem)}
                      >
                        <StyledListItemIcon>
                          <StyledCheckbox
                            edge="start"
                            checked={subItem.exist}
                          />
                        </StyledListItemIcon>
                        <ListItemText>
                          <ListItemTextInfo>
                            <FolderIcon
                              fontSize="small"
                              sx={{
                                mr: 1,
                                color: subItem.exist
                                  ? "rgba(0, 104, 177)"
                                  : "gray",
                              }}
                            />
                            {subItem.name}
                          </ListItemTextInfo>
                        </ListItemText>
                      </StyledListItemButton>
                    </ListItem>
                  ))}
                </AccordionDetails>
              </Accordion>
            ))}
        </PopoverContent>
        <PopoverFooter>
          <Button
            variant="outlined"
            sx={{ mr: 1, height: 33 }}
            onClick={handleClose}
          >
            取消
          </Button>
          <Button
            variant="contained"
            sx={{ height: 33 }}
            disabled={disable}
            onClick={debounce(handleSubmit, 500)}
          >
            确定
          </Button>
        </PopoverFooter>
      </Root>
    </Popover>
  );
};
export default AdminPaperPopover;
