import { downloadFile, getFile, queryImgList } from "@/api/paperSearch";
import { PaperChartPorps, CHART_TYPE_CONTRAST } from "../common";
import CloudDownloadIcon from "@mui/icons-material/CloudDownload";
import Viewer from "react-viewer";
import { anyValueProps } from "@/types/common";
import { Skeleton } from "@mui/material";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { debounce } from "lodash";
const MainBox = styled("div")(({ theme }) => ({
  marginTop: theme.spacing(3),
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
  paddingBottom: theme.spacing(1),
}));

const MainTitle = styled("div")(() => ({
  fontSize: 18,
  fontWeight: 700,
  lineHeight: "18px",
  color: "rgba(31, 31, 31, 1)",
  marginBottom: 14,
}));

const ImageBox = styled("div")(() => ({
  width: "100%",
  position: "relative",
  height: 190,
}));

const HasImageBox = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
}));

const MyViewer = styled(Viewer)(() => ({
  transition: "none",
}));

const PageButton = styled("div", {
  shouldForwardProp: (prop) => prop !== "disable",
})<{ disable: boolean }>(({ disable }) => ({
  width: 20,
  height: "calc(100% - 20px)",
  background: disable ? "rgba(222, 222, 222, 1)" : "rgba(255, 255, 255, 1)",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  cursor: disable ? "not-allowed" : "pointer",
  border: "1px solid rgba(222, 222, 222, 1)",
  boxSizing: "border-box",
  borderRadius: "10px",
  margin: "5px 0",
  color: disable ? "rgba(255, 255, 255, 0.8)" : "rgba(0,0,0,0.8)",
}));

const PictureBox = styled("div")(() => ({
  width: "calc(100% - 40px)",
  height: "100%",
  display: "grid",
  gridTemplateColumns: "repeat(5, 1fr)", // 五列等宽布局
  gap: 1, // 网格间距10px
  alignItems: "center",
}));

const ImgItem = styled("div")(() => ({
  height: "168px",
  position: "relative",
  borderRadius: "10px",
  border: "1px solid rgba(222, 222, 222, 1)",
  cursor: "pointer",
  "& img": {
    borderRadius: "10px",
  },
  marginLeft: 3,
  "&:hover": {
    ".download": {
      // top: 0,
      zIndex: 2,
      opacity: 1,
    },
    "::before": {
      content: "''",
      position: "absolute",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      background: "rgba(255, 255, 255, 0.76)",
      zIndex: 1,
      borderRadius: "10px",
      pointerEvents: "none",
    },
  },
}));

const NodataDiv = styled("div")(() => ({
  color: "#67676A",
  position: "absolute",
  left: "50%",
  top: "50%",
  transform: "translate(-50%,-50%)",
}));

const ButtonArea = styled(Button)(() => ({
  width: 90,
  height: 32,
  borderRadius: "16px",
  background:
    "linear-gradient(90deg, rgba(0, 48, 122, 1) 0%, rgba(0, 104, 177, 1) 100%)",
  fontSize: 14,
  color: "rgba(255, 255, 255, 1)",
  position: "absolute",
  top: "Calc(50% - 12.5px)",
  left: "calc(50% - 50px)",
  opacity: 0,
  zIndex: 100,
}));

const DownloadButton = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const MainFooter = styled("div")(() => ({
  width: "100%",
  height: 30,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const PaperChart: React.FC<PaperChartPorps> = ({ type, imgCount, pdfId }) => {
  const pageSize = 5;
  const [imgaeList, setImageList] = useState<any[]>([]);
  const [visible, setVisible] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [page, setPage] = useState(1);
  const [totalPage, setTotalPage] = useState(Math.ceil(imgCount / pageSize));

  const getImgArray = async () => {
    if (!pdfId || !imgCount) return;
    setTotalPage(Math.ceil(imgCount / pageSize));
    const { data } = await queryImgList({
      pdfId,
      page,
      size: pageSize,
      type,
    });
    if (data.code === 200) {
      getImageFile(data.data);
    }
  };

  const fetchImageById = async (bodyImageUrl: string) => {
    try {
      const { data } = await getFile(bodyImageUrl + `&pdfId=${pdfId}`);
      return data;
    } catch (error) {
      message.error("获取图片失败" + error);
      return null;
    }
  };

  const getImageFile = async (img: anyValueProps) => {
    setImageList(new Array(img.length).fill(null));
    img.forEach(async (item: any, index: any) => {
      const res = await fetchImageById(item.bodyImageUrl);
      if (res) {
        setImageList((prev) => {
          const updatedList = [...prev];
          updatedList[index] = {
            ...item,
            alt: item.title,
            src: URL.createObjectURL(res),
            download: URL.createObjectURL(res),
          };
          return updatedList;
        });
      }
    });
  };

  const extractString = (input: string, type: string) => {
    const xlsxRegex = /\/([^/]+)\.xlsx/;
    const pngRegex = /\/([^/]+)\.png/;
    const match = input.match(type === "xlsx" ? xlsxRegex : pngRegex);

    if (match && match[1]) {
      return match[1];
    } else {
      return null; // 如果没有匹配到，返回 null
    }
  };

  const convertExcel = async (item: anyValueProps) => {
    const { bodyExcelUrl, bodyExcelPath } = item;
    const name = extractString(bodyExcelPath, "xlsx") || "table";
    const { data } = await downloadFile(bodyExcelUrl + `&pdfId=${pdfId}`);
    const url = window.URL.createObjectURL(new Blob([data]));
    const a = document.createElement("a");
    a.href = url;
    a.download = `${name}.xlsx`; // 设置文件名
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const downloadImg = async (item: anyValueProps) => {
    try {
      const { bodyImageUrl, bodyImagePath } = item;
      const name = extractString(bodyImagePath, "png") || "image";
      const { data } = await downloadFile(bodyImageUrl + `&pdfId=${pdfId}`);
      const url = window.URL.createObjectURL(new Blob([data]));
      const a = document.createElement("a");
      a.href = url;
      a.download = `${name}.png`; // 设置文件名
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.log(error);
    }
  };

  const handleDownLoad = async (item: anyValueProps, type: number) => {
    try {
      if (type === 1) {
        convertExcel(item);
      } else {
        downloadImg(item);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleImgClick = (index: number, type: number) => {
    const imgDom = document.getElementsByClassName(`viewer-${type}`);
    if (imgDom.length !== 0) {
      const imgElements = imgDom[0] as HTMLElement;
      imgElements.style.display = "block";
      imgElements.style.opacity = "1";
    }
    setVisible(true);
    setActiveIndex(index);
  };

  const handleImgClose = (type: number) => {
    const imgElements: any = document.getElementsByClassName(`viewer-${type}`);
    setVisible(false);
    Array.from(imgElements).forEach((el: any) => {
      (el as HTMLElement).style.display = "none";
    });
  };

  const handlePageButtonClick = (type: "prev" | "next") => {
    switch (type) {
      case "prev":
        if (page > 1) {
          setPage(page - 1);
        }
        break;
      case "next":
        if (page < totalPage) {
          setPage(page + 1);
        }
        break;
    }
  };

  useEffect(() => {
    getImgArray();
  }, [imgCount, page, pdfId]);

  return (
    <MainBox>
      <MainTitle>{CHART_TYPE_CONTRAST[type] + `(${imgCount})`}</MainTitle>
      <ImageBox>
        {imgCount ? (
          <HasImageBox>
            {imgCount > pageSize && (
              <PageButton
                disable={page === 1}
                onClick={debounce(() => handlePageButtonClick("prev"), 500)}
              >
                <ArrowBackIosNewIcon />
              </PageButton>
            )}
            <PictureBox>
              {imgaeList.map((item, index) =>
                item ? (
                  <div key={index} style={{ marginRight: "10px" }}>
                    <ImgItem onClick={() => handleImgClick(index, type)}>
                      <img
                        src={item.src}
                        height={168}
                        alt={item.title}
                        loading="lazy"
                        title={item.title}
                        style={{ width: "100%" }}
                      />
                      {type === 1 && (
                        <ButtonArea
                          className="download"
                          variant="contained"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownLoad(item, type);
                          }}
                        >
                          转Excel
                        </ButtonArea>
                      )}
                    </ImgItem>
                  </div>
                ) : (
                  <Skeleton
                    key={index}
                    variant="rectangular"
                    width={300}
                    height={168}
                    sx={{ mr: 1 }}
                  />
                ),
              )}
            </PictureBox>
            {imgCount > pageSize && (
              <PageButton
                disable={page === totalPage}
                onClick={debounce(() => handlePageButtonClick("next"), 500)}
              >
                <ArrowForwardIosIcon />
              </PageButton>
            )}
          </HasImageBox>
        ) : (
          <NodataDiv>暂无数据</NodataDiv>
        )}
      </ImageBox>
      {imgCount > pageSize && <MainFooter>{page + "/" + totalPage}</MainFooter>}
      {imgaeList.every((item) => item !== null) && (
        <MyViewer
          className={"viewer-" + type}
          visible={visible}
          onClose={() => handleImgClose(type)}
          images={imgaeList}
          onMaskClick={() => handleImgClose(type)}
          showTotal={false}
          activeIndex={activeIndex}
          customToolbar={(toolbars) =>
            type === 0
              ? toolbars.concat([
                  {
                    key: "mydownload",
                    render: (
                      <DownloadButton>
                        <CloudDownloadIcon fontSize="small" />
                      </DownloadButton>
                    ),
                    onClick: (activeImage) => {
                      handleDownLoad(activeImage, type);
                    },
                  },
                ])
              : toolbars
          }
        ></MyViewer>
      )}
    </MainBox>
  );
};

export default memo(PaperChart);
