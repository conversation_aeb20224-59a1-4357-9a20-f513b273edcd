// 持久化store
import { configureStore, combineReducers } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import counterReducer from "./counterSlice";
import breadcrumbReducer from "./breadcrumbSlice";
import user from "./user";
import route from "./route";
import search from "./search";
// ...

export const rootReducer = combineReducers({
  counter: counterReducer,
  user,
  route,
  search,
  breadcrumb: breadcrumbReducer,
});
const persistConfig = {
  key: "chat",
  storage,
  blacklist: ["user", "route"],
};
const myPersistReducer = persistReducer(persistConfig, rootReducer);
export const store = configureStore({
  reducer: myPersistReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);
