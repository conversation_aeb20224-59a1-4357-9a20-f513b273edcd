import { SelectType, steps } from "../setting";
import StepLine from "./StepLine";
import Step1Card from "./Step1Card";
import Step2Card from "./Step2Card";
import SuccessSvg from "@/assets/reset-success.svg";
import { useNavigate } from "react-router-dom";
import { anyValueProps } from "@/types/common";

interface Props {
  setAction: (value: SelectType) => void;
}

const Root = styled("div")(() => ({
  width: 560,
  background:
    "radial-gradient(98.39% 69.47% at 17.142857142857142% -40.13722126929674%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(255, 255, 255, 1)",
  borderRadius: 20,
  paddingBottom: 30,
}));

const HeaderBar = styled("div")(() => ({
  width: "100%",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  padding: "39px 30px 0 30px",
  boxSizing: "border-box",
}));

const BackLogin = styled("div")(() => ({
  cursor: "pointer",
  fontSize: 14,
  color: "#4248B5",
}));

const HeaderTitle = styled("div")(() => ({
  fontWeight: 700,
  fontSize: 18,
  color: "rgba(64, 64, 64, 1)",
}));

const StepBar = styled("div")(() => ({
  width: "100%",
  height: 100,
  padding: "40px 0",
  boxSizing: "border-box",
  marginBottom: 20,
}));

const OperationBar = styled("div")(() => ({
  width: "100%",
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  boxSizing: "border-box",
}));

const ResetSuccessBox = styled("div")(() => ({
  width: "100%",
  height: " 100%",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  paddingTop: 50,
}));

const SuccessIcon = styled("div")(() => ({
  width: 200,
  height: 200,
  backgroundImage: `url(${SuccessSvg})`,
  backgroundRepeat: "no-repeat",
  backgroundSize: "cover",
}));

const SuccessMsg = styled("div")(() => ({
  fontWeight: 500,
  fontSize: 18,
  color: "#666",
  marginTop: 10,
}));

const BackMsg = styled("div")(() => ({
  fontWeight: 500,
  fontSize: 14,
  color: "#666",
  marginTop: 5,
  marginBottom: 10,
}));

const BackButton = styled(Button)(() => ({
  width: 300,
}));
const BackBox = styled("div")(() => ({
  width: "100%",
  display: "flex",
  justifyContent: "center",
  marginTop: 19,
}));

const ForgetPasswordBox: React.FC<Props> = ({ setAction }) => {
  const navigator = useNavigate();
  const [step, setStep] = useState(0);
  const [countdown, setCountdown] = useState(6);
  const [newPasswordParams, setNewPasswordParams] = useState<anyValueProps>({});

  useEffect(() => {
    if (step === 2 && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (step === 2 && countdown === 0) {
      navigator("/");
    }
  }, [step, countdown]);

  return (
    <Root>
      <HeaderBar>
        <HeaderTitle>重置密码</HeaderTitle>
      </HeaderBar>
      <OperationBar>
        {step !== 2 && (
          <StepBar>
            <StepLine stepOptions={steps} currentStep={step} />
          </StepBar>
        )}
        <Step1Card
          setStep={setStep}
          rowData={newPasswordParams}
          setStepData={setNewPasswordParams}
          step={step}
        />
        <Step2Card setStep={setStep} rowData={newPasswordParams} step={step} />
        {step === 2 && (
          <ResetSuccessBox>
            <SuccessIcon />
            <SuccessMsg>密码重置成功</SuccessMsg>
            <BackMsg>{countdown}秒后自动跳转至首页</BackMsg>
            <BackButton variant="contained" onClick={() => setAction("login")}>
              登录账号
            </BackButton>
          </ResetSuccessBox>
        )}
      </OperationBar>
      {step === 0 && (
        <BackBox>
          <BackLogin onClick={() => setAction("login")}>返回账号登录</BackLogin>
        </BackBox>
      )}
    </Root>
  );
};
export default ForgetPasswordBox;
