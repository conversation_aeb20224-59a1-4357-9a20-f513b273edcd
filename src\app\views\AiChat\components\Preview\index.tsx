import { CircularProgress } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useAppDispatch } from "@/hooks";
import { setPreviewItem } from "@/store/counterSlice";
import PDFVirtualList from "@/components/PdfVirtualList";
interface Props {
  pdfUrl: string;
  setPreviewOpen: (value: boolean) => void;
  previewTitle: string;
}

const Root = styled("div")(() => ({
  height: "100%",
  background:
    "radial-gradient(60.56% 59.81% at 17.077464788732392% -40.0709219858156%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  opacity: 1,
  border: "2px solid rgba(255, 255, 255, 1)",
  borderRadius: "20px",
  padding: "16px",
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
}));

const LoadingBox = styled("div")(() => ({
  width: "100%",
  height: "100%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
}));

const HederDiv = styled("div")(() => ({
  width: "100%",
  display: "flex",
  height: "30px",
  justifyContent: "space-between",
  alignItems: "center",
  background: "rgba(0,0,0,0.2)",
  padding: "0 16px",
  boxSizing: "border-box",
  marginBottom: "10px",
}));

const TitleDiv = styled("div")(() => ({
  width: "70%",
  overflow: "hidden",
  whiteSpace: "nowrap",
  textOverflow: "ellipsis",
}));
const ContentDiv = styled("div")(() => ({
  width: "100%",
  height: "calc(100% - 40px)",
}));
const IconDiv = styled("div")(() => ({
  cursor: "pointer",
}));
const Index: React.FC<Props> = ({ pdfUrl, setPreviewOpen, previewTitle }) => {
  const [height, setHeight] = useState<number>(0);
  // const [width, setWidth] = useState<number>(0);
  const dispatch = useAppDispatch();
  const contentRef = useRef<any>(null);
  const getHight = (value: number) => {
    setHeight(value);
  };

  // const getWidth = (value: number) => {
  //   setWidth(value);
  // };

  const getStyle = () => {
    if (contentRef.current) {
      getHight(contentRef.current.offsetHeight);
      // getWidth(contentRef.current.offsetWidth);
      window.console.log("width", contentRef.current.offsetWidth);
      window.console.log("height", height);
    }
  };
  useEffect(() => {
    getStyle();
    window.addEventListener("resize", getStyle);
    return () => {
      window.removeEventListener("resize", getStyle);
    };
  }, []);

  const closePreview = () => {
    setPreviewOpen(false);
    dispatch(setPreviewItem({}));
  };
  return (
    <Root>
      <HederDiv>
        <TitleDiv
          title={
            previewTitle &&
            previewTitle.replace(/\|\|\|/g, " ").replace(/#/g, " ")
          }
        >
          {previewTitle
            ? previewTitle.replace(/\|\|\|/g, " ").replace(/#/g, " ")
            : "暂无标题"}
        </TitleDiv>
        <IconDiv onClick={closePreview}>
          <CloseIcon style={{ fontSize: "13px" }} />
        </IconDiv>
      </HederDiv>
      <ContentDiv ref={contentRef}>
        {pdfUrl && <PDFVirtualList url={pdfUrl} />}
        {!pdfUrl && (
          <LoadingBox>
            <CircularProgress color="inherit" />
            <Typography variant="h4" sx={{ ml: 2 }}>
              PDF加载中...
            </Typography>
          </LoadingBox>
        )}
      </ContentDiv>
    </Root>
  );
};
export default Index;
