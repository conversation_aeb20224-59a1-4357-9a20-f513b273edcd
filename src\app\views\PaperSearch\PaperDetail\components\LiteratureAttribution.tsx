const Root = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const Content = styled("div")(() => ({
  color: "rgba(64, 64, 64, 1)",
  fontSize: "14px",
  cursor: "pointer",
  ":hover": {
    color: "rgba(0, 48, 122, 1)",
  },
}));

const LiteratureAttribution: React.FC = () => (
  <Root>
    <Content>资料库</Content>-<Content>文献库</Content>
  </Root>
);
export default LiteratureAttribution;
