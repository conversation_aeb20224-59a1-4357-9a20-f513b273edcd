export interface FormattedAnnotation {
  id: string;
  text: string;
  x: [number, number];
  y: [number, number];
  page: number;
  originalWidth?: number;
  originalHeight?: number;
}

export interface RawAnnotation {
  id: number;
  head: string;
  paragraph: string;
  coords: string[];
  frameCoords: {
    page: number;
    pageSize: {
      widthPt: number;
      heightPt: number;
    };
    upLeft: number[];
    lowRight: number[];
    upLeftScale: number[];
    lowRightScale: number[];
  }[];
  paragraphId: number;
  type: string;
}

export interface Operation {
  type: string;
  label: string;
  disable?: boolean;
}

export interface PDFVirtualListProps {
  url: string;
  initialVisiblePages?: number;
  coordsData?: RawAnnotation[];
  operation?: Operation[];
  getParagraph?: (paragraph: string, type: string) => void;
}
