import { anyValueProps } from "../types/common";

/**
 * API地址
 */

const apiBaseUrl =
  process.env.NODE_ENV === "development" ? "http://172.16.10.83:8089" : "/";
/**
 * 登录等的访问 API地址
 */
const logRegApiBaseUrl = process.env.NODE_ENV === "development" ? "/" : "/";

/**
 * 登录等的访问 API地址
 */
const ExcelApiBaseUrl =
  process.env.NODE_ENV === "development" ? "http://172.16.10.88:8088" : "/";

/**
 * API地址
 */
const ChatApiBaseUrl =
  process.env.NODE_ENV === "development"
    ? "http://172.16.10.88:8030"
    : "/server";

const PaperSearchApiBaseUrl =
  process.env.NODE_ENV === "development" ? "http://172.16.10.83:9066" : "/";

const DownloadBaseUrl =
  process.env.NODE_ENV === "development"
    ? "http://172.16.10.89:9066/engine"
    : "/engine";
/**
 * 最长请求超时时间
 */
const maxTimeout = 120 * 1000;

const maxStorageTime = 60 * 60 * 1000;

interface responseProps {
  code: number;
  msg: string;
  data: anyValueProps | anyValueProps[];
  total?: number;
}

export {
  apiBaseUrl,
  logRegApiBaseUrl,
  maxTimeout,
  maxStorageTime,
  ExcelApiBaseUrl,
  ChatApiBaseUrl,
  PaperSearchApiBaseUrl,
  DownloadBaseUrl,
};

export type { responseProps };
