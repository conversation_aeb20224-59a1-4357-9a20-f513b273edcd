import React, { useRef, useEffect, useMemo } from "react";
import { Document } from "react-pdf";
import "react-pdf/dist/Page/TextLayer.css";
import "react-pdf/dist/Page/AnnotationLayer.css";

import PdfStyles from "./components/PdfStyles";
import OperationBar from "./components/OperationBar";
import PdfPageRenderer from "./components/PdfPageRenderer";
import { PdfError } from "./components/PdfLoaders";
import { usePdfVirtualList } from "./hooks/usePdfVirtualList";
import { PDFVirtualListProps } from "./types/types";

const options = {
  cMapUrl: `/aihub-chat/cmaps/`,
};
const PDFVirtualList: React.FC<PDFVirtualListProps> = ({
  url,
  initialVisiblePages = 3,
  coordsData,
  operation,
  getParagraph,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const file = useMemo(() => ({ url }), [url]);
  // 格式化标注数据

  const {
    numPages,
    pageLayouts,
    visibleRange,
    currentPage,
    scale,
    containerWidth,
    isDocumentLoaded,
    updatePageHeight,
    updatePageDimensions,
    handlePageChange,
    handleDocumentLoadSuccess,
    handleDocumentLoadError,
    setScale,
    calculateTotalHeight,
    formattedAnnotations,
    forceUpdatePageGaps,
  } = usePdfVirtualList({
    initialVisiblePages,
    containerRef,
    coordsData,
  });

  const totalHeight = calculateTotalHeight();

  // 监听缩放变化，强制更新页面间距
  useEffect(() => {
    if (isDocumentLoaded && scale > 0) {
      // 延迟一下，等待页面渲染完成
      const timeoutId = setTimeout(() => {
        forceUpdatePageGaps();
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [scale, isDocumentLoaded, forceUpdatePageGaps]);

  return (
    <div
      style={{
        position: "relative",
        width: "100%",
        height: "100%",
        backgroundColor: "#fff",
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <PdfStyles />
      <OperationBar
        currentPage={currentPage}
        totalPages={numPages}
        scale={scale}
        onPageChange={handlePageChange}
        onScaleChange={setScale}
      />
      <div
        style={{
          flex: 1,
          position: "relative",
          display: "flex",
          justifyContent: "center",
          overflow: "hidden",
        }}
      >
        <div
          ref={containerRef}
          id="pdf-virtual-list-container"
          style={{
            width: "100%",
            height: "100%",
            overflow: "auto",
            padding: "0",
            boxSizing: "border-box",
            backgroundColor: "#fff",
            WebkitOverflowScrolling: "touch",
            touchAction: "auto",
            userSelect: "none",
          }}
        >
          <Document
            key={url}
            file={file}
            onLoadSuccess={handleDocumentLoadSuccess}
            onLoadError={handleDocumentLoadError}
            loading=""
            error={<PdfError />}
            externalLinkTarget="_blank"
            options={options}
          >
            {isDocumentLoaded && numPages > 0 ? (
              <div
                style={{
                  minHeight: "100%",
                  position: "relative",
                  height: totalHeight,
                  width: scale > 1 ? `${containerWidth * scale}px` : "100%",
                  maxWidth: scale > 1 ? `${containerWidth * scale}px` : "100%",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                }}
              >
                <PdfPageRenderer
                  visibleRange={visibleRange}
                  numPages={numPages}
                  pageLayouts={pageLayouts}
                  containerWidth={containerWidth}
                  scale={scale}
                  updatePageHeight={updatePageHeight}
                  updatePageDimensions={updatePageDimensions}
                  annotations={formattedAnnotations}
                  operation={operation}
                  getParagraph={getParagraph}
                />
              </div>
            ) : null}
          </Document>
        </div>
      </div>
    </div>
  );
};

export default PDFVirtualList;
