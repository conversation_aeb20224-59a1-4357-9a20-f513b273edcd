import * as React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TableSortLabel,
  Box,
  styled,
} from "@mui/material";
import { TableVirtuoso, TableComponents } from "react-virtuoso";
import { anyValueProps } from "@/types/common";
import { visuallyHidden } from "@mui/utils";
import { TableCheckbox } from "./setting";
import { useRef, useState, useEffect } from "react";

const StyledTableCell = styled(TableCell, {
  shouldForwardProp: (prop) => prop !== "width" && prop !== "ellipsis",
})<{
  width?: number;
  ellipsis?: boolean;
}>(({ width, ellipsis }) => ({
  width: width || "auto",
  maxWidth: width || "auto",
  height: 42, // 单元格高度--不包括padding
  ...(ellipsis && {
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
  }),
}));

// const TableHeadStyle = styled(TableHead)(() => ({
//   position: "sticky",
//   top: 0,
//   zIndex: 1,
//   "tr": {
//     display: "table",
//     width: "100%",
//     tableLayout: "fixed",

//   },
// }));

// const TableBodyStyle = styled(TableBody)(() => ({
//   height: "calc(100% - 48px)",
//   overflow: "scroll",
//   display:'block',
//   WebkitOverflowScrolling: "touch",
//   "tr": {
//     display: "table",
//     width: "100%",
//     height:"56px",
//     tableLayout: "fixed",
//     'td':{
//       // boxSizing:"border-box",
//       padding:"6px 16px"
//     }
//   },
//   "::-webkit-scrollbar":{
//     display: "none",
//   }
// }));

interface VirtualizedTableProps {
  rows: readonly anyValueProps[];
  columns: readonly ColumnProps[];
  rowKey?: string | number;
  selection?: boolean;
  onSortChange?: (sort: string, asc: boolean) => void;
  setSelectList?: (value: any) => void;
  page?: number;
}

export interface ColumnProps {
  dataKey: string;
  label: string;
  sortable?: boolean;
  width?: number;
  ellipsis?: boolean;
  disablePadding?: boolean;
  align?: "left" | "right" | "center";
  render?: (row: anyValueProps) => React.ReactNode;
}
type OrderType = "asc" | "desc";

const VirtuosoTableComponents: TableComponents<anyValueProps> = {
  Scroller: React.forwardRef<HTMLDivElement>((props, ref) => (
    <TableContainer
      component={Paper}
      {...props}
      ref={ref}
      sx={{
        height: "100%",
        width: "100%",
        position: "relative",
      }}
    />
  )),
  Table: (props) => (
    <Table
      {...props}
      size={"small"}
      sx={{ borderCollapse: "separate", tableLayout: "fixed" }}
    />
  ),
  TableHead: React.forwardRef<HTMLTableSectionElement>((props, ref) => (
    <TableHead {...props} ref={ref} />
  )),
  TableRow,
  TableBody: React.forwardRef<HTMLTableSectionElement>((props, ref) => (
    <TableBody {...props} ref={ref} />
  )),
};

const VirtualizedTable: React.FC<VirtualizedTableProps> = ({
  columns,
  rows,
  rowKey = "id",
  selection = false,
  onSortChange,
  setSelectList,
  page,
}) => {
  const scrollerRef = useRef<HTMLElement | null>(null);

  const [order, setOrder] = useState<OrderType>("desc"); // asc--正序,desc--倒序
  const [sort, setSort] = useState(""); // 排序字段

  useEffect(() => {
    if (scrollerRef.current) {
      scrollerRef.current.scrollTop = 0;
    }
    // setSelectList && setSelectList([]); // 清空选中
  }, [page]);

  const createSortHandler = (property: string) => () => {
    if (sort === "") {
      // 第一次点击时，反转默认状态
      setOrder("asc"); // 默认是desc，所以第一次点击设为asc
      setSort(property);
      onSortChange && onSortChange(property, true);
    } else {
      // 已有排序时，正常切换
      const isAsc = sort === property && order === "desc";
      setOrder(isAsc ? "asc" : "desc");
      setSort(property);
      onSortChange && onSortChange(property, isAsc);
    }
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!setSelectList) return;
    const newChecked = event.target.checked;
    const newSelected = rows.map((row) => ({
      ...row,
      checked: newChecked,
    }));
    setSelectList(newSelected);
  };

  function setCheckedTrue(dataArray: any, id: number) {
    return dataArray.map((obj: any) => {
      if (obj.id === id) {
        return { ...obj, checked: !obj.checked };
      }
      // 否则返回原对象
      return obj;
    });
  }

  const handleSelected = (_: React.MouseEvent<unknown>, id: number) => {
    if (!rows) return;
    const newSelected = setCheckedTrue(rows, id);
    setSelectList && setSelectList(newSelected);
  };

  function fixedHeaderContent() {
    return (
      <TableRow>
        {selection && (
          <TableCell
            padding="checkbox"
            sx={{
              backgroundColor: "background.paper",
              border: "none",
            }}
          >
            <TableCheckbox
              color="primary"
              indeterminate={
                rows &&
                rows.length > 0 &&
                rows.some((row) => row.checked) &&
                rows.some((row) => !row.checked)
              }
              checked={
                rows && rows.length > 0 && rows.every((row) => row.checked)
              }
              onChange={handleSelectAllClick}
              inputProps={{
                "aria-label": "select all desserts",
              }}
            />
          </TableCell>
        )}
        {columns.map((column: ColumnProps) => (
          <TableCell
            key={column.dataKey}
            align={column.align || "left"}
            padding={column.disablePadding ? "none" : "normal"}
            sortDirection={sort === column.dataKey ? order : false}
            sx={{
              width: column.width || 60,
              boxSizing: "border-box",
              backgroundColor: "background.paper",
              textWrap: "nowrap",
              border: "none",
              color: "rgba(64, 64, 64, 1)",
              fontSize: 16,
              fontWeight: 700,
            }}
          >
            {column.sortable ? (
              <TableSortLabel
                active={sort === column.dataKey}
                direction={sort === column.dataKey ? order : "desc"}
                onClick={createSortHandler(column.dataKey)}
                sx={{
                  "& .MuiTableSortLabel-icon": {
                    opacity: 1, // 让箭头图标一直显示，不透明
                  },
                  "&:hover .MuiTableSortLabel-icon": {
                    opacity: 1,
                  },
                  "&.Mui-active .MuiTableSortLabel-icon": {
                    opacity: 1,
                  },
                }}
              >
                {column.label}
                {/* 根据orderBy展示排序icon */}
                {sort === column.dataKey ? (
                  <Box component="span" sx={visuallyHidden}>
                    {order === "desc"
                      ? "sorted descending"
                      : "sorted ascending"}
                  </Box>
                ) : null}
              </TableSortLabel>
            ) : (
              column.label
            )}
          </TableCell>
        ))}
      </TableRow>
    );
  }

  function rowContent(_index: number, row: anyValueProps) {
    const labelId = `enhanced-table-checkbox-${_index}`;

    return (
      <React.Fragment>
        {selection && (
          <TableCell
            padding="checkbox"
            style={{
              background: _index % 2 !== 0 ? "rgba(247, 247, 247, 1)" : "#fff",
              border: "none",
            }}
          >
            <TableCheckbox
              color="primary"
              checked={row.checked}
              inputProps={{
                "aria-labelledby": labelId,
              }}
              disabled={row.disabled}
              onClick={(event) => handleSelected(event, row[rowKey])}
            />
          </TableCell>
        )}
        {columns.map((column: ColumnProps, cellIndex: number) => (
          <StyledTableCell
            key={cellIndex}
            align={column.align || "left"}
            component="td"
            scope="row"
            padding={column.disablePadding ? "none" : "normal"}
            width={column.width}
            ellipsis={column.ellipsis}
            title={!column.render ? row[column.dataKey] : ""}
            style={{
              background: _index % 2 !== 0 ? "rgba(247, 247, 247, 1)" : "#fff",
              border: "none",
            }}
          >
            {column.render ? column.render(row) : row[column.dataKey]}
          </StyledTableCell>
        ))}
      </React.Fragment>
    );
  }
  return (
    <Box
      sx={{
        height: "100%",
        width: "100%",
        position: "relative",
      }}
    >
      <TableVirtuoso
        data={rows}
        components={VirtuosoTableComponents}
        fixedHeaderContent={fixedHeaderContent}
        itemContent={rowContent}
        scrollerRef={(ref) => (scrollerRef.current = ref as HTMLElement)}
        style={{
          // background: "transparent",
          boxSizing: "border-box",
          borderRadius: "unset",
          padding: 0,
          boxShadow: "none",
          width: "100%",
          // overflow: "visible",
          // background:
          //   "radial-gradient(46.19% 59.91% at 17.11229946524064% -40.09433962264151%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
        }}
      />
    </Box>
  );
};

export default VirtualizedTable;
