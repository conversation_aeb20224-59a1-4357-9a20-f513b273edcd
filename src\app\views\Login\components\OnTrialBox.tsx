import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { BpCheckbox, OnTrialColumns, SelectType, emailRegex } from "../setting";
import {
  FormControl,
  FormHelperText,
  FormLabel,
  InputAdornment,
  OutlinedInput,
} from "@mui/material";
import AgreementDialog from "./Agreement/AgreementDialog";
import { getVerifyCode, register } from "@/api/login";

interface Props {
  setAction: (value: SelectType) => void;
}

const Root = styled("div")(() => ({
  width: 560,
  // height: 818,
  background:
    "radial-gradient(137.68% 69.32% at 17.142857142857142% -40.09779951100245%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(255, 255, 255, 1)",
  borderRadius: 20,
  border: "2px solid rgba(255, 255, 255, 1)",
  // position: "absolute",
  // top: "100px",
  // left: "calc(50% - 225px)",
  boxSizing: "border-box",
  padding: "0 60px 30px 60px",
}));

const TitleBox = styled("div")(() => ({
  width: "100%",
  // height: 50,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: 18,
  boxSizing: "border-box",
  marginBottom: 15,
  marginTop: 39,
}));

const TitleLabel = styled("div")(() => ({
  fontSize: 18,
  fontWeight: 700,
  color: "rgba(64, 64, 64, 1)",
}));

const FormBox = styled("div")(() => ({
  width: "100%",
  marginBottom: 20,
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
  paddingBottom: "31px",
}));

const SubmitBox = styled("div")(() => ({
  width: "100%",
}));

const ActionBar = styled("div")(() => ({
  width: "100%",
  fontSize: "14px",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: 11,
}));

const KeepLogged = styled("div")(() => ({
  fontSize: 14,

  display: "flex",
}));

const Agreement = styled("div")(() => ({
  cursor: "pointer",
  color: "rgba(125, 125, 125, 1)",
  fontSize: 14,
  fontWeight: 400,
}));

const LoginSide = styled("div")(() => ({
  width: "100%",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
}));

const BackLogin = styled("div")(() => ({
  cursor: "pointer",
  fontSize: 14,
  fontWeight: 400,
  color: "rgba(24, 112, 199, 1)",
  marginTop: 19,
}));

// const VerifyLabel = styled("div")(() => ({
//   display: "flex",
//   alignItems: "center",
// }));

const VerifyInput = styled("div")(() => ({
  width: "100%",
  borderRadius: 28,
}));

const OnTrialBox: React.FC<Props> = ({ setAction }) => {
  const formRef = useRef<RefProps>(null);
  const [already, setAlready] = useState<boolean>(true);
  const [errorText, setErrorText] = useState<string>("");
  const [verifyCode, setVerifyCode] = useState<string>("");
  const [checkVerifyCode, setCheckVerifyCode] = useState<boolean>(false);
  const [countdown, setCountdown] = useState(0);
  const [buttonText, setButtonText] = useState<string>("获取验证码");
  const [open, setOpen] = useState<boolean>(false);
  const [serialNum, setSerialNum] = useState<number>(0);

  const handleReadAlready = (event: React.ChangeEvent<any>) => {
    setAlready(event.target.checked);
  };

  const handleCodeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (!value) {
      setVerifyCode("");
      setErrorText("验证码不可为空");
      return;
    }
    setErrorText("");
    setVerifyCode(value);
  };

  const handleGetCode = async () => {
    const formRefValue: any = formRef.current?.values;
    const email = formRefValue?.email;
    if (!email) {
      setErrorText("请先输入邮箱号");
      return;
    }
    if (!emailRegex.test(email)) {
      setErrorText("请输入有效的邮箱号");
      return;
    }
    const params = {
      email,
      mustExist: false,
      mustUnique: true,
    };
    setErrorText("");
    try {
      const {
        data: { code, result },
      } = await getVerifyCode(params);
      if (code !== 200) {
        setErrorText("验证码发送失败");
        return;
      }
      setSerialNum(result.seq);
      setCheckVerifyCode(true);
      setCountdown(60);
    } catch (error: any) {
      // setErrorText(error.response.data.msg || "验证码发送失败");
      console.error(error.response.data.msg);
    }
  };

  const viewAgreement = () => {
    setOpen(true);
  };

  const handleSubmit = () => {
    if (!verifyCode) {
      setErrorText("请输入邮箱验证码");
    } else {
      setErrorText("");
    }
    formRef.current?.submit().then(async (res: any) => {
      const { email, phone, company, password } = res;
      const params = {
        email,
        phone,
        password,
        affiliations: [{ organization: company }],
        code: verifyCode,
        seq: serialNum,
        name: email,
      };
      if (!already) {
        message.error("请先阅读并同意隐私协议和服务条款");
        setOpen(true);
        return;
      }
      const {
        data: { code },
      } = await register(params);
      if (code !== 200) {
        message.error("注册失败");
        return;
      }
      message.success("注册成功");
      setAction("login");
    });
  };

  const handleCodeBlur = () => {
    if (!verifyCode) {
      setErrorText("请输入邮箱验证码");
    }
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown((prev) => prev - 1), 1000);
    } else if (countdown === 0 && checkVerifyCode) {
      setButtonText("重新获取验证码");
    }
    return () => clearTimeout(timer);
  }, [countdown, setCheckVerifyCode]);

  return (
    <Root>
      <TitleBox>
        <TitleLabel>申请试用</TitleLabel>
      </TitleBox>
      <FormBox>
        <DynamicForm
          ref={formRef}
          columns={OnTrialColumns}
          // labelWidth={70}
          rowSpacing={0}
          direction="column"
        />
        <FormControl
          fullWidth
          error={errorText ? true : false}
          sx={{
            flexDirection: "column",
            height: "100%",
          }}
        >
          <FormLabel
            component={"div"}
            required={true}
            title={"邮箱验证码"}
            sx={{
              textWrap: "nowrap",
              mr: 1,
              textAlign: "right",
              width: 70,
              overflow: "hidden",
              textOverflow: "ellipsis",
              color: "rgba(31, 31, 31, 1)",
              fontWeight: 400,
              fontSize: 14,
              // mt: 0.5,
            }}
          >
            验证码:
          </FormLabel>
          <VerifyInput>
            <OutlinedInput
              fullWidth
              error={errorText ? true : false}
              type={"string"}
              endAdornment={
                <InputAdornment position="end">
                  <Button
                    size="small"
                    onClick={handleGetCode}
                    disabled={countdown ? true : false}
                    sx={{ color: "rgba(24, 112, 199, 1)" }}
                  >
                    {countdown ? `${countdown}秒后重新获取` : buttonText}
                  </Button>
                </InputAdornment>
              }
              placeholder="请输入邮箱验证码"
              value={verifyCode}
              onChange={handleCodeChange}
              onBlur={handleCodeBlur}
              sx={{ mt: 1.5, borderRadius: 28, height: 45 }}
            />
            {errorText && (
              <FormHelperText sx={{ mt: 0, ml: 2, color: "#d32f2f" }}>
                {errorText || " "}
              </FormHelperText>
            )}
          </VerifyInput>
        </FormControl>
      </FormBox>
      <SubmitBox>
        <ActionBar>
          <KeepLogged>
            <BpCheckbox
              size="small"
              onChange={handleReadAlready}
              value={already}
              defaultChecked={true}
            />
            <Agreement onClick={viewAgreement}>
              我已阅读并同意隐私协议 和 服务条款
            </Agreement>
          </KeepLogged>
        </ActionBar>
        <LoginSide>
          <Button variant="contained" fullWidth onClick={handleSubmit}>
            提交
          </Button>
          <BackLogin onClick={() => setAction("login")}>
            已有账号，立即登录
          </BackLogin>
        </LoginSide>
      </SubmitBox>
      <AgreementDialog open={open} setOpen={setOpen} />
    </Root>
  );
};
export default OnTrialBox;
