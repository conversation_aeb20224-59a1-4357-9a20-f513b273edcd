import React, { useEffect, useState, useMemo, memo } from "react";
import { styled } from "@mui/material";
import ReactECharts from "echarts-for-react";
import "echarts-wordcloud";
import { anyValueProps } from "@/types/common";

const StyledContainer = styled("div")(({ theme }) => ({
  width: 358,
  height: "35%",
  borderRadius: "20px",
  padding: theme.spacing(0, 2),
  background: "#fff",
  boxSizing: "border-box",
  position: "relative",
}));

const Header = styled("div")(() => ({
  height: 56,
  fontSize: 18,
  fontWeight: 700,
  lineHeight: "18px",
  color: "rgba(31, 31, 31, 1)",
  display: "flex",
  alignItems: "center",
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
  boxSizing: "border-box",
}));

const Content = styled("div")(() => ({
  height: "calc(100% - 56px)",
  width: "100%",
  display: "flex",
  alignItems: "center",
  padding: "10px 0",
  boxSizing: "border-box",
}));

const WithoutData = styled("div")(({ theme }) => ({
  color: theme.palette.grey[500],
  padding: theme.spacing(4, 0, 4, 0),
  textAlign: "center",
  width: "100%",
}));

interface SearchCloudProps {
  coAuthorShip: anyValueProps;
  wordLoading?: boolean;
}

const SearchCloud: React.FC<SearchCloudProps> = ({
  coAuthorShip,
  wordLoading,
}) => {
  // const history = useHistory();
  // const [expanded, setExpanded] = useState(true);
  const [data, setData] = useState<anyValueProps[]>([]);
  const chartInstanceRef = useRef<any>(null);

  const option = useMemo(
    () =>
      // shape ：词云的形状，默认是 circle，可选的参数有cardioid 、 diamond 、 triangle-forward 、 triangle 、 star。
      // left top right bottom ：词云的位置，默认是 center center。
      // width height ：词云的宽高，默认是 75% 80%。
      // sizeRange ：词云的文字字号范围，默认是[12, 60] ，词云会根据提供原始数据的 value 对文字的字号进行渲染。
      // 以默认值为例， value 最小的渲染为 12px ，最大的渲染为 60px ，中间的值按比例计算相应的数值。
      // rotationRange rotationStep ：词云中文字的角度，词云中的文字会随机的在 rotationRange 范围内旋转角度，
      // 渲染的梯度就是 rotationStep ，这个值越小，词云里出现的角度种类就越多。以上面参数为例，可能旋转的角度就是 - 90 - 45 0 45 90 。
      // gridSize ：词云中每个词的间距。
      // drawOutOfBound ：是否允许词云在边界外渲染，直接使用默认参数 false 就可以，否则容易造成词重叠。
      // textStyle ：词云中文字的样式， normal 是初始的样式， emphasis 是鼠标移到文字上的样式。
      ({
        tooltip: {
          trigger: "item",
          confine: true, // 确保 tooltip 不超出容器
          extraCssText:
            "max-width: 300px; white-space: normal; word-break: break-all;",
          formatter(params: {
            data: { originalName: string; isTruncated: boolean; isTop: number };
          }) {
            return `
              <span class="tooltip-title">${params.data.originalName}</span>`;
          },
        },
        series: [
          {
            left: "center",
            shape: "square",
            width: "100%",
            height: "100%",
            right: null,
            bottom: null,
            type: "wordCloud",
            drawOutOfBound: false,
            textStyle: {
              fontWeight: "bold",
              color() {
                return (
                  "rgb(" +
                  [
                    Math.round(Math.random() * 200),
                    Math.round(Math.random() * 200),
                    Math.round(Math.random() * 200),
                  ].join(",") +
                  ")"
                );
              },
            },
            sizeRange: [15, 25],
            rotationRange: [0, 0],
            gridSize: 8,
            rotationStep: 15,
            emphasis: {
              focus: "self",
              textStyle: {
                textShadowBlur: 10,
                textShadowColor: "#ccc",
              },
            },
            data,
          },
        ],
      }),
    [data],
  );

  useEffect(() => {
    const sortedEntries = Object.entries(coAuthorShip)
      .sort((a, b) => b[1] - a[1])
      .splice(0, 15);
    // 2. 转换成 { name, value } 格式的数组
    const result = sortedEntries.map(([name, value]) => ({
      name,
      value,
    }));
    const processWord = (word: string, maxLength = 15) =>
      word.length > maxLength ? word.substring(0, maxLength) + "..." : word;

    const processedData = result.map((item: any, index: any) => ({
      // 显示用名称（超长会被截断加省略号）
      name: processWord(item.name),
      // 保留原始完整名称
      originalName: item.name,
      value: item.value,
      // 标记是否被截断
      isTruncated: item.name.length > 15,
      isTop: index,
    }));
    setData(processedData);
  }, [coAuthorShip]);

  useEffect(() => {
    const handleResize = () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.resize();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // const handleCollapse = () => {
  //   setExpanded(!expanded);
  // };

  // const onClickCharts = (params: any) => {
  //   history.push(`/author-detail/${params.name}`);
  // };

  return (
    <StyledContainer>
      <Header>词云</Header>
      <Content>
        {Object.keys(coAuthorShip).length > 0 ? (
          <ReactECharts
            // onEvents={{
            //   click: onClickCharts,
            // }}
            style={{ width: "100%", height: "100%" }}
            option={option}
            onChartReady={(chart) => {
              chartInstanceRef.current = chart; // 获取 ECharts 实例
            }}
          />
        ) : (
          <WithoutData> {wordLoading ? "加载中" : "暂无相关数据"}</WithoutData>
        )}
      </Content>
    </StyledContainer>
  );
};
export default memo(SearchCloud);
