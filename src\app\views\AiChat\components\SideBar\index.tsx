import React from "react";
import { <PERSON>, <PERSON><PERSON>, Icon<PERSON>utton } from "@mui/material";
import { styled } from "@mui/material/styles";

import historyIcon from "@/assets/history.svg";
import List from "@/views/AiChat/components/List";
import {
  changeValue,
  clearCurrentChat,
  setActives,
  setSelectedBank,
} from "@/store/counterSlice";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
// import { changeValue } from "@/store/counterSlice";
import ArrowForwardIosOutlinedIcon from "@mui/icons-material/ArrowForwardIosOutlined";
import ArrowBackIosOutlinedIcon from "@mui/icons-material/ArrowBackIosOutlined";
import { getNewChatId } from "@/api/chat";

const Root = styled("div", {
  shouldForwardProp: (p) => p !== "shrink",
})<{
  shrink: boolean;
}>(({ shrink }) => ({
  width: shrink ? "260px" : "70px",
  height: shrink ? "100%" : "55px",
  display: "flex",
  flexDirection: "column",
  transition: "height 0.2s linear",
  opacity: 1,
  borderRadius: "20px",
  background: shrink
    ? "radial-gradient(258.82% 69.42% at 17.***************% -40.***************%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(242, 243, 247, 1)"
    : "radial-gradient(43.57% 68.18% at 17.***************% -39.**************%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(242, 243, 247, 1);",

  border: "2px solid rgba(255, 255, 255, 1)",
  boxSizing: "border-box",
}));

const HistoryDiv = styled("div")(() => ({
  height: "35px",
  lineHeight: "35px",
  margin: "8px 5px 0 12px",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
}));

const AddButton = styled(Button)(() => ({
  width: "100%",
  height: "38px",
  borderRadius: "19px",
  background:
    " linear-gradient(90deg, rgba(110, 84, 227, 1) 0%, rgba(27, 130, 227, 1) 100%)",
}));

const ListContent = styled("div")(() => ({
  flex: 1,
  display: "flex",
  flexDirection: "column",
  height: 0,
}));
const ListBox = styled("div")(({ theme }) => ({
  boxSizing: "border-box",
  padding: `0 ${theme.spacing()}`,
  flex: 1,
  height: 0,
}));

export interface knowledgeDataProps {
  kb_name: string;
  kb_version: string;
  kb_description: null | string;
  id: number;
}
export interface knowledgeOptionProps {
  label: string;
  value: number;
}

const SideBar: React.FC = () => {
  const dispatch = useAppDispatch();
  const { active, appSetting } = useAppSelector(
    (state: RootState) => state.counter,
  );
  const [shrink, setShrink] = useState(true);
  const [total, setTotal] = useState(0);

  const ChangeSettingValue = (field: string, value: number) => {
    if (appSetting?.[field] !== value) {
      dispatch(changeValue({ field, value }));
    }
  };

  const getChatId = async () => {
    const {
      data: { data },
    } = await getNewChatId();
    dispatch(setActives(data));
  };
  const handleAdd = () => {
    if (!active && total < 1000) {
      dispatch(setActives(null));
      dispatch(setSelectedBank({}));
      dispatch(clearCurrentChat());
      ChangeSettingValue("temperature", 0.2);
      ChangeSettingValue("normal", 50);
      message.success("已是最新对话");
      getChatId();
    } else if (total >= 1000) {
      message.warning("聊天窗口已到达1000次,请删除之前创建的聊天窗口");
    } else {
      dispatch(setActives(null));
      dispatch(setSelectedBank({}));
      dispatch(clearCurrentChat());
      ChangeSettingValue("temperature", 0.2);
      ChangeSettingValue("normal", 50);
      getChatId();
    }
  };
  return (
    <Root shrink={shrink}>
      <HistoryDiv>
        <div style={{ display: "flex", alignItems: "center" }}>
          <img
            src={historyIcon}
            alt=""
            width={16}
            height={16}
            style={{ marginRight: "8px" }}
          />
          {shrink ? "历史记录" : ""}
        </div>
        <IconButton
          aria-label="left"
          size="small"
          onClick={() => setShrink(!shrink)}
          sx={{
            transform: "scale(0.5)",
            background: "#fff",
            flexGrow: 0,
            width: "35px",
            height: "35px",
          }}
        >
          {shrink ? (
            <ArrowBackIosOutlinedIcon
              fontSize="inherit"
              sx={{ fontSize: "25px" }}
            />
          ) : (
            <ArrowForwardIosOutlinedIcon
              fontSize="inherit"
              sx={{ fontSize: "25px" }}
            />
          )}
        </IconButton>
      </HistoryDiv>
      {shrink ? (
        <ListContent>
          <ListBox>
            <List setTotal={setTotal} total={total} />
          </ListBox>
          <Box sx={{ p: 2 }}>
            <AddButton variant="contained" onClick={handleAdd}>
              新建对话
            </AddButton>
          </Box>
        </ListContent>
      ) : (
        ""
      )}
    </Root>
  );
};

export default SideBar;
