import { createTheme } from "@mui/material/styles";

const customTheme = createTheme({
  palette: {
    primary: {
      main: "rgba(0, 104, 177)",
      dark: "rgba(0, 120, 177)",
      light: "rgb(247, 247, 247)",
    },
    secondary: {
      main: "rgba(255,255,255)",
      dark: "rgba(244,246,248)",
      light: "rgba(229,229,229)",
    },
    info: {
      main: "#ccc",
      dark: "#888",
      light: "#474b83",
    },
    error: {
      main: "#d32f2f",
      dark: "#c62828",
      light: "#ef5350",
    },
  },
  typography: {
    // 一级标题
    subtitle1: {
      fontSize: 18,
      lineHeight: 1,
      fontWeight: 700,
      color: "rgba(64, 64, 64, 1)",
    },
    subtitle2: {
      fontSize: 16,
    },
    // 摘要字体
    body1: {
      fontSize: 14,
      color: "rgba(64, 64, 64, 1)",
    },
    // 摘要字体 -- 浅
    body2: {
      fontSize: 14,
      color: "rgba(64, 64, 64, 0.8)",
    },
  },
  components: {
    // 调整*号位置
    MuiFormLabel: {
      styleOverrides: {
        root: {
          display: "flex",
          "& .MuiFormLabel-asterisk": {
            order: -1, // 调整顺序
            marginRight: "4px", // 添加间距
            color: "red", // 自定义颜色
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: "none", // 取消大写转换
        },
      },
    },
  },
});

export default customTheme;
