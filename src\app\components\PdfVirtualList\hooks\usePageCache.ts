import { useRef, useCallback } from "react";
import { CacheEntry } from "../types/types";

interface PageCacheEntry extends CacheEntry<React.ReactElement> {
  pageNumber: number;
  scale: number;
  containerWidth: number;
}

/**
 * 页面渲染缓存Hook
 * 使用LRU策略管理页面缓存，提高渲染性能
 */
export const usePageCache = () => {
  const cache = useRef(new Map<string, PageCacheEntry>());
  const maxCacheSize = useRef(20); // 最大缓存页面数
  const accessOrder = useRef<string[]>([]); // LRU访问顺序

  /**
   * 生成缓存键
   */
  const generateCacheKey = useCallback((
    pageNumber: number,
    scale: number,
    containerWidth: number,
    url: string
  ): string => {
    return `${url}_${pageNumber}_${scale.toFixed(2)}_${containerWidth}`;
  }, []);

  /**
   * LRU缓存清理
   */
  const evictLRU = useCallback((): void => {
    while (cache.current.size >= maxCacheSize.current && accessOrder.current.length > 0) {
      const oldestKey = accessOrder.current.shift();
      if (oldestKey && cache.current.has(oldestKey)) {
        cache.current.delete(oldestKey);
      }
    }
  }, []);

  /**
   * 更新访问顺序
   */
  const updateAccessOrder = useCallback((key: string): void => {
    // 移除旧的访问记录
    const index = accessOrder.current.indexOf(key);
    if (index > -1) {
      accessOrder.current.splice(index, 1);
    }
    // 添加到末尾（最新访问）
    accessOrder.current.push(key);
  }, []);

  /**
   * 获取缓存的页面
   */
  const getCachedPage = useCallback((
    pageNumber: number,
    scale: number,
    containerWidth: number,
    url: string,
    renderFn: () => React.ReactElement
  ): React.ReactElement => {
    const key = generateCacheKey(pageNumber, scale, containerWidth, url);
    const entry = cache.current.get(key);

    if (entry) {
      // 更新访问计数和时间戳
      entry.accessCount++;
      entry.timestamp = Date.now();
      updateAccessOrder(key);
      return entry.data;
    }

    // 缓存未命中，执行渲染函数
    const page = renderFn();
    
    // 清理旧缓存
    evictLRU();
    
    // 添加新缓存
    const newEntry: PageCacheEntry = {
      data: page,
      timestamp: Date.now(),
      accessCount: 1,
      pageNumber,
      scale,
      containerWidth,
    };
    
    cache.current.set(key, newEntry);
    updateAccessOrder(key);
    
    return page;
  }, [generateCacheKey, evictLRU, updateAccessOrder]);

  /**
   * 预加载页面到缓存
   */
  const preloadPage = useCallback((
    pageNumber: number,
    scale: number,
    containerWidth: number,
    url: string,
    renderFn: () => React.ReactElement
  ): void => {
    const key = generateCacheKey(pageNumber, scale, containerWidth, url);
    
    if (!cache.current.has(key)) {
      // 使用requestIdleCallback进行空闲时预加载
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => {
          getCachedPage(pageNumber, scale, containerWidth, url, renderFn);
        });
      } else {
        // 降级方案：使用setTimeout
        setTimeout(() => {
          getCachedPage(pageNumber, scale, containerWidth, url, renderFn);
        }, 0);
      }
    }
  }, [generateCacheKey, getCachedPage]);

  /**
   * 清空特定条件的缓存
   */
  const clearCache = useCallback((predicate?: (entry: PageCacheEntry) => boolean): void => {
    if (!predicate) {
      cache.current.clear();
      accessOrder.current = [];
      return;
    }

    const keysToDelete: string[] = [];
    for (const [key, entry] of cache.current.entries()) {
      if (predicate(entry)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => {
      cache.current.delete(key);
      const index = accessOrder.current.indexOf(key);
      if (index > -1) {
        accessOrder.current.splice(index, 1);
      }
    });
  }, []);

  /**
   * 清空过期缓存
   */
  const clearExpiredCache = useCallback((maxAge: number = 5 * 60 * 1000): void => {
    const now = Date.now();
    clearCache(entry => now - entry.timestamp > maxAge);
  }, [clearCache]);

  /**
   * 获取缓存统计信息
   */
  const getCacheStats = useCallback(() => {
    let totalAccess = 0;
    let totalHits = 0;
    const sizeByScale = new Map<number, number>();

    for (const entry of cache.current.values()) {
      totalAccess += entry.accessCount;
      totalHits += entry.accessCount - 1; // 第一次访问不算命中
      
      const scaleKey = Math.round(entry.scale * 10) / 10;
      sizeByScale.set(scaleKey, (sizeByScale.get(scaleKey) || 0) + 1);
    }

    return {
      size: cache.current.size,
      maxSize: maxCacheSize.current,
      hitRate: totalAccess > 0 ? totalHits / totalAccess : 0,
      sizeByScale: Object.fromEntries(sizeByScale),
      accessOrderLength: accessOrder.current.length,
    };
  }, []);

  /**
   * 设置最大缓存大小
   */
  const setMaxCacheSize = useCallback((size: number): void => {
    maxCacheSize.current = size;
    evictLRU(); // 立即清理超出限制的缓存
  }, [evictLRU]);

  return {
    getCachedPage,
    preloadPage,
    clearCache,
    clearExpiredCache,
    getCacheStats,
    setMaxCacheSize,
  };
};
