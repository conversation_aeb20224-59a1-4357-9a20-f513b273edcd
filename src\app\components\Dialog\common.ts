export interface DialogProps {
  open: boolean;
  width?: number | string;
  setDialogOpen: (value: boolean) => void;
  title?: string;
  center?: boolean;
  hideCloseButton?: boolean;
  hideTitleCloseButton?: boolean;
  children?: React.ReactNode;
  okButtonProps?: ButtonProps;
  cancelButtonProps?: ButtonProps;
  maskClosable?: boolean; // 点击遮罩层是否关闭弹窗
  btnIsCenter?: boolean;
  bgColor?: string;
  buttonHeight?: string;
}

export interface ButtonProps {
  text?: string;
  onOk?: () => void;
  onCancel?: () => void;
  loading?: boolean;
  disabled?: boolean;
  size?: "small" | "large" | "medium";
}

export interface DialogHeaderProps {
  title?: string;
  onClose: () => void;
  hideCloseButton?: boolean;
  center?: boolean;
  header?: React.ReactNode;
}

export interface DialogFooterProps {
  cancel: () => void;
  confirm: () => void;
  hideCloseButton?: boolean;
  center?: boolean;
  footer?: React.ReactNode;
  okButtonProps?: ButtonProps;
  cancelButtonProps?: ButtonProps;
  btnIsCenter?: boolean;
  buttonHeight?: string;
}
