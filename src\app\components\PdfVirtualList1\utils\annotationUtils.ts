import { RawAnnotation, FormattedAnnotation } from "../types/types";
function findMinObject(
  data: Record<number, { width: number; height: number }>,
) {
  let minEntry = null;

  for (const [key, value] of Object.entries(data)) {
    if (
      !minEntry ||
      (value.width < minEntry.width && value.height < minEntry.height)
    ) {
      minEntry = { key, ...value };
    }
  }

  return minEntry;
}

/**
 * 将原始标注数据转换为按页面分组的格式化标注数组
 * @param annotations - 原始标注数据数组
 * @param pageDimensions - 页面尺寸记录，key为页码，value为宽高对象
 * @returns 按页面分组的格式化标注数组
 */
export function formatAnnotations(
  annotations: RawAnnotation[],
  pageDimensions: Record<number, { width: number; height: number }>,
): FormattedAnnotation[][] {
  // 参数验证
  if (
    !annotations?.length ||
    !pageDimensions ||
    !Object.keys(pageDimensions).length
  ) {
    return [];
  }

  // 获取第一页的尺寸作为基准
  const basePage = findMinObject(pageDimensions);
  if (!basePage?.width || !basePage?.height) {
    console.warn("无效的页面尺寸数据");
    return [];
  }

  // 使用 Map 来存储每页的标注，避免数组预分配
  const pageAnnotationsMap = new Map<number, FormattedAnnotation[]>();

  // 遍历标注数据并格式化
  for (const item of annotations) {
    if (!item.frameCoords?.length) continue;

    for (const coord of item.frameCoords) {
      // 计算坐标
      const xMin = basePage.width * coord.upLeftScale[0];
      const xMax = basePage.width * coord.lowRightScale[0];
      const yMin = basePage.height * coord.upLeftScale[1];
      const yMax = basePage.height * coord.lowRightScale[1];

      // 创建标注对象
      const annotation: FormattedAnnotation = {
        id: `annotation_${item.paragraphId}`,
        text: item.paragraph,
        x: [xMin, xMax],
        y: [yMin, yMax],
        page: coord.page,
        originalWidth: basePage.width,
        originalHeight: basePage.height,
      };

      // 获取或创建页面数组
      const pageIndex = coord.page - 1;
      const pageAnnotations = pageAnnotationsMap.get(pageIndex) || [];
      pageAnnotations.push(annotation);
      pageAnnotationsMap.set(pageIndex, pageAnnotations);
    }
  }

  // 将 Map 转换为数组，确保按页码顺序排列
  const maxPage = Math.max(...Array.from(pageAnnotationsMap.keys()));
  const result: FormattedAnnotation[][] = [];

  for (let i = 0; i <= maxPage; i++) {
    result[i] = pageAnnotationsMap.get(i) || [];
  }

  return result;
}
