import { styled } from "@mui/material";
import React, { useState, useCallback, useMemo } from "react";
import { But<PERSON> } from "@mui/material";
import PdfDrawer from "@/views/PaperBase/components/PersonalPaper/components/PdfDrawer";
import { downloadFile, getFile } from "@/api/paperSearch";
import PaperPopover from "../../components/PaperPopover";
import { setActives, setSelectedBank } from "@/store/counterSlice";
import { RootState, useAppDispatch, useAppSelector } from "@/hooks";
import { useNavigate } from "react-router-dom";
import { withPermission } from "@/components/HocButton";
import { PERMISSION_MENU } from "@/utils/permission";
import AdminPaperPopover from "../../components/PaperPopover/AdminPaperPopover";
import { checkPermission } from "@/utils/auth";
import { anyValueProps } from "@/types/common";
import { getNewChatId } from "@/api/chat";
import { message } from "@/components/MessageBox/message";
import En_Icon from "@/assets/en-icon.svg";
import Zh_Icon from "@/assets/zh-icon.svg";
import { flattenDocuments } from "./util";

const Main = styled("div")(() => ({
  width: "100%",
  padding: "20px 0 10px 0",
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
  background: "#fff",
}));

interface PaperHeaderProps {
  headerParams: anyValueProps;
  init: () => void;
  share?: string | null;
  retry?: () => void;
}

const TitleNav = styled("div")(() => ({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 16,
}));

const TitleLable = styled("div")(() => ({
  width: "70%",
  fontSize: "18px",
  fontWeight: 700,
  letterSpacing: 0,
  lineHeight: "18px",
  color: "rgba(31, 31, 31, 1)",
  verticalAlign: "top",
}));

const TitleText = styled("div")(() => ({
  fontSize: "18px",
  fontWeight: 700,
  letterSpacing: 0,
  lineHeight: "18px",
  color: "rgba(31, 31, 31, 1)",
  display: "flex",
  alignItems: "center",
  gap: "8px",
}));

const LanguageIcon = styled("img")<{ size: number }>(({ size }) => ({
  width: size,
  height: size,
}));

const ButtonGroup = styled("div")(() => ({
  height: "100%",
  display: "flex",
}));

const ViewButton = styled(Button)(() => ({
  width: 70,
  height: 32,
  borderRadius: "14.5px",
  background: "rgba(235, 244, 255, 1)",
  marginLeft: 20,
}));

const StyleButton = styled(Button, {
  shouldForwardProp: (prop) => prop !== "MyType",
})<{ MyType: "normal" | "add" }>(({ MyType }) => ({
  width: MyType === "normal" ? 84 : 100,
  height: 32,
  borderRadius: "28px",
  background: "rgba(242, 242, 242, 1)",
  color: "rgba(64, 64, 64, 1)",
  fontSize: 14,
  boxSizing: "border-box",
  marginLeft: 20,
}));

const InfoBox = styled("div")(() => ({
  color: "rgba(64, 64, 64, 1)",
  fontSize: "14px",
  fontWeight: 400,
  verticalAlign: "top",
}));

const AuthorsBox = styled(InfoBox)(() => ({
  lineHeight: "14px",
  flexWrap: "wrap",
  alignItems: "center",
}));

const DataBox = styled(InfoBox)(() => ({
  lineHeight: "21px",
  marginTop: 10,
}));

const AttributionBox = styled("div")(() => ({
  display: "flex",
  alignItems: "flex-start",
  color: "rgba(64, 64, 64, 1)",
  fontSize: "14px",
  fontWeight: 400,
  verticalAlign: "top",
  marginTop: 10,
}));

const AttributionContent = styled("div")(() => ({
  color: "rgb(29, 90, 246)",
  fontSize: "14px",
  cursor: "pointer",
}));

const AttributionContentBox = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  flexWrap: "wrap",
}));

interface ButtonComponentProps {
  action: () => void;
}

interface AddPaperBaseProps {
  headerParams: anyValueProps;
}

const AiChatButton: React.FC<ButtonComponentProps> = ({ action }) => {
  const StyleAiChatButton: React.FC<any> = ({ action }) => (
    <StyleButton MyType="normal" onClick={action}>
      +AI对话
    </StyleButton>
  );
  const PermissionButton = withPermission(
    StyleAiChatButton,
    PERMISSION_MENU["chat"],
  );
  return <PermissionButton action={action} />;
};

const DownLoadButton: React.FC<ButtonComponentProps> = ({ action }) => {
  const StyleDownLoadButton: React.FC<any> = ({ action }) => (
    <StyleButton MyType="normal" onClick={action}>
      下载
    </StyleButton>
  );
  const PermissionButton = withPermission(
    StyleDownLoadButton,
    PERMISSION_MENU["edit"],
  );
  return <PermissionButton action={action} />;
};

const AddPaperBaseButton: React.FC<AddPaperBaseProps> = ({ headerParams }) => {
  const StyleAiChatButton: React.FC<any> = ({ headerParams }) => {
    const { roleOption } = useAppSelector((state) => state.user);
    const [pdfId, setPdfId] = useState<string>("");
    const [anchorEl, setAnchorEl] = useState<null | HTMLButtonElement>(null);
    const [adminanchorEl, setAdminAnchorEl] =
      useState<null | HTMLButtonElement>(null);

    const onClickPaperBase = useCallback(
      (event: React.MouseEvent<HTMLButtonElement>) => {
        if (!headerParams?.id) return;

        setPdfId(headerParams.id);
        if (checkPermission(roleOption)) {
          setAdminAnchorEl(event.currentTarget);
        } else {
          setAnchorEl(event.currentTarget);
        }
      },
      [headerParams?.id, roleOption],
    );

    const handleClose = useCallback(() => {
      setAnchorEl(null);
    }, []);

    const handleAdminClose = useCallback(() => {
      setAdminAnchorEl(null);
    }, []);

    return (
      <>
        <StyleButton MyType="add" onClick={onClickPaperBase}>
          加入资料库
        </StyleButton>
        {Boolean(anchorEl) && (
          <PaperPopover
            id="simple-popover"
            open={Boolean(anchorEl)}
            onClose={handleClose}
            anchorEl={anchorEl}
            pdfId={pdfId}
          />
        )}
        {Boolean(adminanchorEl) && (
          <AdminPaperPopover
            id="simple-popover"
            open={Boolean(adminanchorEl)}
            onClose={handleAdminClose}
            anchorEl={adminanchorEl}
            pdfId={pdfId}
          />
        )}
      </>
    );
  };

  const PermissionButton = withPermission(
    StyleAiChatButton,
    PERMISSION_MENU["edit"],
  );
  return <PermissionButton headerParams={headerParams} />;
};

const RetryButton = ({ action }: { action: any }) => {
  const StyleUploadButton: React.FC<any> = ({ action }) => (
    <StyleButton MyType="normal" onClick={action}>
      重新解析
    </StyleButton>
  );

  const PermissionButton = withPermission(
    StyleUploadButton,
    PERMISSION_MENU["edit"],
  );
  return <PermissionButton action={action} />;
};

const PaperHeader: React.FC<PaperHeaderProps> = ({
  headerParams,
  share,
  retry,
}) => {
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [drawerTitle, setDrawerTitle] = useState<string>("");
  const [pdfUrl, setPdfUrl] = useState<string>("");

  const { listTotal } = useAppSelector((state: RootState) => state.counter);
  const dispatch = useAppDispatch();
  const navigator = useNavigate();

  const viewPdf = useCallback(async () => {
    if (!headerParams?.pdfUrl || !headerParams?.id) {
      message.error("PDF信息不完整");
      return;
    }

    try {
      setPdfUrl("");
      setDrawerOpen(true);
      setDrawerTitle(headerParams.drawerTitle || "PDF预览");

      const { data } = await getFile(
        `${headerParams.pdfUrl}&pdfId=${headerParams.id}`,
      );
      const pdfData = new Blob([data], { type: "application/pdf" });
      const pdfDownloadUrl = window.URL.createObjectURL(pdfData);
      setPdfUrl(pdfDownloadUrl);
    } catch (error) {
      message.error(`获取PDF失败: ${error}`);
      setDrawerOpen(false);
    }
  }, [headerParams?.pdfUrl, headerParams?.id, headerParams?.title]);

  const getChatId = useCallback(async () => {
    try {
      const {
        data: { data },
      } = await getNewChatId();
      dispatch(setActives(data));
    } catch (error) {
      message.error(`获取聊天ID失败: ${error}`);
    }
  }, [dispatch]);

  const chatPdf = useCallback(() => {
    if (!headerParams?.id) {
      message.error("论文信息不完整");
      return;
    }

    if (listTotal >= 1000) {
      message.warning("聊天窗口已到达1000次,请删除之前创建的聊天窗口");
      return;
    }

    dispatch(setActives(null));
    const id = Number(headerParams.id);
    dispatch(setSelectedBank({ externalIds: [id], bankType: 2 }));
    navigator("/ai-chat");
    getChatId();
  }, [headerParams?.id, listTotal, dispatch, navigator, getChatId]);

  const downLoadPdf = useCallback(async () => {
    if (!headerParams?.pdfUrl || !headerParams?.id || !headerParams?.pdfName) {
      message.error("下载信息不完整");
      return;
    }

    try {
      const { pdfName, pdfUrl, id } = headerParams;
      const { data } = await downloadFile(`${pdfUrl}&pdfId=${id}`);

      const url = window.URL.createObjectURL(new Blob([data]));
      const a = document.createElement("a");
      a.href = url;
      a.download = pdfName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      message.error(`下载失败: ${error}`);
    }
  }, [headerParams?.pdfUrl, headerParams?.id, headerParams?.pdfName]);

  const renderTitle = useMemo(() => {
    if (!headerParams?.title && !headerParams?.titleZh) {
      return <div>暂无数据</div>;
    }

    const showLanguageIcons =
      headerParams?.language === "zh" &&
      headerParams?.title &&
      headerParams?.titleZh;

    return (
      <>
        {headerParams?.titleZh && (
          <TitleText
            style={{ marginBottom: headerParams?.titleZh ? "5px" : 0 }}
          >
            {showLanguageIcons && (
              <LanguageIcon src={Zh_Icon} alt="中文" size={18} />
            )}
            {headerParams.titleZh.replace(/#/g, "")}
          </TitleText>
        )}
        {headerParams?.title && (
          <TitleText>
            {showLanguageIcons && <LanguageIcon src={En_Icon} size={18} />}
            {headerParams.title.replace(/#/g, "")}
          </TitleText>
        )}
      </>
    );
  }, [headerParams?.title, headerParams?.titleZh, headerParams?.language]);

  const renderAuthors = useMemo(() => {
    if (!headerParams?.authors?.length && !headerParams?.zhAuthors?.length) {
      return "作者：暂无数据";
    }

    const showLanguageIcons =
      headerParams?.language === "zh" &&
      headerParams?.authors?.length > 0 &&
      headerParams?.zhAuthors?.length > 0;

    return (
      <>
        {headerParams?.zhAuthors?.length > 0 && (
          <div
            style={{
              marginBottom: headerParams?.zhAuthors?.length > 0 ? "5px" : 0,
              display: "flex",
              alignItems: "center",
              flexWrap: "wrap",
            }}
          >
            {showLanguageIcons && (
              <LanguageIcon
                src={Zh_Icon}
                alt="中文"
                size={18}
                style={{ marginRight: "8px" }}
              />
            )}
            <span>作者：</span>
            {headerParams.zhAuthors.map((author: string, index: number) => (
              <span key={index} style={{ marginRight: "15px" }}>
                {author}
              </span>
            ))}
          </div>
        )}
        {headerParams?.authors?.length > 0 && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              flexWrap: "wrap",
            }}
          >
            {showLanguageIcons && (
              <LanguageIcon
                src={En_Icon}
                size={18}
                style={{ marginRight: "8px" }}
              />
            )}
            <span>作者：</span>
            {headerParams.authors.map((author: string, index: number) => (
              <span key={index} style={{ marginRight: "15px" }}>
                {author}
              </span>
            ))}
          </div>
        )}
      </>
    );
  }, [headerParams?.authors, headerParams?.zhAuthors, headerParams?.language]);

  const parseZhData = (value: string | number | null | undefined) => {
    if (!value) {
      return "";
    }
    // 如果是数字，直接返回
    if (typeof value === "number") {
      return value;
    }
    // 如果是字符串，尝试解析 JSON
    try {
      return JSON.parse(value);
    } catch {
      return value;
    }
  };

  const renderInfoRow = useCallback(
    (label: string, value: string | number | null | undefined, suffix = "") => (
      <DataBox>
        {label}：{value ? `${parseZhData(value)}${suffix}` : "暂无数据"}
      </DataBox>
    ),
    [],
  );

  const handleAttributionClick = (type: string, data?: anyValueProps) => {
    switch (type) {
      case "database":
        navigator("/paper-base");
        break;
      default: {
        const urlParams = {
          share: data?.documentSourceType === "share",
          resourceCode: data?.resourceCode,
          paperBaseName: encodeURIComponent(data?.name),
          groupName: encodeURIComponent(data?.resourceName),
        };
        const queryString = new URLSearchParams(
          Object.entries(urlParams).reduce(
            (acc, [key, value]) => {
              acc[key] = String(value);
              return acc;
            },
            {} as Record<string, string>,
          ),
        ).toString();

        navigator(
          `/paper-base/TopicDocDB/personal-paper/${data?.id}?${queryString}`,
        );
        break;
      }
    }
  };

  const formattedExistDocumentRes = useMemo(() => {
    if (!headerParams?.existDocumentRes) {
      return [];
    }
    return flattenDocuments(headerParams.existDocumentRes);
  }, [headerParams?.existDocumentRes]); // 添加依赖项，当数据变化时重新计算

  const renderAttributionRow = useCallback(
    () => (
      <AttributionBox>
        <span style={{ whiteSpace: "nowrap" }}>归属：</span>
        {formattedExistDocumentRes.length > 0 ? (
          <AttributionContentBox>
            <AttributionContent
              onClick={() => handleAttributionClick("database")}
            >
              资料库
            </AttributionContent>
            -
            {formattedExistDocumentRes.map(
              (item: anyValueProps, index: number) => (
                <React.Fragment key={item.id || index}>
                  <AttributionContent
                    onClick={() => handleAttributionClick("literature", item)}
                  >
                    {item.name}
                  </AttributionContent>
                  {index !== formattedExistDocumentRes.length - 1 && "/"}
                </React.Fragment>
              ),
            )}
          </AttributionContentBox>
        ) : (
          "暂无数据"
        )}
      </AttributionBox>
    ),
    [formattedExistDocumentRes],
  ); // 添加依赖项，确保数据变化时重新渲染

  const isTestReport = headerParams?.docType === "common_doc";
  const isZhPaper =
    headerParams?.language === "zh" && headerParams?.docType === "paper";

  const zhPaperLabel = [
    {
      label: "中图分类号",
      value: headerParams?.ext1,
    },
    {
      label: "文献标识码",
      value: headerParams?.ext2,
    },
    {
      label: "文章编号",
      value: headerParams?.ext3,
    },
    {
      label: "作者简介",
      value: headerParams?.ext4,
    },
  ];

  return (
    <Main>
      <TitleNav>
        <TitleLable>{renderTitle}</TitleLable>
        <ButtonGroup>
          {share !== "true" && <RetryButton action={retry} />}
          <ViewButton onClick={viewPdf}>预览</ViewButton>
          <AiChatButton action={chatPdf} />
          <DownLoadButton action={downLoadPdf} />
          {share !== "true" && (
            <AddPaperBaseButton headerParams={headerParams} />
          )}
        </ButtonGroup>
      </TitleNav>

      {!isTestReport && <AuthorsBox>{renderAuthors}</AuthorsBox>}
      {/* {isTestReport && renderInfoRow("年份", headerParams?.ext2)} */}

      {!isTestReport && renderInfoRow("DOI", headerParams?.doi)}
      {!isTestReport &&
        renderInfoRow("出版年", headerParams?.publishedYear, "年")}
      {!isTestReport && renderInfoRow("期刊", headerParams?.journal)}
      {isZhPaper &&
        zhPaperLabel.map((item) => renderInfoRow(item.label, item.value))}
      {renderAttributionRow()}
      {drawerOpen && (
        <PdfDrawer
          setOpen={setDrawerOpen}
          width={800}
          title={drawerTitle}
          pdfUrl={pdfUrl}
        />
      )}
    </Main>
  );
};

export default PaperHeader;
