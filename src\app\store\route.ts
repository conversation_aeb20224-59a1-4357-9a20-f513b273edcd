import { queryMenuList } from "@/api/login";
import { RouteProps, dynamicRouteList } from "@/router";
import { filterMenuData, filterPermRoutes } from "@/utils/permission";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

const initialState = {
  systemRoute: [] as RouteProps[],
  permMenus: [] as any[],
  buttonPermMenus: [] as any[],
};
const routeSlice = createSlice({
  name: "route",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getSystemMenu.fulfilled, (state, { payload }) => {
        const { menuConfigList, menuPermission, buttonPermission } = payload;
        if (
          (!menuPermission || menuPermission.length === 0) &&
          (!buttonPermission || buttonPermission.length === 0)
        ) {
          window.location.href = window.APP_CONFIG.BASE_PATH + "/401";
        }
        const permMenus = filterMenuData(menuConfigList, menuPermission);
        state.permMenus = permMenus;
        const hasAiChat = permMenus.some(
          (item: any) => item.menuCode === "AiChat",
        );
        state.buttonPermMenus = [
          ...buttonPermission,
          ...(hasAiChat ? ["CHAT"] : []),
          "NORMAL",
        ];
        state.systemRoute = filterPermRoutes(dynamicRouteList, permMenus);
      })
      .addCase(getSystemMenu.rejected, (state) => {
        // 获取权限菜单失败
        state.systemRoute = initialState.systemRoute;
      });
  },
});

// 获取权限菜单
export const getSystemMenu = createAsyncThunk(
  "user/getSystemMenu",
  async (roleId: string, { rejectWithValue }) => {
    try {
      const res = await queryMenuList({ appCode: "aihub-chat", roleId });
      return res.data.result[0] || {};
    } catch (error) {
      return rejectWithValue("权限菜单获取失败" + error);
    }
  },
);
export default routeSlice.reducer;
