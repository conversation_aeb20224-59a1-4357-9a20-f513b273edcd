import { FC } from "react";
import { styled } from "@mui/material";
import TextComponent from "./Text";
import CircularProgress from "@mui/material/CircularProgress";
import Scheme from "./Scheme";
import Operate from "./Operate";
import { Props } from "./common";

const Root = styled("div")(() => ({
  display: "flex",
  boxSizing: "border-box",
  width: "100%",
}));

const TextContent = styled("div")(() => ({
  display: "flex",
  flexDirection: "column",
  width: "100%",
}));

const Index: FC<Props> = ({
  text,
  inversion,
  handleDelete,
  thinkText,
  additionalInfo,
  setDrawerOpen,
  setPdfUrl,
  loading,
  isLocalStorage,
  currentLoading,
  schemeLoading,
  setHistoryData,
  progressText,
  itemIndex,
  setExpandedIndex,
  expandedIndex,
  stopMsg,
  setPaperData,
  setCurrentPaperItemId,
  count,
  templateId,
  aiChemUrl,
  onGetBoxHeight,
}) => {
  function isJsonString(str: string) {
    try {
      const parsedValue = JSON.parse(str);
      return (
        typeof parsedValue === "object" &&
        Reflect.has(parsedValue, "run_status") &&
        Reflect.has(parsedValue, "data")
      );
    } catch {
      return false;
    }
  }
  return (
    <Root>
      <TextContent>
        <div>
          {(loading && isLocalStorage && !currentLoading) ||
          (!progressText &&
            !thinkText &&
            text.length === 0 &&
            loading &&
            isLocalStorage) ? (
            <CircularProgress sx={{ color: "#ccc" }} size="20px" />
          ) : (
            <>
              {!isJsonString(text) ? (
                <>
                  <TextComponent
                    inversion={inversion}
                    text={text}
                    setDrawerOpen={setDrawerOpen}
                    setPdfUrl={setPdfUrl}
                    setHistoryData={setHistoryData}
                    thinkTexts={thinkText}
                    progressTexts={progressText}
                    additionalInfo={additionalInfo}
                    schemeLoading={schemeLoading}
                    itemIndex={itemIndex}
                    setExpandedIndex={setExpandedIndex}
                    expandedIndex={expandedIndex}
                    stopMsg={stopMsg}
                    count={count}
                    setPaperData={setPaperData}
                    setCurrentPaperItemId={setCurrentPaperItemId}
                    onGetBoxHeight={onGetBoxHeight}
                  />
                  {!inversion && !currentLoading && !schemeLoading ? (
                    <Operate handleDelete={handleDelete} text={text} />
                  ) : (
                    ""
                  )}
                </>
              ) : (
                <Scheme
                  data={JSON.parse(text).data}
                  stopMsg={stopMsg}
                  handleDelete={handleDelete}
                  templateId={templateId}
                  aiChemUrl={aiChemUrl}
                  loading={schemeLoading}
                ></Scheme>
              )}
            </>
          )}
        </div>
      </TextContent>
    </Root>
  );
};

export default Index;
