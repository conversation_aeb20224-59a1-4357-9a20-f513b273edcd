import DynamicForm, { RefProps } from "@/components/DynamicForm";
import {
  FormControl,
  FormHelperText,
  FormLabel,
  InputAdornment,
  OutlinedInput,
} from "@mui/material";
import { emailRegex, forgotStep1Columns } from "../setting";
import { anyValueProps } from "@/types/common";
import { getVerifyCode, checkVerify } from "@/api/login";

interface Props {
  rowData: anyValueProps;
  setStep: (value: any) => void;
  setStepData: (value: any) => void;
  step: number;
}

const Step1Box = styled("div", {
  shouldForwardProp: (props) => props !== "step",
})<{ step: number }>(({ step }) => ({
  width: "100%",
  height: "100%",
  padding: "0 60px",
  boxSizing: "border-box",
  display: step === 0 ? "block" : "none",
}));

const FormBox = styled("div")(() => ({
  width: "100%",
  marginBottom: 30,
  paddingBottom: "31px",
  borderBottom: "1px dashed rgba(207, 207, 207, 1)",
}));

const VerifyInput = styled("div")(() => ({
  width: "100%",
  borderRadius: 28,
}));

const ButtonGroup = styled("div")(() => ({
  width: "100%",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  marginTop: 20,
}));

const Step1Card: React.FC<Props> = ({
  setStep,
  rowData,
  setStepData,
  step,
}) => {
  const [errorText, setErrorText] = useState<string>("");
  const [verifyCode, setVerifyCode] = useState<string>("");
  const [checkVerifyCode, setCheckVerifyCode] = useState<boolean>(false);
  const [countdown, setCountdown] = useState(0);
  const formRef = useRef<RefProps>(null);
  const [buttonText, setButtonText] = useState<string>("获取验证码");
  const [serialNum, setSerialNum] = useState<number>(0);
  const handleCodeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    if (!value) {
      setVerifyCode("");
      setErrorText("验证码不可为空");
      return;
    }
    setErrorText("");
    setVerifyCode(value);
  };

  const handleGetCode = async () => {
    const formRefValue: any = formRef.current?.values;
    const email = formRefValue?.email;
    if (!email) {
      setErrorText("请先输入邮箱号");
      return;
    }
    if (!emailRegex.test(email)) {
      setErrorText("请输入有效的邮箱号");
      return;
    }
    const params = {
      email,
      mustExist: true,
      mustUnique: false,
    };
    setErrorText("");
    try {
      const {
        data: { code, result },
      } = await getVerifyCode(params);
      if (code !== 200) {
        setErrorText("验证码发送失败");
        return;
      }
      setSerialNum(result.seq);
      setCheckVerifyCode(true);
      setCountdown(60);
    } catch (error) {
      window.console.log(error);
      setErrorText("验证码发送失败");
    }
  };

  const handleStepChange = () => {
    if (!verifyCode) {
      setErrorText("请输入验证码");
    }

    formRef.current?.submit().then(async (res: any) => {
      const formData = {
        ...rowData,
        ...res,
        code: verifyCode,
        seq: serialNum,
      };
      setStepData(formData);
      try {
        const {
          data: { code },
        } = await checkVerify(formData);
        if (code === 200) {
          setStep(1);
        } else {
          setErrorText("验证码错误");
        }
      } catch (error) {
        setErrorText("验证码错误");
        console.log(error);
        return;
      }
    });
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else if (countdown === 0 && checkVerifyCode) {
      setButtonText("重新获取验证码");
    }
    return () => clearTimeout(timer);
  }, [countdown, setCheckVerifyCode]);
  return (
    <Step1Box step={step}>
      <FormBox>
        <DynamicForm
          ref={formRef}
          columns={forgotStep1Columns}
          direction="column"
        />
        <FormControl
          fullWidth
          error={errorText ? true : false}
          sx={{
            flexDirection: "column",
            height: "100%",
          }}
        >
          <FormLabel
            component={"div"}
            required={true}
            title={"邮箱验证码"}
            sx={{
              textWrap: "nowrap",
              mr: 1,
              textAlign: "right",
              width: 70,
              overflow: "hidden",
              textOverflow: "ellipsis",
              color: "rgba(31, 31, 31, 1)",
              fontWeight: 400,
              fontSize: 14,
              mt: 0.5,
            }}
          >
            验证码:
          </FormLabel>
          <VerifyInput>
            <OutlinedInput
              fullWidth
              error={errorText ? true : false}
              type={"string"}
              endAdornment={
                <InputAdornment position="end">
                  <Button
                    size="small"
                    onClick={handleGetCode}
                    disabled={countdown ? true : false}
                    sx={{ color: "rgba(24, 112, 199, 1)" }}
                  >
                    {countdown ? `${countdown}秒后重新获取` : buttonText}
                  </Button>
                </InputAdornment>
              }
              placeholder="请输入邮箱验证码"
              value={verifyCode}
              onChange={handleCodeChange}
              sx={{ mt: 1.5, borderRadius: 28, height: 56 }}
            />
          </VerifyInput>
        </FormControl>
        {errorText && (
          <FormHelperText sx={{ mt: 0, ml: 2, color: "#d32f2f" }}>
            {errorText || " "}
          </FormHelperText>
        )}
      </FormBox>
      <ButtonGroup>
        <Button variant="contained" fullWidth onClick={handleStepChange}>
          下一步
        </Button>
      </ButtonGroup>
    </Step1Box>
  );
};
export default Step1Card;
