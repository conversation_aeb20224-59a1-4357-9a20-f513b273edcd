import { cloneDeep } from "lodash";
import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { v4 as uuidv4 } from "uuid";
import { useNavigate } from "react-router-dom";
import { setPaperNumber, setRelevances } from "@/store/search";
import { useAppDispatch } from "@/hooks";
const searchKey = "AI-SYSTEM-SEARCH";

export const searchTypeMap: Record<number, string> = {
  0: "正文",
  1: "关键字",
  2: "标题",
  3: "摘要",
  4: "作者",
  5: "期刊",
  // 6: "图注",
};
export type SearchTypeProps = keyof typeof searchTypeMap;
export const LogicMap: Record<number, string> = {
  0: "并且",
  1: "或者",
  2: "且非",
};
export type LogicType = keyof typeof LogicMap;

export interface SearchesProps {
  content: string;
  logic: LogicType;
  type: SearchTypeProps;
  id: string;
}

export interface SearchRecordProps {
  searches: Array<SearchesProps>;
  fromYear?: number | "";
  toYear?: number | "";
  category?: number;
  query: string;
}

interface StorageRecordProps {
  searchId: string;
  data: SearchRecordProps;
}

const useSearchRecord = () => {
  const { searchId } = useParams();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [init, setInit] = useState(true);
  const [searchRecord, setSearchRecord] = useState<SearchRecordProps>(() => {
    // 回显
    const cachedSearchList = localStorage.getItem(searchKey);
    if (cachedSearchList && searchId) {
      const recordList = JSON.parse(cachedSearchList) as StorageRecordProps[];
      const targetRecord = recordList?.find(
        (item) => item.searchId === searchId,
      );
      if (targetRecord) return targetRecord.data;
      return {
        query: "",
        searches: [],
      };
    }
    return {
      query: "",
      searches: [],
    };
  });

  useEffect(() => {
    if (init) return setInit(false);
    if (searchId) {
      const cachedSearchList = localStorage.getItem(searchKey);
      if (cachedSearchList) {
        const recordList = JSON.parse(cachedSearchList) as StorageRecordProps[];
        const targetRecord = recordList?.find(
          (item) => item.searchId === searchId,
        );
        if (targetRecord) setSearchRecord(targetRecord.data);
      }
    }
  }, [searchId]);

  const updateSearchRecord = (
    newSearchId: string,
    newRecord: SearchRecordProps,
  ) => {
    setSearchRecord(newRecord);
    // ！注意：序列化data会丢失undefined属性的键值
    const cachedSearchList = localStorage.getItem(searchKey);
    let recordList: StorageRecordProps[] = [];
    if (cachedSearchList) {
      // 控制检索记录99条
      recordList = JSON.parse(cachedSearchList).slice(
        -99,
      ) as StorageRecordProps[];
    }
    recordList.push({
      searchId: newSearchId,
      data: newRecord,
    });
    localStorage.setItem(searchKey, JSON.stringify(recordList));
  };

  const getHistoryKeys = () => {
    let historyWords: string[] = [];
    const cachedSearchList = localStorage.getItem(searchKey);
    if (cachedSearchList) {
      // 解析 JSON 字符串
      const parsedList = JSON.parse(cachedSearchList);
      // 提取 searches 中每一项的 content，过滤掉空值
      historyWords = parsedList.map((item: any) => {
        const firstContent = item.data.searches.find(
          (search: any) => search.content !== "",
        )?.content;
        if (item.data.query) {
          return item.data.query;
        } else if (firstContent) {
          return firstContent;
        }
      });
    }
    return Array.from(
      new Set(historyWords.filter((word) => word !== undefined)),
    );
  };

  // 校验检索内容是否为空
  const checkObj = (dataObj: SearchRecordProps) => {
    if (
      dataObj.fromYear ||
      dataObj.toYear ||
      dataObj.category ||
      dataObj.query
    ) {
      return false;
    } else {
      for (const item of dataObj.searches) {
        if (item.content && item.content.trim() !== "") {
          return false;
        }
      }
    }
    return true;
  };
  const onSearchClick = (
    record: SearchRecordProps,
    relevance?: number,
    number?: number,
  ) => {
    const newRecord = cloneDeep(record);
    const newSearchId = uuidv4();
    if (checkObj(newRecord)) return message.warning("请输入检索内容");
    if (typeof relevance === "number") {
      dispatch(setRelevances(relevance));
    }
    number && dispatch(setPaperNumber(number));
    navigate(`/paper-search/search-result/${newSearchId}`);
    updateSearchRecord(newSearchId, newRecord);
  };

  // 清空搜索记录
  const clearSearchRecord = () => {
    setSearchRecord({
      query: "",
      searches: [],
    });
    localStorage.removeItem(searchKey);
  };

  return { searchRecord, onSearchClick, clearSearchRecord, getHistoryKeys };
};

export default useSearchRecord;
