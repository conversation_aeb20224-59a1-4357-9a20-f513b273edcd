interface Props {
  img: string;
  message: string;
}
const EmptyDiv = styled("div")(() => ({
  height: "100%",
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  color: "#cfcfcf",
  fontSize: "14px",
}));
const Index: React.FC<Props> = ({ img, message }) => (
  <EmptyDiv>
    <img src={img} width={62} height={40} alt="" />
    <div>{message}</div>
  </EmptyDiv>
);
export default Index;
