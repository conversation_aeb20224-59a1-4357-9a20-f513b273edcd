import React from "react";
import {
  Box,
  Button,
  IconButton,
  MenuItem,
  OutlinedInput,
  Paper,
  Select,
  TextField,
  // Tooltip,
  styled,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { HEIGHT as ConditionHeight, SearchSelectWidth } from ".";
import RemoveCircleIcon from "@mui/icons-material/RemoveCircle";
import { anyValueProps } from "../../../../types/common";
// import SearchIcon from "@mui/icons-material/Search";
// import SearchClear from "@/assets/search-clear.svg";
import {
  LogicMap,
  SearchRecordProps,
  SearchesProps,
  searchTypeMap,
} from "@/hooks/useSearchRecord";
import SearchSelect from "../SearchSelect";
// import { RootState, useAppSelector } from "@/hooks";

const ITEM_PADDING_TOP = 8; // 下拉选上下padding
const ITEM_HEIGHT = 40; // 下拉选项高度

const AdvancedBox = styled(Paper)(() => ({
  width: "100%",
  position: "absolute",
  top: ConditionHeight + 10,
  zIndex: 10,
  borderRadius: 18,
  background: "rgba(240, 240, 240)",
  padding: "15px 20px 20px 10px",
  boxSizing: "border-box",
}));

const ListWrapper = styled("div")(() => ({
  maxHeight: 55 * 5,
  overflow: "auto",
}));
const ListItem = styled("div")(() => ({
  height: 45,
  marginBottom: 10,
}));

const StyledMenuItem = styled(MenuItem)(() => ({
  height: ITEM_HEIGHT,
  fontSize: 14,
}));

const StyledConditionInput = styled(TextField)(({ theme }) => ({
  flex: 1,
  backgroundColor: "white",
  border: 0,
  borderRadius: 28,
  "& .MuiInputBase-root": {
    height: "100%",
  },
  "& fieldset": {
    top: 0,
    height: "100%",
    lineHeight: 1,
    borderRadius: 28,
    border: 0,
    boxSizing: "border-box",
  },
  "& input": {
    padding: theme.spacing(1, 2),
    fontSize: 16,
  },
  "& input:-internal-autofill-selected": {
    borderRadius: 0,
  },
  "&:hover": {
    zIndex: 10,
  },
}));

const StyledCateTextField = styled(TextField)(() => ({
  width: SearchSelectWidth,
  height: "100%",
  borderRadius: 28,
  backgroundColor: "white",
  lineHeight: 1,
  border: 0,
  "& fieldset": {
    border: 0,
  },
  "& input": {
    paddingLeft: 30,
    fontSize: 16,
  },
  "& .Mui-disabled": {
    height: "100%",
    boxSizing: "border-box",
  },
}));
const PublishSelect = styled(Select)(({ theme }) => ({
  borderRadius: 28,
  background: theme.palette.secondary.main,
  "& fieldset": {
    border: 0,
  },
}));
const AddConditionButton = styled(Button)(({ theme }) => ({
  height: 36,
  borderRadius: 18,
  padding: "0 16px",
  color: theme.palette.primary.main,
}));

// const ClearButton = styled(Button)(() => ({
//   height: 32,
//   borderRadius: 28,
//   fontSize: 14,
// }));

// const SearchButton = styled(Button)(() => ({
//   height: 32,
//   borderRadius: 28,
//   fontSize: 14,
// }));

const NumberInputDiv = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  fontSize: 14,
  marginRight: "20px",
}));

const TextFieldStyle = styled(TextField)(() => ({
  padding: 0,
  marginLeft: "10px",
  background: "rgba(255,255,255)",
  width: 124,
  borderRadius: 18,

  ":hover": {
    background: "rgba(255, 255, 255, 0.04)",
    boxShadow:
      "0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)",
  },
  "& input[type=number]": {
    "-moz-appearance": "textfield",
  },
  "& input[type=number]::-webkit-outer-spin-button": {
    "-webkit-appearance": "none",
    margin: 0,
  },
  "& input[type=number]::-webkit-inner-spin-button": {
    "-webkit-appearance": "none",
    margin: 0,
  },
  "& .MuiInputBase-root": {
    height: "36px",
  },
  "& .MuiOutlinedInput-root": {
    "& fieldset": {
      border: "none", // 设置无边框
    },
    "&:hover fieldset": {
      border: "none", // 鼠标悬停时无边框
    },
    "&.Mui-focused fieldset": {
      border: "none", // 聚焦时无边框
    },
  },
  boxShadow:
    "0px 3px 1px -2px rgba(0,0,0,0.2),0px 2px 2px 0px rgba(0,0,0,0.14),0px 1px 5px 0px rgba(0,0,0,0.12)",
  ".css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input ": {
    boxSizing: "border-box",
  },
}));

interface AdvancedSearchProps {
  searchParam: SearchRecordProps;
  addCondition: (type: string) => void;
  changeCondition: (data: anyValueProps) => void;
  removeCondition: (type: string, id?: string) => void;
  // resetCondition: () => void;
  setIsSearch?: (value: boolean) => void;
  setRelevance: (value: number) => void;
  setNumber: (value: number) => void;
  relevance: number;
  number: number;
  setPageInfo?: (value: { page: number; pageSize: number }) => void;
}

interface IndexedNumberMap {
  [key: number]: string;
}
export type IndexedKeyProps = keyof IndexedNumberMap;

interface SearchConditionItemProps {
  removeCondition: () => void;
  searchItem: SearchesProps;
  changeCondition: (data: SearchesProps) => void;
}

interface CateConditionItemProps {
  category: number | string;
  changeCondition: (value: IndexedKeyProps) => void;
  removeCondition: () => void;
}
interface PublishTimeProps {
  fromYear?: number | "";
  toYear?: number | "";
}
interface PublishConditionProps extends PublishTimeProps {
  removeCondition: () => void;
  changeCondition: (data: PublishTimeProps) => void;
}

const cateTypeMap: IndexedNumberMap = {
  0: "科技",
  1: "计算机",
  2: "数学",
  3: "物理",
  4: "化学",
  5: "生物",
};

const publishTimeList: IndexedNumberMap = {
  1: "近一年",
  3: "近三年",
  5: "近五年",
};

const getYears = (year: number) => {
  const currentYear = new Date().getFullYear();
  const yearsList = [];
  for (let i = 0; i < year; i++) {
    yearsList.push(currentYear - i);
  }
  return yearsList;
};

const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 6 + ITEM_PADDING_TOP,
    },
  },
};

const DeleteButton: React.FC<{ onDelete: () => void }> = ({ onDelete }) => (
  <IconButton
    sx={{ ml: 1 }}
    color="error"
    aria-label="delete"
    onClick={onDelete}
  >
    <RemoveCircleIcon fontSize="inherit" />
  </IconButton>
);

// 子检索条件item
const SearchConditionItem: React.FC<SearchConditionItemProps> = ({
  removeCondition,
  changeCondition,
  searchItem,
}) => {
  const handleChange = (field: string, value: any) => {
    const newCondition = { ...searchItem, [field]: value };
    changeCondition(newCondition);
  };
  return (
    <Box
      sx={{
        display: "flex",
        width: "100%",
        height: 45,
        alignContent: "center",
      }}
    >
      {/* 且或非 */}
      <Box
        sx={{
          display: "flex",
          width: SearchSelectWidth,
          height: ConditionHeight,
          mr: "10px",
        }}
      >
        <SearchSelect
          width={100}
          value={searchItem.logic}
          sx={{ textAlign: "center" }}
          optionMap={LogicMap}
          onChange={(value) => handleChange("logic", value)}
        />
        <Box sx={{ flex: 1, m: "0 10px", height: ConditionHeight }}>
          <SearchSelect
            sx={{ textAlign: "center" }}
            value={searchItem.type}
            optionMap={searchTypeMap}
            onChange={(value) => handleChange("type", value)}
          />
        </Box>
      </Box>
      <StyledConditionInput
        value={searchItem.content}
        onChange={(e) => handleChange("content", e.target.value)}
        autoComplete="off"
        color="info"
      />
      <DeleteButton onDelete={removeCondition} />
    </Box>
  );
};
// 出版时间条件item
const PublishConditionItem: React.FC<PublishConditionProps> = ({
  fromYear,
  toYear,
  removeCondition,
  changeCondition,
}) => {
  const yearList = getYears(30);
  const handleTimeChange = (type: string, value: number) => {
    switch (type) {
      case "range":
        handleYearRange(value);
        break;
      case "start":
        changeCondition({ toYear, fromYear: value });
        break;
      case "end":
        changeCondition({ fromYear, toYear: value });
        break;
    }
  };
  const handleYearRange = (value: string | unknown) => {
    const toYear = new Date().getFullYear();
    const fromYear = toYear - Number(value);
    changeCondition({ fromYear, toYear });
  };

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        alignItems: "center",
        height: ConditionHeight,
      }}
    >
      {/* 出版日期 */}

      <PublishSelect
        sx={{ width: SearchSelectWidth, height: "100%", mr: "10px" }}
        displayEmpty
        value={""}
        onChange={(e) => handleTimeChange("range", Number(e.target.value))}
        input={<OutlinedInput />}
        renderValue={(selected) => {
          if (!selected) {
            return <span style={{ fontSize: 14 }}>出版日期</span>;
          }
          return selected + "";
        }}
        MenuProps={MenuProps}
        inputProps={{ "aria-label": "Without label" }}
      >
        {Object.keys(publishTimeList).map((key) => (
          <StyledMenuItem key={key} value={key}>
            {publishTimeList[Number(key)]}
          </StyledMenuItem>
        ))}
      </PublishSelect>
      <Box
        sx={{ display: "flex", alignItems: "center", flex: 1, height: "100%" }}
      >
        <SearchSelect
          sx={{ pl: "30px" }}
          value={fromYear || ""}
          options={yearList}
          onChange={(value) => handleTimeChange("start", value)}
        />
        <span style={{ margin: "0 10px" }}>至</span>
        <SearchSelect
          sx={{ pl: "30px" }}
          value={toYear || ""}
          options={yearList}
          onChange={(value) => handleTimeChange("end", value)}
        />
      </Box>
      <DeleteButton onDelete={removeCondition} />
    </Box>
  );
};

const CateConditionItem: React.FC<CateConditionItemProps> = ({
  category,
  changeCondition,
  removeCondition,
}) => (
  <Box
    sx={{
      width: "100%",
      height: ConditionHeight,
      display: "flex",
      alignItems: "center",
    }}
  >
    <StyledCateTextField defaultValue={"类别"} disabled variant="outlined" />
    <Box sx={{ flex: 1, height: "100%", ml: 1 }}>
      <SearchSelect
        sx={{ pl: "30px" }}
        value={category}
        optionMap={cateTypeMap}
        onChange={(value) => changeCondition(value)}
      />
    </Box>
    <DeleteButton onDelete={removeCondition} />
  </Box>
);
const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  searchParam,
  addCondition,
  changeCondition,
  removeCondition,
  setRelevance,
  setNumber,
  relevance,
  number,
  // resetCondition,
  // setIsSearch,
  // setPageInfo,
}) => {
  const showPublish = useMemo(
    () => "fromYear" in searchParam || "toYear" in searchParam,
    [searchParam],
  );
  const len =
    "fromYear" in searchParam || "toYear" in searchParam
      ? searchParam.searches.length + 1
      : searchParam.searches.length;
  // const handleSearch = () => {
  //   onSearchClick(searchParam, relevance, number);
  //   setIsSearch && setIsSearch(true);
  //   setPageInfo && setPageInfo({ page: 1, pageSize: 10 });
  // };

  const handleChange = (e: any, type: string) => {
    const value = e.target.value;
    if (type === "relevance") {
      if (value === "") {
        setRelevance(value);
      } else if (/^0\.[0-9]{1}$|^0{1}$|^1{1}$/.test(value)) {
        setRelevance(Number(value));
      }
    } else if (type === "number") {
      if (value === "") {
        setNumber(value);
      } else if (/^(100|[1-9]\d?)$/.test(value)) {
        setNumber(Number(value));
      }
    }
  };
  return (
    <AdvancedBox elevation={3}>
      <ListWrapper>
        {searchParam.searches.length > 0 &&
          searchParam.searches.map((item: SearchesProps) => (
            <ListItem key={item.id}>
              <SearchConditionItem
                searchItem={item}
                changeCondition={changeCondition}
                removeCondition={() => removeCondition("search", item.id)}
              />
            </ListItem>
          ))}
        {showPublish && (
          <ListItem>
            <PublishConditionItem
              fromYear={searchParam.fromYear}
              toYear={searchParam.toYear}
              changeCondition={changeCondition}
              removeCondition={() => removeCondition("publishTime")}
            />
          </ListItem>
        )}
        {"category" in searchParam && (
          <ListItem>
            <CateConditionItem
              category={searchParam.category || ""}
              changeCondition={(value: IndexedKeyProps) =>
                changeCondition({ category: value })
              }
              removeCondition={() => removeCondition("category")}
            />
          </ListItem>
        )}
      </ListWrapper>

      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          width: "100%",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-start",
            alignItems: "center",
            height: "36px",
            gap: "10px",
          }}
        >
          <AddConditionButton
            variant="contained"
            color="secondary"
            onClick={() => addCondition("search")}
            disabled={len < 7 ? false : true}
          >
            <AddIcon />
            添加检索条件
          </AddConditionButton>
          {!showPublish && (
            <AddConditionButton
              variant="contained"
              color="secondary"
              onClick={() => addCondition("publishTime")}
              disabled={len < 7 ? false : true}
            >
              <AddIcon />
              添加出版时间
            </AddConditionButton>
          )}

          {/* {!("category" in searchParam) && (
            <AddConditionButton
              variant="contained"
              color="secondary"
              onClick={() => addCondition("category")}
            >
              <AddIcon />
              添加类别
            </AddConditionButton>
          )} */}
        </Box>
        <Box style={{ display: "flex", alignItems: "center" }}>
          <NumberInputDiv>
            检索最低相关度
            <TextFieldStyle
              autoComplete="off"
              value={relevance}
              onChange={(e) => handleChange(e, "relevance")}
              placeholder="输入范围0-1"
              type="number"
              onKeyDown={(e) => {
                // 键盘事件拦截科学计数法
                if (["e", "E", "+", "-"].includes(e.key)) {
                  e.preventDefault();
                }
              }}
              slotProps={{
                htmlInput: {
                  inputMode: "decimal",
                  min: 0,
                  max: 1,
                  step: 0.1,
                },
              }}
            />
          </NumberInputDiv>
          <NumberInputDiv>
            检索最大返回数(条)
            <TextFieldStyle
              autoComplete="off"
              placeholder="输入范围1-100"
              value={number}
              onChange={(e) => handleChange(e, "number")}
            />
          </NumberInputDiv>
        </Box>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          {/* <ClearButton
            variant="contained"
            color="secondary"
            onClick={resetCondition}
          >
            清除
          </ClearButton> */}
          {/* <Tooltip title="清除" arrow placement="top">
            <ImgStyle
              src={SearchClear}
              alt=""
              onClick={resetCondition}
            ></ImgStyle>
          </Tooltip> */}
          {/* <SearchButton
            sx={{ ml: "10px" }}
            variant="contained"
            onClick={handleSearch}
          >
            检索
          </SearchButton> */}
          {/* <Tooltip title="检索" arrow placement="top">
            <SearchIcon
              color="primary"
              sx={{ cursor: "pointer", margin: "0 10px" }}
              onClick={handleSearch}
            />
          </Tooltip> */}
        </Box>
      </Box>
    </AdvancedBox>
  );
};

export default AdvancedSearch;
