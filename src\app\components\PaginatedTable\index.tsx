// 分页表格
import React from "react";
import { PaginationProps, styled } from "@mui/material";
import CusTomTable, { ColumnProps } from "../CustomTable";
import { PageProps } from "../MyPaperPagination";
import { anyValueProps } from "@/types/common";
import Pagination from "../Pagination";
import { useCheckPermission } from "@/utils/tableCheck";
const Root = styled("div")(() => ({
  display: "flex",
  width: "100%",
  height: "100%",
  flexDirection: "column",
}));
const TableWrapper = styled("div", {
  shouldForwardProp: (prop) => prop !== "isDialog",
})<{ isDialog?: boolean | undefined }>(({ isDialog }) => ({
  flex: "1",
  overflow: "hidden",
  borderBottom: !isDialog ? "1px dashed rgba(207, 207, 207, 1)" : "",
  paddingBottom: !isDialog ? 13 : 0,
  marginBottom: 13,
}));

const TableFooter = styled("div", {
  shouldForwardProp: (prop) => prop !== "buttonGroup",
})<{ buttonGroup: any }>(({ buttonGroup }) => ({
  display: "flex",
  justifyContent: buttonGroup ? "space-between" : "flex-end",
  alignItems: "center",
}));

const ButtonBox = styled("div")<{ buttonLength: number }>(
  ({ buttonLength }) => ({
    width: "100%",
    height: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: buttonLength === 1 ? "center" : "space-between",
  }),
);

const StyleButton = styled("div")(() => ({
  color: "rgba(24, 112, 199, 1)",
  cursor: "pointer",
}));

export interface ActionButtonProps {
  type: string;
  role: string;
}

interface PaginatedTableProps extends PaginationProps {
  total: number;
  page: number;
  pageSize: number;
  rows: anyValueProps[];
  headCells: ColumnProps[];
  pageSizeOptions?: number[];
  actionButtons?: ActionButtonProps[];
  selection?: boolean;
  isDialog?: boolean;
  actionClick?: (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    action: string,
    row: anyValueProps,
  ) => void;
  onChangePage: (pageParams: PageProps) => void;
  onSortChange: (sort: string, asc: boolean) => void;
  buttonGroup?: React.ReactNode;
  setCheckedList?: (value: any) => void;
}
const PaginatedTable: React.FC<PaginatedTableProps> = (props) => {
  const {
    total,
    page,
    pageSize,
    rows,
    headCells,
    pageSizeOptions = [10, 20, 50],
    actionButtons,
    actionClick,
    selection = false,
    onChangePage,
    onSortChange,
    buttonGroup,
    setCheckedList,
    ...others // 可自定义传入分页其他信息
  } = props;
  // const { buttonPermMenus } = useAppSelector((state) => state.route);
  const filterActionButtons = useCheckPermission(actionButtons || []);
  const tableColumns = useMemo(() => {
    if (actionButtons && actionButtons.length > 0) {
      return headCells.concat({
        dataKey: "action",
        label: "操作",
        width: 40 * filterActionButtons.length,
        align: "center",
        render: (row: anyValueProps) => (
          <ButtonBox buttonLength={filterActionButtons.length}>
            {filterActionButtons?.map((action) => (
              <StyleButton
                key={action.type}
                onClick={(e: any) => onAction(e, action.type, row)}
              >
                {action.type}
              </StyleButton>
            ))}
          </ButtonBox>
        ),
      });
    }
    return headCells;
  }, [headCells, actionButtons]);
  const onAction = (
    event: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    action: string,
    row: anyValueProps,
  ) => {
    event.stopPropagation();
    actionClick && actionClick(event, action, row);
  };
  return (
    <Root>
      <TableWrapper>
        <CusTomTable
          rows={rows}
          page={page}
          columns={tableColumns}
          selection={selection}
          onSortChange={onSortChange}
          setSelectList={setCheckedList}
        />
      </TableWrapper>
      <TableFooter buttonGroup={buttonGroup}>
        {total > 0 && buttonGroup}
        {total > 0 && (
          <Pagination
            total={total}
            page={page}
            pageSize={pageSize}
            onChangePage={onChangePage}
            pageSizeOptions={pageSizeOptions}
            {...others}
          />
        )}
      </TableFooter>
    </Root>
  );
};

export default PaginatedTable;
