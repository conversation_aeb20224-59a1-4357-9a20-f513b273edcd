#!/usr/bin/env node

/**
 * PDF Worker 修复脚本
 * 自动检测和修复PDF Worker相关问题
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

const PUBLIC_DIR = path.join(process.cwd(), 'public');
const WORKER_FILE = path.join(PUBLIC_DIR, 'pdf.worker.min.js');
const CMAPS_DIR = path.join(PUBLIC_DIR, 'cmaps');

console.log('🔧 PDF Worker 修复脚本启动...\n');

// 检查文件是否存在
function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

// 获取package.json中的pdfjs版本
function getPdfjsVersion() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const pdfjsVersion = packageJson.dependencies['pdfjs-dist'] || 
                        packageJson.devDependencies['pdfjs-dist'];
    
    if (pdfjsVersion) {
      // 移除版本号前缀（如^, ~）
      return pdfjsVersion.replace(/^[\^~]/, '');
    }
  } catch (error) {
    console.warn('⚠️  无法读取package.json，使用默认版本');
  }
  
  return '3.11.174'; // 默认版本
}

// 下载文件
function downloadFile(url, destination) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(destination);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`下载失败: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve();
      });
      
      file.on('error', (error) => {
        fs.unlink(destination, () => {}); // 删除部分下载的文件
        reject(error);
      });
    }).on('error', reject);
  });
}

// 从node_modules复制文件
function copyFromNodeModules() {
  const nodeModulesWorker = path.join(
    process.cwd(), 
    'node_modules', 
    'pdfjs-dist', 
    'build', 
    'pdf.worker.min.js'
  );
  
  if (checkFileExists(nodeModulesWorker)) {
    try {
      fs.copyFileSync(nodeModulesWorker, WORKER_FILE);
      console.log('✅ 从node_modules复制Worker文件成功');
      return true;
    } catch (error) {
      console.error('❌ 从node_modules复制失败:', error.message);
      return false;
    }
  }
  
  return false;
}

// 从CDN下载Worker文件
async function downloadFromCDN() {
  const version = getPdfjsVersion();
  const url = `https://unpkg.com/pdfjs-dist@${version}/build/pdf.worker.min.js`;
  
  console.log(`📥 从CDN下载Worker文件 (版本: ${version})...`);
  
  try {
    await downloadFile(url, WORKER_FILE);
    console.log('✅ 从CDN下载Worker文件成功');
    return true;
  } catch (error) {
    console.error('❌ 从CDN下载失败:', error.message);
    return false;
  }
}

// 检查和修复cmaps目录
function checkCmaps() {
  if (!checkFileExists(CMAPS_DIR)) {
    console.log('⚠️  cmaps目录不存在');
    
    const nodeModulesCmaps = path.join(
      process.cwd(),
      'node_modules',
      'pdfjs-dist',
      'cmaps'
    );
    
    if (checkFileExists(nodeModulesCmaps)) {
      try {
        // 递归复制cmaps目录
        fs.cpSync(nodeModulesCmaps, CMAPS_DIR, { recursive: true });
        console.log('✅ 从node_modules复制cmaps目录成功');
        return true;
      } catch (error) {
        console.error('❌ 复制cmaps目录失败:', error.message);
        return false;
      }
    } else {
      console.log('⚠️  node_modules中也没有找到cmaps目录');
      return false;
    }
  } else {
    console.log('✅ cmaps目录已存在');
    return true;
  }
}

// 验证Worker文件
function validateWorkerFile() {
  if (!checkFileExists(WORKER_FILE)) {
    return false;
  }
  
  try {
    const stats = fs.statSync(WORKER_FILE);
    const fileSizeKB = Math.round(stats.size / 1024);
    
    console.log(`📊 Worker文件大小: ${fileSizeKB}KB`);
    
    // PDF Worker文件通常应该大于100KB
    if (stats.size < 100 * 1024) {
      console.log('⚠️  Worker文件可能不完整（文件过小）');
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('❌ 验证Worker文件失败:', error.message);
    return false;
  }
}

// 生成配置建议
function generateConfigSuggestions() {
  console.log('\n📋 配置建议:');
  console.log('');
  
  console.log('1. 如果应用有子路径，请在public/config.js中设置:');
  console.log('   window.APP_CONFIG = { BASE_PATH: "/your-sub-path" };');
  console.log('');
  
  console.log('2. 对于Next.js项目，在next.config.js中设置:');
  console.log('   module.exports = {');
  console.log('     basePath: "/your-sub-path",');
  console.log('     assetPrefix: "/your-sub-path"');
  console.log('   };');
  console.log('');
  
  console.log('3. 确保服务器正确配置静态文件服务');
  console.log('');
}

// 主函数
async function main() {
  // 检查public目录
  if (!checkFileExists(PUBLIC_DIR)) {
    console.error('❌ public目录不存在，请确认在正确的项目根目录运行此脚本');
    process.exit(1);
  }
  
  console.log('📁 检查文件状态...');
  
  // 检查Worker文件
  let workerExists = checkFileExists(WORKER_FILE);
  console.log(`Worker文件: ${workerExists ? '✅ 存在' : '❌ 不存在'}`);
  
  // 检查cmaps
  let cmapsExists = checkFileExists(CMAPS_DIR);
  console.log(`cmaps目录: ${cmapsExists ? '✅ 存在' : '❌ 不存在'}`);
  
  console.log('');
  
  // 修复Worker文件
  if (!workerExists || !validateWorkerFile()) {
    console.log('🔧 修复Worker文件...');
    
    // 尝试从node_modules复制
    let success = copyFromNodeModules();
    
    // 如果失败，尝试从CDN下载
    if (!success) {
      success = await downloadFromCDN();
    }
    
    if (!success) {
      console.error('❌ 无法修复Worker文件，请手动下载');
      console.log('手动下载地址: https://unpkg.com/pdfjs-dist@latest/build/pdf.worker.min.js');
    }
  } else {
    console.log('✅ Worker文件正常');
  }
  
  // 修复cmaps
  if (!cmapsExists) {
    console.log('🔧 修复cmaps目录...');
    checkCmaps();
  } else {
    console.log('✅ cmaps目录正常');
  }
  
  // 最终验证
  console.log('\n🔍 最终验证...');
  const finalWorkerExists = checkFileExists(WORKER_FILE) && validateWorkerFile();
  const finalCmapsExists = checkFileExists(CMAPS_DIR);
  
  console.log(`Worker文件: ${finalWorkerExists ? '✅ 正常' : '❌ 异常'}`);
  console.log(`cmaps目录: ${finalCmapsExists ? '✅ 正常' : '❌ 异常'}`);
  
  if (finalWorkerExists && finalCmapsExists) {
    console.log('\n🎉 所有文件修复完成！');
  } else {
    console.log('\n⚠️  部分文件仍有问题，请查看上述错误信息');
  }
  
  generateConfigSuggestions();
}

// 运行脚本
main().catch(error => {
  console.error('❌ 脚本执行失败:', error.message);
  process.exit(1);
});
