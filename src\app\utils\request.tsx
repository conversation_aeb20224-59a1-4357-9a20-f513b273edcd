import axios from "axios";
import React, { useEffect } from "react";
import { maxTimeout } from "../api/config";
import { useAppSelector } from "@/hooks";
import { getRoleInfo, getToken, navigateToLogin, removeToken } from "./auth";

const AxiosInterceptors: React.FC = () => {
  const userInfo = useAppSelector((state) => state.user.userInfo);
  useEffect(() => {
    axios.interceptors.request.use(
      (config) => {
        const token = getToken();
        // const localStorageRoleOption = localStorage.getItem("roleInfo");
        const roleInfoStorage = getRoleInfo();
        const userId = userInfo?.id;
        if (token) {
          config.headers["Authorization"] = `Bearer ${token}`;
        } else {
          Reflect.deleteProperty(config.headers, "Authorization");
        }
        if (userId) {
          config.headers["Optional-Info"] = userId;
        } else {
          Reflect.deleteProperty(config.headers, "Optional-Info");
        }

        if (roleInfoStorage) {
          const roleOption = roleInfoStorage;
          config.headers["X-Group"] = roleOption.resourceCode;
        } else {
          Reflect.deleteProperty(config.headers, "X-Group");
        }

        if (!config.timeout) {
          config.timeout = maxTimeout;
        }
        return config;
      },
      (error) => Promise.reject(error),
    );

    axios.interceptors.response.use(
      (response) => {
        const { code } = response.data;
        if (code) {
          if (code !== 200) {
            // message.error(response.data.msg);
            if (code === 401) {
              // 删除token并刷新页面
              removeToken();
              setTimeout(() => {
                location.reload();
              }, 100);
            }
            return Promise.reject(response.data);
          }
        }
        return Promise.resolve(response);
      },
      (error) => {
        if (error.response && error.response.status) {
          if (error.response.status === 401) {
            const originUrl = window.location.origin + "/api/auth/login";
            if (originUrl !== error.request.responseURL) {
              removeToken();
              navigateToLogin();
              return;
            }
          }
          if (error.response.status !== 200) {
            const originUrl =
              window.location.origin + "/api/public/checkVerificationCode";
            if (
              originUrl !== error.request.responseURL &&
              (error.response.data.msg || error.response.data.message)
            )
              message.error(
                error.response.data.msg || error.response.data.message,
              );
          }
        }
        return Promise.reject(error);
      },
    );
  }, []);

  return <></>;
};

export default AxiosInterceptors;
