import React, { useEffect, useState } from "react";
import { styled, Box } from "@mui/material";
import PopConfirm from "@/components/Popover";
import { Grid2 as Grid } from "@mui/material";
import { getFile } from "@/api/paperSearch";
import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { searchColumns } from "./common";
import {
  deletePdf,
  getPersonalPaperList,
  queryPdfList,
} from "@/api/personalpaper";
import { anyValueProps } from "@/types/common";
import { useNavigate, useParams } from "react-router-dom";
import Breadcrumb from "@/components/Breadcrumb";
import { columns } from "./common";
import PaginatedTable from "@/components/PaginatedTable";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "react-router-dom";
// import FileUploadIcon from "@mui/icons-material/FileUpload";
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import { buttonGroup } from "../components/common";
const Root = styled("div")(() => ({
  width: "100%",
  height: "100%",
  boxSizing: "border-box",
  display: "flex",
  flexDirection: "column",
}));

const RootContent = styled("div")(() => ({
  width: "100%",
  padding: "18px 41px 0 41px",
  boxSizing: "border-box",
  flex: 1,
  display: "flex",
  flexDirection: "column",
  height: 0,
}));
const OperationBar = styled(Box)(() => ({
  width: "100%",
  height: 64,
  boxSizing: "border-box",
  display: "flex",
  alignItems: "center",
}));

const KnowledgeHeader = styled("div")(() => ({
  width: "100%",
  background: "rgba(255, 255, 255, 1)",
  height: "100%",
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  borderRadius: "16px",
  opacity: 1,
}));

const TableBar = styled("div")(() => ({
  display: "flex",
  flex: 1,
  background:
    "radial-gradient(46.19% 59.91% at 17.11229946524064% -40.09433962264151%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(252, 252, 252, 1)",
  borderRadius: 20,
  marginTop: "20px",
  padding: "10px",
  boxSizing: "border-box",
  // boxShadow:
  //   "0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12)",
}));

const ButtonStyle = styled(Button, {
  shouldForwardProp: (props) => props !== "isColor",
})<{ isColor: boolean }>(({ isColor }) => ({
  width: "80px",
  borderRadius: "18px",
  background: isColor
    ? " linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)"
    : "rgba(242, 242, 242, 1)",
  color: isColor ? "#fff" : "rgba(31, 31, 31, 1)",
}));

const BackBox = styled("div")(() => ({
  width: 86,
  height: "100%",
  borderRadius: 14,
  background:
    "radial-gradient(70.93% 70.31% at 17.441860465116278% -40.625%, rgba(41, 66, 227, 0.63) 0%, rgba(255, 255, 255, 0) 100%), rgba(242, 243, 247, 1)",
  border: "2px solid rgba(255, 255, 255, 1)",
  marginRight: 17,
  boxSizing: "border-box",
  fontSize: 14,
  color: "#000",
  fontWeight: 400,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  cursor: "pointer",
}));
const BackIcon = styled("div")(() => ({
  width: 16,
  height: 16,
  borderRadius: "50%",
  background: "rgba(255, 255, 255, 0.88)",
  marginRight: 8,
  boxSizing: "border-box",
}));
interface PaginationProps {
  page: number;
  size: number;
}
export type SearchParamProp = anyValueProps | null;
interface SortInfoProps {
  sort: string;
  asc: boolean;
}
const PersonalPaper: React.FC = () => {
  const navigator = useNavigate();
  const location = useLocation();
  const { taskId } = useParams();
  const formRef = useRef<RefProps>(null);
  const [pagination, setPagination] = useState<PaginationProps>({
    page: 1,
    size: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [tableData, setTableData] = useState<anyValueProps[]>([]);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  const [drawerTitle, setDrawerTitle] = useState<string>("");
  const [pdfUrl, setPdfUrl] = useState<string>("");
  const [deletePdfId, setDeletePdfId] = useState<number | string>(0);
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false);
  const [rowOption, setRowOption] = useState<anyValueProps>();
  const [searchParam, setSearchParam] = useState<SearchParamProp>({
    searchValue: "",
    source: "",
    category: "2",
  });
  const [searchParams, setSearchParams] = useState<SearchParamProp>({});
  const [sortInfo, setSortInfo] = useState<SortInfoProps>({
    sort: "", // 排序字段
    asc: true, // 正序 | 反序
  });

  const { kb_name, documentId } = location.state || {};
  const queryRequest = async () => {
    const httpRequest = !documentId ? queryPdfList : getPersonalPaperList;
    const response = await httpRequest({
      ...pagination,
      ...sortInfo,
      ...searchParam,
      ...(documentId && { documentId }),
    });
    return response.data;
  };

  const { data, status, refetch, error } = useQuery({
    queryKey: ["get-person", pagination, sortInfo, searchParam], // 使用唯一键，将参数作为查询键的一部分
    queryFn: queryRequest,
  });

  useEffect(() => {
    window.console.log(
      drawerOpen,
      drawerTitle,
      pdfUrl,
      editDialogOpen,
      rowOption,
      setSearchParam({}),
    );

    switch (status) {
      case "success":
        setTableData(data?.data || []);
        setTotal(data?.total || 0);
        break;
      case "error":
        message.error("获取资料列表失败, " + error.message);
        break;
    }
  }, [status, data]);

  const onPageChange = (paginationParams: anyValueProps) => {
    const { page, pageSize: size } = paginationParams;
    setPagination({ page, size });
  };

  const handleAction = (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    key: string,
    row: anyValueProps,
  ) => {
    switch (key) {
      case "查看":
        navigator(`/knowledge-base/data-details`, {
          state: { ...location.state, ...row },
        });
        break;
      case "预览":
        previewPdf(row);
        break;
      case "编辑":
        setEditDialogOpen(true);
        setRowOption(formatData(row));
        break;
      case "删除":
        setAnchorEl(e.currentTarget);
        setDeletePdfId(row.id);
        break;
    }
  };

  const formatData = (data: anyValueProps) => {
    const formattedData: anyValueProps = {};
    for (const key in data) {
      if (data[key] === null) {
        formattedData[key] = "";
      } else if (key === "keywords" || key === "authors") {
        try {
          formattedData[key] = JSON.parse(data[key]).join(", ");
        } catch (e) {
          formattedData[key] = data[key];
          window.console.log(e);
        }
      } else {
        formattedData[key] = data[key];
      }
    }
    return formattedData;
  };

  // 预览pdf
  const previewPdf = async (rowData: anyValueProps) => {
    try {
      const { pdfUrl, id } = rowData;
      setDrawerOpen(true);
      const { data } = await getFile(pdfUrl + `&pdfId=${id}`);
      const pdfData = new Blob([data], { type: "application/pdf" });
      const pdfDownloadUrl = window.URL.createObjectURL(pdfData);
      setPdfUrl(pdfDownloadUrl);
      setDrawerTitle(rowData.title);
    } catch (e) {
      message.error("获取PDF失败" + e);
    }
  };

  const handleConfirm = async () => {
    setAnchorEl(null);
    try {
      const payload = taskId
        ? { pdfIds: [deletePdfId], externalId: taskId, type: "upload" }
        : { pdfIds: [deletePdfId] };

      const { data } = await deletePdf(payload);

      if (data.code === 200) {
        message.success("删除成功");
        refetch?.();
      } else {
        message.error("删除失败");
      }
    } catch (e: any) {
      message.error("删除失败: " + e.message);
    }
  };

  const onSortChange = (
    sort: SortInfoProps["sort"],
    asc: SortInfoProps["asc"],
  ) => {
    setSortInfo({ sort, asc });
  };

  // from对象集合
  const onChanges = (key: string, value: any) => {
    const newSearchParam = { ...searchParams, [key]: value };
    setSearchParams(newSearchParam);
  };
  // 重置
  const handleReset = () => {
    setSearchParams({});
    // setSearchValues([]);
    // setPageInfo((prev) => ({ ...prev, offset: 0 }));
  };
  // 查询
  const handleQuery = () => {
    window.console.log(searchParams);
    // window.console.log(id);
    // const newArr = [];
    // for (const key in searchParam) {
    //   if (key === "range_value" && range_value.length) {
    //     const start = range_value[0] + " 00:00:00";
    //     const end = range_value[1] + " 23:59:59";
    //     searchParam[key] &&
    //       newArr.push({
    //         name: "update_dt",
    //         range_value: [start, end],
    //         operator: "between",
    //       });
    //   } else if (key === "kb_name" && kb_name) {
    //     searchParam[key] &&
    //       newArr.push({
    //         name: key,
    //         value: kb_name,
    //         operator: "like",
    //       });
    //   }
    // }
  };
  return (
    <Root>
      <Breadcrumb
        parent={[
          {
            name: "实验数据库",
            path: "/knowledge-base",
          },
        ]}
        current={`${kb_name}实验数据库`}
      />
      <RootContent>
        <OperationBar>
          <BackBox>
            <BackIcon>
              <ArrowBackIosNewIcon sx={{ fontSize: 14 }} />
            </BackIcon>
            返回
          </BackBox>
          <KnowledgeHeader>
            {/* from对象集合 */}
            <div style={{ width: "85%", height: "40px" }}>
              <DynamicForm
                ref={formRef}
                columns={searchColumns}
                formData={searchParams}
                onChange={onChanges}
                size="small"
              />
            </div>
            {/* 操作 */}
            <Grid size={3} sx={{ mr: 3 }}>
              <ButtonStyle sx={{ mr: 3 }} onClick={handleReset} isColor={false}>
                重置
              </ButtonStyle>
              <ButtonStyle onClick={handleQuery} isColor={true}>
                查询
              </ButtonStyle>
            </Grid>
          </KnowledgeHeader>
        </OperationBar>
        <TableBar>
          <PaginatedTable
            total={total}
            page={pagination.page}
            pageSize={pagination.size}
            onChangePage={onPageChange}
            onSortChange={onSortChange}
            rows={tableData}
            headCells={columns}
            selection={true}
            actionButtons={["查看", "编辑", "删除", "下载"]}
            actionClick={handleAction}
            buttonGroup={tableData.length ? buttonGroup : []}
          />
        </TableBar>
      </RootContent>
      {!!anchorEl && (
        <PopConfirm
          title="确定删除该篇资料吗?"
          anchorEl={anchorEl}
          handleClose={() => setAnchorEl(null)}
          handleConfirm={handleConfirm}
        />
      )}
      {/* <EditDialog
        open={editDialogOpen}
        setOpen={setEditDialogOpen}
        reload={refetch}
        rowOption={rowOption}
      /> */}
    </Root>
  );
};

export default PersonalPaper;
