import React from "react";
import { SnackbarProvider } from "notistack";
import { SnackbarUtilsConfigurator } from "./message";
import AxiosInterceptors from "@/utils/request";
const MessageBox: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <SnackbarProvider
    maxSnack={3}
    autoHideDuration={2000}
    anchorOrigin={{
      vertical: "top",
      horizontal: "center",
    }}
  >
    <SnackbarUtilsConfigurator />
    <AxiosInterceptors />
    {children}
  </SnackbarProvider>
);

export default MessageBox;
