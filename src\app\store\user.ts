import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { getUserInfomation, login as loginApi, logoutUser } from "@/api/login";
import {
  getRoleInfo,
  navigateToLogin,
  removeToken,
  setToken,
  removeRoleInfo,
} from "@/utils/auth";
import { global } from "@/utils/constants";

export type RoleType =
  | "ADMIN"
  | "GROUP_ADMIN"
  | "GROUP_MEMBER"
  | "TEMP_VISITOR";

interface groupOptionProps {
  label: string;
  value: string;
  roleName?: string;
}

interface InitialStateProps {
  userInfo: {
    name: string;
    email: string;
    id: string;
    groupList: any[];
    systemRole: any;
    token?: string;
    phone?: string;
  };
  groupOption: groupOptionProps[];
  roleOption: {
    roleCode: RoleType;
    roleId: string;
    resourceCode?: string;
    groupId?: string;
    roleName: string;
  };
}
const initialState: InitialStateProps = {
  userInfo: {
    name: "",
    email: "",
    id: "",
    groupList: [],
    systemRole: {},
    phone: "",
  },
  groupOption: [], // 课题组
  roleOption: {
    roleCode: "TEMP_VISITOR",
    roleId: "",
    resourceCode: "",
    groupId: "",
    roleName: "",
  },
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getInfoByToken.fulfilled, loginSuccess)
      .addCase(getInfoByToken.rejected, Logout);
    builder.addCase(login.fulfilled, loginSuccess);
    builder.addCase(logout.fulfilled, Logout);
  },
});

const loginSuccess = (state: any, { payload }: any) => {
  state.userInfo = payload;
  if (payload.token) {
    setToken(payload.token, payload.keepLoggedIn);
    global.token = payload.token;
  }
  // 判断是否是管理员 | 课题组管理员
  const selectRoleInfo = getRoleInfo();
  state.roleOption = selectRoleInfo;
  state.groupOption =
    payload.groupList?.map((item: any) => ({
      label: item.groupName,
      value: item.resourceCode,
      ...(selectRoleInfo.roleCode !== "ADMIN"
        ? { roleName: item.groupRole.roleName }
        : {}),
    })) || [];
};

const Logout = (state: any) => {
  state.userInfo = initialState.userInfo;
  state.groupOption = [];
  state.roleOption = initialState.roleOption;
  removeToken();
  removeRoleInfo();
  setTimeout(() => {
    navigateToLogin();
    localStorage.clear();
  }, 100);
};

export const login = createAsyncThunk(
  "user/login",
  async (params: any, { rejectWithValue }) => {
    try {
      const res = await loginApi(params);
      return { ...res.data.result, keepLoggedIn: params.keepLoggedIn };
    } catch (error) {
      return rejectWithValue("登录失败" + error);
    }
  },
);

export const logout = createAsyncThunk(
  "user/logout",
  async (_, { rejectWithValue }) => {
    try {
      const res = await logoutUser();
      return res.data;
    } catch (error) {
      return rejectWithValue("退出登录失败" + error);
    }
  },
);

export const getInfoByToken = createAsyncThunk(
  "user/getInfoByToken",
  async (_, { rejectWithValue }) => {
    try {
      const res = await getUserInfomation();
      return res.data.result;
    } catch (error) {
      return rejectWithValue("获取用户信息失败" + error);
    }
  },
);

export default userSlice.reducer;
