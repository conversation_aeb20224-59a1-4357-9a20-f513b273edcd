import { anyValueProps } from "@/types/common";

export interface Props {
  text: string | any;
  inversion: boolean;
  dateTime: string;
  handleDelete?: () => void;
  onGetBoxHeight?: () => void;
  setDrawerOpen?: (value: boolean) => void;
  setPdfUrl?: (value: string) => void;
  setHistoryData?: (value: any) => void;
  setPaperData?: (value: any) => void;
  setExpandedIndex: (value: any) => void;
  setCurrentPaperItemId?: (value: number) => void;
  expandedIndex: number | null;
  thinkText?: string;
  additionalInfo?: anyValueProps[];
  loading?: boolean;
  isLocalStorage?: boolean;
  currentLoading?: boolean;
  schemeLoading?: boolean;
  note?: string;
  progressText?: string;
  itemIndex: number;
  stopMsg?: string;
  count?: number;
  templateId: string;
  aiChemUrl: string;
}

export interface textProps {
  inversion: boolean;
  text: string;
  additionalInfo?: anyValueProps[];
  setDrawerOpen?: (value: boolean) => void;
  setPdfUrl?: (value: string) => void;
  setHistoryData?: (value: anyValueProps[]) => void;
  setPaperData?: (value: anyValueProps[]) => void;
  setCurrentPaperItemId?: (value: number) => void;
  onGetBoxHeight?: () => void;
  thinkTexts?: string;
  schemeLoading?: boolean;
  progressTexts?: string;
  itemIndex: number;
  setExpandedIndex: (value: any) => void;
  expandedIndex: number | null;
  stopMsg?: string;
  count?: number;
}
