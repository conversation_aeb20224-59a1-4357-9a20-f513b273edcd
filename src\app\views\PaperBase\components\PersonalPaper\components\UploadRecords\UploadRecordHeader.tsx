import DynamicForm, { RefProps } from "@/components/DynamicForm";
import { recordsSearchColumns } from "./setting";
import { SearchParamProp } from "../..";
import { useAppSelector } from "@/hooks";
import { checkPermission } from "@/utils/auth";

interface UploadRecordHeaderProps {
  searchParam: SearchParamProp;
  setSearchParam: (param: SearchParamProp) => void;
}

const HEIGHT = 40;
const Root = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  width: "100%",
  height: "100%",
  background: "#fff",
  borderRadius: 16,
  padding: "0 20px 0 27px",
  minWidth: "1056px",
}));

const FormBox = styled("div")(() => ({
  width: "50%",
  height: HEIGHT,
}));

const ButtonGroup = styled("div")(() => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-end",
  width: "50%",
  height: HEIGHT,
}));

const StyleButton = styled(Button)<{ type: string }>(({ type }) => ({
  width: 60,
  height: 32,
  borderRadius: 28,
  background:
    type !== "submit"
      ? "rgba(242, 242, 242, 1)"
      : "linear-gradient(90deg, rgba(19, 108, 191, 1) 0%, rgba(38, 124, 222, 1) 100%)",
  color: type !== "submit" ? "#000" : "#fff",
  marginRight: type !== "submit" ? "10px" : "0",
}));

const UploadRecordHeader: React.FC<UploadRecordHeaderProps> = ({
  searchParam,
  setSearchParam,
}) => {
  const formRef = useRef<RefProps>(null);
  const { roleOption } = useAppSelector((state) => state.user);
  const selectColumns = useMemo(
    () => recordsSearchColumns(checkPermission(roleOption)),
    [],
  );

  const resetForm = () => {
    setSearchParam({
      status: "3",
      fuzzyQuery: "",
      ...(checkPermission(roleOption) ? { resourceCode: "1" } : {}),
    });
  };

  const submitForm = () => {
    formRef.current?.submit().then((res: any) => {
      const newSearchParam = { ...searchParam, ...(res || {}) };
      setSearchParam(newSearchParam);
    });
  };
  return (
    <Root>
      <FormBox>
        <DynamicForm
          ref={formRef}
          columns={selectColumns}
          formData={searchParam}
          labelWidth={"auto"}
          size="small"
        />
      </FormBox>
      <ButtonGroup>
        <StyleButton type="reset" onClick={resetForm}>
          重置
        </StyleButton>
        <StyleButton type="submit" onClick={submitForm}>
          查询
        </StyleButton>
      </ButtonGroup>
    </Root>
  );
};
export default UploadRecordHeader;
